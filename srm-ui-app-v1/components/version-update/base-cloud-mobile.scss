div,a,img,span,page,view,navigator,image,text,input,textarea,button,form{
	box-sizing: border-box;
}

a{
	text-decoration: none;
	color: $main;
}

form{
	display: block;
	width: 100%;
}

image{will-change: transform}

input,textarea,form{
	width: 100%;
	height: auto;
	min-height: 10px;
	display: block;
	font-size: inherit;
}

button{
	color: inherit;
	line-height: inherit;
	margin: 0;
	background-color: transparent;
	padding: 0;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	&:after{
		display: none;
	}
}

switch .uni-switch-input{
	margin-right: 0;
}
.wx-switch-input,.uni-switch-input{width:42px !important;height:22px !important;}
	.wx-switch-input::before,.uni-switch-input::before{width:40px !important;height: 20px !important;}
	.wx-switch-input::after,.uni-switch-input::after{width: 20px !important;height: 20px !important;}


/**背景颜色**/
.bg{
	background-color: $main;
	color: $mainInverse;
}
.gradualBg{
	background-image: $mainGradual;
	color: $mainGradualInverse ;
}
.grayBg{
	background-color: #f7f7f7;
	color: #30302f;
}
.whiteBg{
	background-color: #fff;
	color: #000;
}
.blackBg{
	background-color: #000;
	color: #fff;
}
.orangeBg{
	background-color: $orange;
	color: #fff;
}
.redBg{
	background-color: $red;
	color: #fff;
}
.yellowBg{
	background-color: $yellow;
	color: #000;
}
.greenBg{
	background-color: $green;
	color: #fff;
}
.brownBg{
	background-color: $brown;
	color: #fff;
}
.blueBg{
	background-color: $blue;
	color: #fff;
}
.purpleBg{
	background-color: $purple;
	color: #fff;
}

/* 文字颜色 */
.main{
	color: $main;
}
.green{
	color: $green;
}
.red{
	color: $red;
}
.yellow{
	color: $yellow;
}
.black{
	color: $black;
}
.white{
	color: $white;
}
.gray{
	color: $gray;
}
.grey{
	color: $grey;
}
.orange{
	color: $orange;
}
.brown{
	color: $brown;
}
.blue{
	color: $blue;
}
.purple{
	color: $purple;
}

.hoverMain{
	&:hover{
		color: $main;
	}
}

.hoverGreen{
	&:hover{
		color: $green;
	}
}

.hoverRed{
	&:hover{
		color: $red;
	}
}

.hoverBlue{
	&:hover{
		color: $blue;
	}
}

.hoverGray{
	&:hover{
		color: $gray;
	}
}

.hoverWhite{
	&:hover{
		color: $white;
	}
}

.hoverBlack{
	&:hover{
		color: $black;
	}
}

.hoverOrange{
	&:hover{
		color: $orange;
	}
}

.hoverYellow{
	&:hover{
		color: $yellow;
	}
}

.hoverBrown{
	&:hover{
		color: $brown;
	}
}

.hoverPurple{
	&:hover{
		color: $purple;
	}
}

/* 宽度 高度 */
$w:0;
@while $w <= 500 {
	@if $w <= 100 {
		.w#{$w}p{
			width: $w*1%;
		}
		.h#{$w}p{
			height: $w*1%;
		}
		@if $w == 100 {
			.100p{
				width: 100%;
				height: 100%;
			}
			.ww{
				width: 100vw;
			}
			.hh{
				height: 100vh;
			}
		}
	}
	.w#{$w}{
		width: $w*2upx;
	}
	.h#{$w}{
		height: $w*2upx;
	}
	@if $w < 10 {
		$w : $w + 1 ;
	} @else{
		$w : $w + 5 ;
	}
}


/* 字号 */
@for $fz from 12 through 100 {
	.fz#{$fz}{
		font-size: $fz*2upx !important;
	}
}

/* 边距 - 覆盖顺序是小的尺寸覆盖大的尺寸 少的方向覆盖多的方向 */
$s : 0 ;
@while $s <= 500 {
	.pd#{$s}{
		padding: $s*2upx!important;
	}
	.m#{$s}{
		margin: $s*2upx!important;
	}
	@if $s == 15 {
		.pd{
			padding: 30upx!important;
		}
		.m{
			margin: 30upx!important;
		}
	}
	@if $s < 10 {
		$s : $s + 1 ;
	} @else if($s < 100){
		$s : $s + 5 ;
	}   @else if($s < 300){
		$s : $s + 10 ;
	} @else{
		$s : $s + 50 ;
	}
}

$s : 0 ;
@while $s <= 500 {
	.ptb#{$s}{
		padding-top: $s*2upx!important;
		padding-bottom: $s*2upx!important;
	}
	.plr#{$s}{
		padding-left: $s*2upx!important;
		padding-right: $s*2upx!important;
	}
	.mtb#{$s}{
		margin-top: $s*2upx!important;
		margin-bottom: $s*2upx!important;
	}
	.mlr#{$s}{
		margin-left: $s*2upx!important;
		margin-right: $s*2upx!important;
	}
	@if $s == 15 {
		.ptb{
			padding-top: 30upx!important;
			padding-bottom: 30upx!important;
		}
		.plr{
			padding-left: 30upx!important;
			padding-right: 30upx!important;
		}
		
		.mlr{
			margin-left: 30upx!important;
			margin-right: 30upx!important;
		}
		.mtb{
			margin-top: 30upx!important;
			margin-bottom: 30upx!important;
		}
	}
	@if $s < 10 {
		$s : $s + 1 ;
	} @else if($s < 100){
		$s : $s + 5 ;
	}   @else if($s < 300){
		$s : $s + 10 ;
	} @else{
		$s : $s + 50 ;
	}
}

$s : 0 ;
@while $s <= 500 {
	.pl#{$s}{
		padding-left: $s*2upx!important;
	}
	.pr#{$s}{
		padding-right: $s*2upx!important;
	}
	.pt#{$s}{
		padding-top: $s*2upx!important;
	}
	.pb#{$s}{
		padding-bottom: $s*2upx!important;
	}
	.ml#{$s}{
		margin-left: $s*2upx!important;
	}
	.mr#{$s}{
		margin-right: $s*2upx!important;
	}
	.mt#{$s}{
		margin-top: $s*2upx!important;
	}
	.mb#{$s}{
		margin-bottom: $s*2upx!important;
	}
	@if $s == 15 {
		.pt{
			padding-top: 30upx!important;
		}
		.pb{
			padding-bottom: 30upx!important;
		}
		.pl{
			padding-left: 30upx!important;
		}
		.pr{
			padding-right: 30upx!important;
		}
		.mt{
			margin-top: 30upx!important;
		}
		.mr{
			margin-right: 30upx!important;
		}
		.ml{
			margin-left: 30upx!important;
		}
		.mb{
			margin-bottom: 30upx!important;
		}
	}
	@if $s < 10 {
		$s : $s + 1 ;
	} @else if($s < 100){
		$s : $s + 5 ;
	} @else if($s < 300){
		$s : $s + 10 ;
	} @else{
		$s : $s + 50 ;
	}
}



/* 文字溢出隐藏 */
.clip{
	width: 100%;
	display: -webkit-box;
	overflow: hidden;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	@for $i from 2 through 10{
		&.c#{$i}{
			-webkit-line-clamp: $i;
		}
	}
}

.cut{
	display: block;
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 价格 */
.price{
	font-size: inherit ;
	&:before{
		content: "￥";
		font-size: 70%;
		color: inherit;
		font-weight: normal;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif ;
	}
	.point{
		display: inline-block;
		font-size: 70%;
		font-weight: inherit;
		letter-spacing: 1px;
		color: inherit;
	}
	&.noPrefix{
		&:before{
			content: '';
		}
	}
}

/* 布局 */
.grid,.gridNoPd,.gridSmPd,.gridNoMb{
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	width: 100%;
	padding: 20upx 20upx 0 20upx;
	>.item,>image,>view,>navigator,>text,>button{
		width: 50%;
		padding: 0 10upx;
		margin-bottom: 20upx;
	}
	@for $i from 1 through 50{
		&.g#{$i}{
			>.item,>image,>view,>navigator,>text,>button{
				width: 100%/$i;
			}
		}
	}
}

.gridNoMb{
	>.item,>image,>view,>navigator,>text,>button{
		margin-bottom: 0;
	}
}

.gridNoPd{
	padding: 0;
	>.item,>image,>view,>navigator,>text,>button{
		padding: 0;
		margin-bottom: 0;
	}
}
.gridSmPd{
	padding: 0;
	>.item,>image,>view,>navigator,>text,>button{
		padding-right: 0;
		&:first-child{
			padding-left: 0;
			padding-right: 10upx;
		}
	}
}

/* flex布局 */
.flex{
	display: flex;
	align-items: center;
	justify-content: space-between;
	&.t{
		align-items: flex-start;
	}
	&.b{
		align-items: flex-end;
	}
	&.cv{ //垂直方向铺满
		align-items: stretch;
	}
	&.bk{ //水平方向铺满
		flex-direction: column;
	}
	&.lt{
		justify-content: flex-start;
	}
	&.ct{
		justify-content: center;
	}
	&.rt{
		justify-content: flex-end;
	}
	&.ar{
		justify-content: space-around;
	}
	&.av{
		>.item,view,button,navigator,image,text{
			flex:1;
		}
	}
}

/* 定位布局 */
.father{
	position: relative;
}
.abs,.fixed{
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
	&image{
		width: 100%;
		height: 100%;
	}
	&.top{
		bottom: auto;
	}
	&.bottom{
		top: auto;
	}
	&.left{
		right: auto;
	}
	&.right{
		left: auto;
	}
}
@for $i from 0 through 20 {
	.z#{$i}{
		z-index: $i !important;
	}
}

@for $i from 1 through 200 {
	.top-#{$i}{
		bottom: auto;
		top: $i * -2upx;
	}
	.left-#{$i}{
		right: auto;
		left: $i *  -2upx;
	}
	.bottom-#{$i}{
		top: auto;
		bottom: $i *  -2upx;
	}
	.right-#{$i}{
		left: auto;
		right: $i *  -2upx;
	}
	.top#{$i}{
		bottom: auto;
		top: $i * 2upx;
	}
	.left#{$i}{
		right: auto;
		left: $i *  2upx;
	}
	.bottom#{$i}{
		top: auto;
		bottom: $i *  2upx;
	}
	.right#{$i}{
		left: auto;
		right: $i *  2upx;
	}
	.top-#{$i}p{
		bottom: auto;
		top: $i * -1%;
	}
	.left-#{$i}p{
		right: auto;
		left: $i * -1%;
	}
	.bottom-#{$i}p{
		top: auto;
		bottom: $i * -1%;
	}
	.right-#{$i}p{
		left: auto;
		right: $i * -1%;
	}
	.top#{$i}p{
		bottom: auto;
		top: $i * 1%;
	}
	.left#{$i}p{
		right: auto;
		left: $i * 1%;
	}
	.bottom#{$i}p{
		top: auto;
		bottom: $i * 1%;
	}
	.right#{$i}p{
		left: auto;
		right: $i * 1%;
	}
}

.fixed{
	position: fixed;
}

/* fix-auto布局 */
.fixAuto,.fixAutoNoPd,.fixAutoSmPd{
	display: table;
	width: 100%;
	padding: 20upx 10upx;
	>.item,>view,>image,>navigator,>text,>button{
		vertical-align: top;
		padding: 0 10upx;
		display: table-cell ;
	}
	&.middle{
		>.item,>view,>image,>navigator,>text{
			vertical-align: middle;
		}
	}
	&.bottom{
		>.item,>view,>image,>navigator,>text{
			vertical-align: bottom;
		}
	}
}
.fixAutoSmPd{
	padding: 0;
	>.item,>view,>image,>navigator,>text{
		padding-right: 0;
		&:first-child{
			padding-left: 0;
			padding-right: 10upx;
		}
	}
}
.fixAutoNoPd{
	padding: 0;
	>.item,>view,>image,>navigator,>text{
		padding: 0;
	}
}

/* 浮动组件 */
.clear{
	&:after{
		content: "";
		clear: both;
		height: 0;
		display: block;
		visibility: hidden;
	}
}
.fl{
	float: left;
}
.fr{
	float: right;
}

/* 按钮样式类 */
.btn,.roundBtn{
	cursor: pointer;
	display: inline-block;
	text-align: center;
	padding: 8upx 24upx;
	background-color: $main;
	color: $mainInverse ;
	font-size: 28upx;
	border: 1px solid $main;
	-webkit-border-radius: 8upx;
	-moz-border-radius: 8upx;
	border-radius: 8upx;
	transition: 0.4s;
	white-space: nowrap;
	text-overflow: ellipsis;
	&.gradualBg{
		border-color: transparent;
		background-image: $mainGradual;
		color: $mainGradualInverse;
		
	}
	&.blackBg{
		background-color: $black;
		border-color: $black;
		color: #fff;
		&.shadow{
			box-shadow: 0px 2px 9px -1px rgba($black , 0.4);
		}
	}
	&.greenBg{
		background-color: $green;
		border-color: $green;
		color: #fff;
		&.shadow{
			box-shadow: 0px 2px 9px -1px rgba($green , 0.4);
		}
	}
	&.grayBg{
		border-color: rgba(#30302f,0.2);
		background-color: #f7f7f7;
		color: #30302f;
		&.shadow{
			box-shadow: 0px 2px 9px -1px rgba( #30302f , 0.2);
		}
	}
	&.whiteBg{
		border-color: rgba(#fff,0.2);
		background-color: #fff;
		color: #000;
	}
	
	&.orangeBg{
		border-color: $orange;
		background-color: $orange;
		color: #fff;
		&.shadow{
			box-shadow: 0px 2px 9px -1px rgba( $orange , 0.4);
		}
	}
	&.redBg{
		border-color: $red;
		background-color: $red;
		color: #fff;
		&.shadow{
			box-shadow: 0px 2px 9px -1px rgba( $red , 0.4);
		}
	}
	&.yellowBg{
		border-color: $yellow;
		background-color: $yellow;
		color: #000;
		&.shadow{
			box-shadow: 0px 2px 9px -1px rgba( $yellow , 0.4);
		}
	}
	
	&.line{
		background-color: transparent;
		background-image: none;
		color: $main;
		&.blackBg{
			color: $black;
		}
		&.greenBg{
			color: $green;
		}
		&.yellowBg{
			color: $yellow;
		}
		&.grayBg{
			color: #30302f;
		}
		&.whiteBg{
			border-color:  rgba(#fff,0.7);
			color: #fff;
		}
		&.orangeBg{
			color: $orange;
		}
		&.redBg{
			color: $red;
		}
	}
	&+.btn,&+.roundBtn{
		margin-left: 20upx;
	}
	&.block{
	   margin: 0;
	   padding: 20upx 24upx;
	   display: block;
	   width: 100%;
	   &+.btn{
		   margin-left: 0;
	   }
	}
	&:hover{
		-webkit-transform: scale(0.99);
		-moz-transform: scale(0.99);
		-ms-transform: scale(0.99);
		-o-transform: scale(0.99);
		transform: scale(0.99);
		opacity: 0.8;
	}
	&.disabled{
		-webkit-transform: scale(1);
		-moz-transform: scale(1);
		-ms-transform: scale(1);
		-o-transform: scale(1);
		transform: scale(1);
		opacity: 0.4;
		cursor: not-allowed;
	}
}

[class^="tag"] , [class*=" tag"]{
	display: inline-block;
	font-size: 24upx;
	padding: 4upx 14upx;
	border-radius: 4upx;
	margin-right: 6upx;
	margin-left: 6upx;
}
.tag{
	background-color: rgba($main,0.2);
	color: $main;
}
.tagBlue{
	background-color: rgba($blue,0.2);
	color: $blue;
}
.tagGray{
	background-color: rgba($gray,0.2);
	color: $gray;
}

.tagGradual{
	background-image: linear-gradient(to top right,rgba($main,0.2),rgba($main,0.1));
	color: $main;
}

.tagBlack{
	background-color: rgba($black,0.2);
	color: $black;
}
.tagGreen{
	background-color: rgba($green,0.2);
	color: $green;
}

.tagWhite{
	background-color: rgba($white,0.2);
	color: $white;
}

.tagOrange{
	background-color: rgba($orange,0.2);
	color: $orange;
}
.tagRed{
	background-color: rgba($red,0.2);
	color: $red;
}
.tagYellow{
	background-color: rgba($yellow,0.2);
	color: $yellow;
}

/* 边线(实线、虚线) */
.bdn{
	border: none;
}
.bd{
	border: 1px solid $borderColor;
	&.dashed{
		border-style: dashed;
	}
}
.bt{
	border-top:1px solid $borderColor;
	&.dashed{
		border-top-style: dashed;
	}
}
.bb{
	border-bottom:1px solid $borderColor;
	&.dashed{
		border-bottom-style: dashed;
	}
}
.bl{
	border-left:1px solid $borderColor;
	&.dashed{
		border-left-style: dashed;
	}
}
.br{
	border-right: 1px solid $borderColor;
	&.dashed{
		border-right-style: dashed;
	}
}

$b:2;
@while $b <= 10 {
	.bd#{$b}{
		border: #{$b}px solid $borderColor;
		&.dashed{
			border-style: dashed;
		}
	}
	.bt#{$b}{
		border-top:#{$b}px solid $borderColor;
		&.dashed{
			border-top-style: dashed;
		}
	}
	.bb#{$b}{
		border-bottom:#{$b}px solid $borderColor;
		&.dashed{
			border-bottom-style: dashed;
		}
	}
	.bl#{$b}{
		border-left:#{$b}px solid $borderColor;
		&.dashed{
			border-left-style: dashed;
		}
	}
	.br#{$b}{
		border-right: #{$b}px solid $borderColor;
		&.dashed{
			border-right-style: dashed;
		}
	}
	$b : $b + 1 ;
}

/* 边线颜色 */
.mainBd{
	border-color: $main;
}
.greenBd{
	border-color:  $green;
}
.redBd{
	border-color: $red;
}
.yellowBd{
	border-color:$yellow ;
}
.blackBd{
	border-color: $black;
}
.whiteBd{
	border-color:$white ;
}
.grayBd{
	border-color:$gray;
}
.greyBd{
	border-color:$grey;
}
.orangeBd{
	border-color:$orange;
}

/* 圆角 */
.radius,.rds{
	-webkit-border-radius: 100%!important;
	-moz-border-radius: 100%!important;
	border-radius: 100%!important;
}

$r:0;
@while $r <= 50{
	.rds#{$r},&.radius#{$r}{
		-webkit-border-radius:$r*2upx!important;
		-moz-border-radius:$r*2upx!important;
		border-radius:$r*2upx!important;
	}
	$r : $r + 1;
}

.rdsTl,.radiusTopLeft{
	border-top-left-radius:100%!important;
}
.rdsTr,.radiusTopRight{
	border-top-right-radius: 100%!important;
}
.rdsBl,.radiusBottomLeft{
	border-bottom-left-radius: 100%!important;
}
.rdsBr,.radiusBottomRight{
	border-bottom-right-radius: 100%!important;
}

$r:0;
@while $r <= 50{
	.rdsTl#{$r},.radiusTopLeft#{$r}{
		border-top-left-radius: $r*2upx!important;
	}
	.rdsTr#{$r},.radiusTopRight#{$r}{
		border-top-right-radius: $r*2upx!important;
	}
	.rdsBl#{$r},.radiusBottomLeft#{$r}{
		border-bottom-left-radius: $r*2upx!important;
	}
	.rdsBr#{$r},.radiusBottomRight#{$r}{
		border-bottom-right-radius: $r*2upx!important;
	}
	$r : $r + 1;
}

/* 正方形&长方形 */
[class^="square"] , [class*=" square"]{
	width: 100%;
	position: relative;
	height: auto;
	>.item,>image,>view,>navigator,>text,>button{
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
	}
}

$p : 200 ;
@while $p > 0 {
	.square#{$p}{
		padding-top: $p*1%;
	}
	@if $p == 100 {
		.square{
			padding-top: 100%;
		}
	}
	$p : $p - 5 ;
}



/* 阴影 */
.shadow{
	box-shadow: 0px 2px 9px -1px rgba(0, 0, 0, 0.1);
}

/* 遮罩层 */
.wrapper-top,.wt{
	background-image: linear-gradient(rgba(0,0,0,0.3) , rgba(0,0,0,0.02));
}
.wrapper-bottom,.wb{
	background-image: linear-gradient( rgba(0,0,0,0.02) , rgba(0,0,0,0.3) );
}

[class^="wp"],[class*=" wp"] {
	z-index: 10;
}

/* 透明度 */
@for $op from 0 through 10 {
	.op#{$op}{
		opacity: $op * 0.1!important;
	}
	.wp#{$op}{
		background-color: rgba(#000,$op*0.1);
	}
	@if $op == 5 {
		.wp{
			background-color: rgba(#000,0.5);
		}
	}
}

/* 分割线 */
[class*=" split"],[class^="split"] {
	position: relative;
	&:before{
		content:"";
		display: block;
		position: absolute;
		left: 0;
		top: 50%;
		border-left: 1px solid $borderColor;
	}
}

$s:10;
@while $s <= 100 {
	.split#{$s}{
		&:before{
			height: #{$s*2}upx;
			margin-top: -#{$s}upx;
		}
	}
	@if $s == 10 {
		.split{
			&:before{
				height: 20upx;
				margin-top: -10upx;
			}
		}
	}
	$s:$s+2;
}

.hover,[class^="hover"],[class*=" hover"]{
	transition: all 0.4s;
	cursor: pointer;
	&:hover{
		opacity: 0.8 !important;
	}
}



.statusBar{
	height: var(--status-bar-height);
}

.winBottom{
	height: var(--windown-bottom);
}

.safeBottom{
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}

.disabled{
	opacity:0.8;
	cursor: not-allowed;
}



.grid,.gridNoMb,.gridNoPd{
	>.btn,>.roundBtn{
		&+.btn,&+.roundBtn{
			margin-left: 0 ;
		}
	}
}

.roundBtn{
	-webkit-border-radius: 100upx;
	-moz-border-radius: 100upx;
	border-radius: 100upx;
} 
 
 
 
 /* 位置 */
 .text-center,.tc{
 	text-align: center!important;
 }
 .text-left,.tl{
 	text-align: left!important;
 }
 .text-right,.tr{
 	text-align: right!important;
 }
 .text-justify,.tj{
 	text-align: justify!important;
 }
 .text-bold,.bold{
 	font-weight: bold!important;
 }
 .text-normal,.normal{
 	font-weight: normal!important;
 }
 .break{
 	white-space: normal;
 	word-break: break-all;
 }
 .noBreak{
 	white-space: nowrap;
 	word-break: keep-all;
 }
 .inline{
 	display: inline-block;
 }
 .block{
 	display: block;
 	width: 100%;
 }
 .none{
 	display: none;
 }
 .center-block{
 	margin: 0 auto;
 	display: block;
 }
 .hidden{
 	overflow: hidden;
 }
 .hiddenX{
 	overflow-x: hidden;
 }
 .hiddenY{
 	overflow-y: hidden;
 }
 .auto{
 	overflow: auto;
 }
 .autoX{
 	overflow-x: auto;
 }
 .autoY{
 	overflow-y: auto;
 }
 .showInMb{
	 display: block;
 }
 .showInPc{
	 display: none;
 }
 table{
 	width: 100%;
 	border-collapse: collapse;
 	border-spacing: 0;
 	border: 1px solid #e6e6e6;
 	thead{
 		tr{
 			background-color: #f2f2f2;
 			th{
 				color: #8799a3;
 				width: 1%;
 			}
 		}
 	}
 	tr{
 		background-color: #fff;
 		transition: all 0.4s;
 		td,th{
 			border: 1px solid #e6e6e6;
 			overflow: hidden;
 			-o-text-overflow: ellipsis;
 			text-overflow: ellipsis;
 			white-space: nowrap;
 			word-wrap: break-word;
 			padding: 5px 10px;
 			height: 28px;
 			line-height: 28px;
 			&.autoWidth{
 				width: auto;
 			}
 		}
 		&:hover{
 			background-color: #f2f2f2;
 		}
 	}
 }