{
	"easycom": {
		"^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "uni-app"
			}
		}, {
			"path": "pages/tabbar/user",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationStyle": "custom"
			}
		}, {
			"path": "pages/auth/login",
			"style": {
				"navigationBarTitleText": "登录",
				"navigationStyle":"custom",
				"app-plus": {
					"animationType": "slide-in-bottom"
				}
			}
		}, {
            "path" : "pages/set/userInfo",
            "style" : {
				"navigationBarTitleText": "个人资料"
			}
        }
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"color": "#666",
		"selectedColor": "#FF5A5F",
		"borderStyle": "black",
		"list": [{
			"text": "首页",
			"pagePath": "pages/index/index",
			"iconPath": "static/tarbar/index.png",
			"selectedIconPath": "static/tarbar/index-active.png"
		}, {
			"text": "商品",
			"pagePath": "pages/product/list",
			"iconPath": "static/tarbar/product.png",
			"selectedIconPath": "static/tarbar/product-active.png"
		}, {
			"text": "我的",
			"pagePath": "pages/tabbar/user",
			"iconPath": "static/tarbar/ucenter.png",
			"selectedIconPath": "static/tarbar/ucenter-active.png"
		}]
	}
}
