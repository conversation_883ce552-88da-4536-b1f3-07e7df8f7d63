/* #ifndef APP-PLUS-NVUE */
view,
scroll-view,
swiper,
swiper-item,
cover-view,
cover-image,
icon,
text,
rich-text,
progress,
button,
checkbox,
form,
input,
label,
radio,
slider,
switch,
textarea,
navigator,
audio,
camera,
image,
video {
	box-sizing: border-box;
}
image{
	display: block;
}
text{
	line-height: 1;
	/* font-family: Helvetica Neue, Helvetica, sans-serif; */
}
button{
	padding: 0;
	margin: 0;
	background-color: rgba(0,0,0,0) !important;
}
button:after{
	border: 0;
}
.bottom-fill{
	height: constant(safe-area-inset-bottom);
	height: env(safe-area-inset-bottom); 
}
.fix-bot{
	box-sizing: content-box;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom); 
}

/* 边框 */
.round{
	position: relative;
	border-radius: 100rpx;
}
.round:after{
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 200%;
	height: 200%;
	transform: scale(.5) translate(-50%,-50%);
	border: 1px solid #878787;
	border-radius: 100rpx;
	box-sizing: border-box;
}
.b-b:after{
	position: absolute;
	z-index: 3;
	left: 0;
	top: auto;
	bottom: 0;
	right: 0;
	height: 0;
	content: '';
	transform: scaleY(.5);
	border-bottom: 1px solid #e0e0e0;
}
.b-t:before{
	position: absolute;
	z-index: 3;
	left: 0;
	top: 0;
	right: 0;
	height: 0;
	content: '';
	transform: scaleY(.5);
	border-bottom: 1px solid #e5e5e5;
}
.b-r:after{
	position: absolute;
	z-index: 3;
	right: 0;
	top: 0;
	bottom: 0;
	width: 0;
	content: '';
	transform: scaleX(.5);
	border-right: 1px solid #e5e5e5;
}
.b-l:before{
	position: absolute;
	z-index: 3;
	left: 0;
	top: 0;
	bottom: 0;
	width: 0;
	content: '';
	transform: scaleX(.5);
	border-left: 1px solid #e5e5e5;
}
.b-b, .b-t, .b-l, .b-r{
	position: relative;
}
/* 点击态 */
.hover-gray {
	background: #fafafa !important;
}
.hover-dark {
	background: #f0f0f0 !important;
}

.hover-opacity {
	opacity: 0.7;
}

/* #endif */

.clamp {
	/* #ifdef APP-PLUS-NVUE */
	lines: 1;
	/* #endif */
	/* #ifndef APP-PLUS-NVUE */
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	display: block;
	/* #endif */
}
.clamp2 {
	/* #ifdef APP-PLUS-NVUE */
	lines: 2;
	/* #endif */
	/* #ifndef APP-PLUS-NVUE */
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	/* #endif */
}

/* 布局 */
.row{
	/* #ifndef APP-PLUS-NVUE */
	display:flex;
	/* #endif */
	flex-direction:row;
	align-items: center;
}
.column{
	/* #ifndef APP-PLUS-NVUE */
	display:flex;
	/* #endif */
	flex-direction: column;
}
.center{
	/* #ifndef APP-PLUS-NVUE */
	display:flex;
	/* #endif */
	align-items: center;
	justify-content: center;
}
.fill{
	flex: 1;
}
/* input */
.placeholder{
	color: #999 !important;
}