const fs = require('fs');
const path = require('path');

// 剩余需要修改的文件和对应的行号
const remainingFiles = [
  { file: 'src/views/ledger/index.vue', line: 434 },
  { file: 'src/views/system/license/index.vue', line: 136 },
  { file: 'src/views/order/evaluateRule/index.vue', line: 171 },
  { file: 'src/views/order/runHead/index.vue', line: 229 },
  { file: 'src/views/paymentCycle/category.vue', line: 321 },
  { file: 'src/views/paymentCycle/standard.vue', line: 786 },
  { file: 'src/views/paymentCycle/supplier.vue', line: 311 },
  { file: 'src/views/scar/scarFile.vue', line: 110 },
  { file: 'src/views/scar/defectType/index.vue', line: 311 },
  { file: 'src/views/scar/recordTypeUserRelConfig/index.vue', line: 239 },
  { file: 'src/views/sa/saFile.vue', line: 61 },
  { file: 'src/views/supplier/userRel/index.vue', line: 212 },
  { file: 'src/views/system/upload.vue', line: 64 },
  { file: 'src/views/system/user/info.vue', line: 41 }
];

function fixFile(filePath) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`文件不存在: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(fullPath, 'utf8');
  let modified = false;

  // 替换直接使用 process.env.VUE_APP_BASE_API 的地方（排除注释行）
  const lines = content.split('\n');
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    // 跳过注释行
    if (line.trim().startsWith('//') || line.trim().startsWith('*') || line.includes('// ')) {
      continue;
    }
    
    // 替换直接使用的 process.env.VUE_APP_BASE_API
    if (line.includes('process.env.VUE_APP_BASE_API') && !line.includes('getConfig(')) {
      lines[i] = line.replace(/process\.env\.VUE_APP_BASE_API/g, "getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API)");
      modified = true;
    }
  }
  
  if (modified) {
    content = lines.join('\n');
  }

  // 检查是否已经导入了 getConfig
  const hasGetConfigImport = /import.*getConfig.*from.*@\/utils\/config/.test(content);
  
  if (modified && !hasGetConfigImport) {
    // 查找合适的位置添加导入
    const scriptMatch = content.match(/<script[^>]*>/);
    if (scriptMatch) {
      const scriptIndex = scriptMatch.index + scriptMatch[0].length;
      const beforeScript = content.substring(0, scriptIndex);
      const afterScript = content.substring(scriptIndex);
      
      // 查找第一个 import 语句的位置
      const firstImportMatch = afterScript.match(/\nimport\s+/);
      if (firstImportMatch) {
        const importIndex = scriptIndex + firstImportMatch.index;
        const beforeImport = content.substring(0, importIndex);
        const afterImport = content.substring(importIndex);
        
        content = beforeImport + "\nimport { getConfig } from '@/utils/config'" + afterImport;
      } else {
        // 如果没有找到 import 语句，在 script 标签后添加
        content = beforeScript + "\nimport { getConfig } from '@/utils/config'\n" + afterScript;
      }
    }
  }

  if (modified) {
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`✅ 已修改: ${filePath}`);
    return true;
  } else {
    console.log(`⏭️  无需修改: ${filePath}`);
    return false;
  }
}

console.log('开始批量修改剩余文件...\n');

let successCount = 0;
let totalCount = remainingFiles.length;

remainingFiles.forEach(({ file }) => {
  try {
    if (fixFile(file)) {
      successCount++;
    }
  } catch (error) {
    console.error(`❌ 修改失败 ${file}:`, error.message);
  }
});

console.log(`\n批量修改完成！成功修改 ${successCount}/${totalCount} 个文件`);
