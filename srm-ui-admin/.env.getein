NODE_ENV = production

BABEL_ENV = production
#在非production配置下必须填写，否则路由懒加载会失效！！！
#在非production配置下必须填写，否则路由懒加载会失效！！！
#在非production配置下必须填写，否则路由懒加载会失效！！！

# 页面标题
VUE_APP_TITLE = 基蛋生物 SRM-PRO 测试环境

# 测试环境配置
ENV = 'staging'
#跨页全选的数量限制
VUE_APP_SELECT_ALL_COUNT = 1000
# SRM 2.0/测试环境
#VUE_APP_BASE_API = 'http://test.esicint.com'
#VUE_APP_BASE_API = 'http://192.168.200.42:8016'
VUE_APP_BASE_API = 'http://223.111.224.209:1008'
# 静态资源地址
#PUBLIC_PATH = 'http://221.224.111.170:8015'
# 水印
VUE_APP_WATER_MARK='基蛋生物'
# 多租户的开关
VUE_APP_TENANT_ENABLE = true
#附件大小限制 MB
VUE_APP_FILESIZE = 100
# 文档的开关
VUE_APP_DOC_ENABLE = false

# 百度统计
VUE_APP_BAIDU_CODE = fadc1bd5db1a1d6f581df60a1807f8ab
# superset某个仪表板的key
VUE_APP_SOME_DASHBOARD = 86b48c4c-58d9-4ac0-84ad-fce8cd9bdc41

# superset地址
VUE_APP_SUPERSET_URL=http://223.111.224.209:51005
