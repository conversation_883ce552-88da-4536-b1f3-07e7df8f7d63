const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 动态查找所有需要修改的文件
function findFilesToFix() {
  try {
    const command = `Get-ChildItem -Path "src" -Recurse -Include "*.js","*.vue","*.ts" | Select-String -Pattern "process\\.env\\.VUE_APP_BASE_API(?!\\s*\\|\\|)" | Where-Object { $_.Line -notmatch "getConfig\\(" } | Select-Object -ExpandProperty Path | Get-Unique`;
    const result = execSync(command, { encoding: 'utf8', shell: 'powershell' });

    return result.trim().split('\n')
      .filter(line => line.trim())
      .map(line => line.trim().replace(/^.*srm-ui-admin[\\\/]/, '').replace(/\\/g, '/'));
  } catch (error) {
    console.error('查找文件失败，使用预定义列表');
    // 备用文件列表
    return [
      'src/views/ledger/recordInfo/index.vue',
      'src/views/ledger/recordInfo/uploadItem.vue',
      'src/views/order/deliveryConfirmation/index.vue',
      'src/views/order/deliveryConfirmationSupplier/index.vue',
      'src/views/order/evaluateRule/index.vue',
      'src/views/order/runHead/index.vue',
      'src/views/paymentCycle/category.vue',
      'src/views/paymentCycle/standard.vue',
      'src/views/paymentCycle/supplier.vue',
      'src/views/scar/scarFile.vue',
      'src/views/scar/defectType/index.vue',
      'src/views/scar/recordTypeUserRelConfig/index.vue',
      'src/views/sa/saFile.vue',
      'src/views/supplier/userRel/index.vue',
      'src/views/system/upload.vue',
      'src/views/system/user/info.vue',
      'src/views/system/user/userAvatar.vue',
      'src/views/system/user/userSeal.vue'
    ];
  }
}

function fixFile(filePath) {
  const fullPath = path.join(__dirname, filePath);

  if (!fs.existsSync(fullPath)) {
    console.log(`文件不存在: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(fullPath, 'utf8');
  let modified = false;

  // 替换直接使用 process.env.VUE_APP_BASE_API 的地方
  const regex = /process\.env\.VUE_APP_BASE_API(?!\s*\|\|)/g;
  if (regex.test(content)) {
    content = content.replace(regex, "getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API)");
    modified = true;
  }

  // 检查是否已经导入了 getConfig
  const hasGetConfigImport = /import.*getConfig.*from.*@\/utils\/config/.test(content);

  if (modified && !hasGetConfigImport) {
    // 查找合适的位置添加导入
    const scriptMatch = content.match(/<script[^>]*>/);
    if (scriptMatch) {
      const scriptIndex = scriptMatch.index + scriptMatch[0].length;
      const beforeScript = content.substring(0, scriptIndex);
      const afterScript = content.substring(scriptIndex);

      // 查找第一个 import 语句的位置
      const firstImportMatch = afterScript.match(/\nimport\s+/);
      if (firstImportMatch) {
        const importIndex = scriptIndex + firstImportMatch.index;
        const beforeImport = content.substring(0, importIndex);
        const afterImport = content.substring(importIndex);

        content = beforeImport + "\nimport { getConfig } from '@/utils/config'" + afterImport;
      } else {
        // 如果没有找到 import 语句，在 script 标签后添加
        content = beforeScript + "\nimport { getConfig } from '@/utils/config'\n" + afterScript;
      }
    }
  }

  if (modified) {
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`✅ 已修改: ${filePath}`);
  } else {
    console.log(`⏭️  无需修改: ${filePath}`);
  }
}

console.log('开始批量修改文件...\n');

const filesToFix = findFilesToFix();
console.log(`找到 ${filesToFix.length} 个需要修改的文件\n`);

filesToFix.forEach(filePath => {
  try {
    fixFile(filePath);
  } catch (error) {
    console.error(`❌ 修改失败 ${filePath}:`, error.message);
  }
});

console.log('\n批量修改完成！');
