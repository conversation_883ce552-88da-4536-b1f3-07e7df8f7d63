import router, { mainIndex } from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getAccessToken, setToken } from '@/utils/auth'
import { isRelogin } from '@/utils/request'
import i18n, { getLanguage } from '@/lang/index'
import zhCN from 'vxe-table/lib/locale/lang/zh-CN'
import enUS from 'vxe-table/lib/locale/lang/en-US'
import elementEnLocale from 'element-ui/lib/locale/lang/en' // element-ui lang
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN' // element-ui lang

import { getTranslationLabelList } from '@/api/system/translationLabel'

NProgress.configure({ showSpinner: false })

// 增加三方登陆 update by 芋艿
const whiteList = ['/login', '/forgetPassWord', '/social-login', '/auth-redirect', '/bind', '/register', '/oauthLogin/gitee', '/license/index', '/xr/demo', '/filePreview', '/previewReport']

router.beforeEach(async(to, from, next) => {
  NProgress.start()
  try {
    if (JSON.stringify(i18n.messages).length < 50000) {
      // 计算i18n的数据大小， 50000只是粗略估计 ，本地存在是31187，小于则去拉数据库的完整国际化数据
      const locale = getLanguage()
      const element = locale === 'zh' ? elementZhLocale : elementEnLocale
      const vxe = locale === 'zh' ? zhCN : enUS
      const res = await getTranslationLabelList({
        locale
      })
      i18n.setLocaleMessage(locale, {
        ...res.data,
        ...element,
        ...vxe
      })
    }
  } catch (e) {
    console.log(e)
  }
  if (to.path === '/previewReport') {
    // 直接进入文件预览页面，无需加载其他数据
    next()
    return
  }

  // /xr/demo 路由指向的是雪榕单点登录成功后回调的重定向地址，在该页面需要从query中取出token（雪榕返回的），在该页面进行处理。因此不需要走通用的token处理
  if (to.path !== '/xr/demo' && to.query.token) {
    setToken({ accessToken: to.query.token })
  }
  if (getAccessToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) {
        isRelogin.show = true
        // 获取字典数据 add by 芋艿
        try {
          await store.dispatch('dict/loadDictDatas')
        } catch (e) {
          console.log(e)
        }
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(() => {
          store.dispatch('NotifyPasswordExpiredTime')
          isRelogin.show = false
          store.dispatch('GenerateRoutes').then(accessRoutes => {
            const tempArr = []

            // 根据roles权限生成可访问的路由表
            function flattenTreeWithPath(tree, path = []) {
              const result = []

              function traverse(node, nodePath) {
                const fullPath = [...nodePath, node.path]

                if (node.children) {
                  node.children.forEach(child => traverse(child, fullPath))
                } else {
                  result.push({ ...node, path: fullPath.join('/'), children: [] })
                }
              }

              traverse(tree, path)
              return result
            }

            if (store.getters.userId === -1) {
              for (let i = 0; i < accessRoutes.length; i++) {
                tempArr.push(...flattenTreeWithPath(accessRoutes[i]))
              }
              router.addRoutes(tempArr) // 匿名供应商不要菜单及头
            } else {
              store.commit('SET_SIDEBAR_ROUTERS', [mainIndex, ...store.getters.sidebarRouters])
              router.addRoutes([
                mainIndex,
                ...accessRoutes]) // 动态添加可访问路由表
            }
            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          })
        }).catch(err => {
          store.dispatch('LogOut').then(() => {
            Message.error(err)
            next({ path: '/' })
          })
        })
      } else {
        next()
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
