<template>
  <div>
    <!-- 悬浮标记 -->
    <div
      v-show="!isChatOpen"
      ref="chatTrigger"
      class="chat-trigger shake"
      :class="{ 'dragging': dragging, 'stop': dragging }"
      title="采购精灵"
      :style="{ top: triggerPosition + 'px' }"
      @mousedown="startDrag"
      @click="handleTriggerClick"
      @touchstart="startDragTouch"
    >
      <i class="el-icon-chat-dot-round" />
    </div>
    <!-- 聊天窗口 -->
    <div
      v-show="isChatOpen"
      ref="chatWindow"
      class="chat-window"
      :class="{ 'dragging': windowDragging }"
      :style="{
        top: windowPosition.y + 'px',
        left: windowPosition.x + 'px'
      }"
    >
      <div class="chat-overlay" @click="closeChat" />
      <beautiful-chat
        :participants="participants"
        :on-message-was-sent="onMessageWasSent"
        :message-list="messageList"
        :is-open="isChatOpen"
        :close="closeChat"
        :open="openChat"
        :show-emoji="false"
        :show-file="false"
        :colors="colors"
        :disable-user-list-toggle="true"
        :always-scroll-to-bottom="true"
        :message-styling="true"
      >
        <template #header>
          <div
            class="chat-header"
            @mousedown="startWindowDrag"
            @touchstart="startWindowDragTouch"
          >
            <span>采购精灵</span>
            <el-tooltip content="新建会话" placement="bottom">
              <div class="new-chat-button" @click="startNewChat">
                <i class="el-icon-plus" />
                <span>新会话</span>
              </div>
            </el-tooltip>
          </div>
        </template>
        <template #system-message-body="{ message }">
          [System]: {{ message.text }}
        </template>
        <template #text-message-body="{ message }">
          <vue-markdown v-if="message.data.text" :key="message.data.text.length">
            {{ message.data?.text }}
          </vue-markdown>
        </template>
      </beautiful-chat>
    </div>
  </div>
</template>

<script>
import authIcon from '@/assets/images/logo.png'
import { prepareConverseRagFlow } from '@/api/agent/agent'
import { getConfig } from '@/utils/config'

export default {
  name: 'GlobalChat',
  data() {
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    const chatWidth = 370
    const chatHeight = 550
    return {
      participants: [
        {
          id: 'ai',
          name: '采购精灵',
          imageUrl: authIcon
        }
      ],
      messageList: [],
      isChatOpen: false,
      colors: {
        header: {
          bg: '#4996b8',
          text: '#ffffff'
        },
        launcher: {
          bg: '#4996b8'
        },
        messageList: {
          bg: '#ffffff'
        },
        sentMessage: {
          bg: '#4996b8',
          text: '#ffffff'
        },
        receivedMessage: {
          bg: '#eaeaea',
          text: '#222222'
        },
        userInput: {
          bg: '#f4f7f9',
          text: '#565867'
        }
      },
      isLoading: false,
      dragging: false,
      triggerPosition: 50, // 初始位置默认为 50%
      startMouseY: 0,
      startPositionY: 0,
      isDragged: false, // 标记是否处于拖动状态
      dragThreshold: 3, // 移动多少像素才算拖动
      windowDragging: false,
      windowPosition: {
        x: windowWidth - chatWidth - 25, // 默认右侧距离
        y: windowHeight - chatHeight - 25 // 默认底部距离
      },
      defaultWindowPosition: {
        x: windowWidth - chatWidth - 25,
        y: windowHeight - chatHeight - 25
      },
      startWindowPos: {
        x: 0,
        y: 0
      },
      startMousePos: {
        x: 0,
        y: 0
      },
      // 新增锚点相关数据
      anchorX: null, // 'left', 'right', or null
      anchorY: null, // 'top', 'bottom', or null
      anchorThreshold: 15 // Pixels to consider near an edge
    }
  },
  mounted() {
    window.addEventListener('keydown', this.handleShortcut)
    window.addEventListener('mousemove', this.onDrag)
    window.addEventListener('mouseup', this.stopDrag)
    window.addEventListener('touchmove', this.onDragTouch, { passive: false })
    window.addEventListener('touchend', this.stopDragTouch)
    window.addEventListener('resize', this.handleResize)
    // document.addEventListener('click', this.handleClickOutside)

    // 从本地存储中恢复位置
    const savedPosition = localStorage.getItem('chatTriggerPosition')
    if (savedPosition) {
      this.triggerPosition = parseInt(savedPosition)
    } else {
      // 如果没有保存的位置，设置为屏幕中间位置
      this.triggerPosition = window.innerHeight / 2
    }

    // 从本地存储中恢复聊天窗口位置
    const savedWindowPosition = localStorage.getItem('chatWindowPosition')
    if (savedWindowPosition) {
      this.windowPosition = JSON.parse(savedWindowPosition)
    }

    // 加载锚点信息
    const savedAnchorX = localStorage.getItem('chatAnchorX')
    const savedAnchorY = localStorage.getItem('chatAnchorY')
    // localStorage存的是空字符串代表null
    this.anchorX = savedAnchorX === '' ? null : (savedAnchorX || 'right')
    this.anchorY = savedAnchorY === '' ? null : (savedAnchorY || 'bottom')

    // 组件加载后，根据当前窗口大小和锚点调整一次位置
    // 延迟执行确保 chatWindow ref 可用
    this.$nextTick(() => {
      this.handleResize()
    })
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.handleShortcut)
    window.removeEventListener('mousemove', this.onDrag)
    window.removeEventListener('mouseup', this.stopDrag)
    window.removeEventListener('touchmove', this.onDragTouch)
    window.removeEventListener('touchend', this.stopDragTouch)
    window.removeEventListener('resize', this.handleResize)
    // document.removeEventListener('click', this.handleClickOutside)
  },
  methods: {
    async converse(message) {
      try {
        this.messageList = [...this.messageList, message]

        const aiMessage = {
          author: 'ai',
          type: 'text',
          data: {
            text: ''
          }
        }
        this.messageList = [...this.messageList, aiMessage]

        const aiSession = JSON.parse(localStorage.getItem('ai_session'))
        const token = localStorage.getItem('ACCESS_TOKEN')
        const response = await fetch(getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/ov/ai-agent/converse-ragFlow', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: token ? `Bearer ${token}` : '',
            'tenant-id': localStorage.getItem('tenantId') || ''
          },
          body: JSON.stringify({
            message: message.data.text,
            sessionId: aiSession.chatId
          })
        })

        const reader = response.body.getReader()
        const decoder = new TextDecoder()

        while (true) {
          const { value, done } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          // 处理返回的数据
          const lines = chunk.split('data:')
          for (const line of lines) {
            if (line.trim()) {
              try {
                const jsonData = JSON.parse(line)
                if (jsonData.data?.data?.answer) {
                  // 过滤掉包含 "is running..." 的状态消息
                  if (!jsonData.data.data.answer.includes('is running...')) {
                    aiMessage.data.text = jsonData.data.data.answer
                    this.messageList = [...this.messageList.slice(0, -1), aiMessage]
                  }
                }
              } catch (e) {
                console.error('Parse chunk error:', e)
              }
            }
          }
        }
      } catch (error) {
        console.error('Stream error:', error)
        this.messageList = [...this.messageList, {
          author: 'ai',
          type: 'text',
          data: {
            text: '抱歉，发生了一些错误，请稍后重试。'
          }
        }]
      }
    },
    async prepareConverse() {
      try {
        prepareConverseRagFlow().then(res => {
          const aiSession = JSON.parse(localStorage.getItem('ai_session'))
          if (!aiSession) {
            localStorage.setItem('ai_session', JSON.stringify(res.data))
            const parsedPrologue = res.data.prologue
            const welcomeMessage = {
              author: 'ai',
              type: 'text',
              data: {
                text: parsedPrologue
              }
            }
            this.messageList = [welcomeMessage]
          } else {
            if (this.messageList.length <= 0) {
              const parsedPrologue = aiSession.prologue
              const welcomeMessage = {
                author: 'ai',
                type: 'text',
                data: {
                  text: parsedPrologue
                }
              }
              this.messageList = [welcomeMessage]
            }
          }
        })
      } catch (error) {
        // ... error handling
      }
    },
    toggleChat() {
      console.log('toggleChat called, current state:', this.isChatOpen)
      this.isChatOpen = !this.isChatOpen
      if (this.isChatOpen) {
        // 打开聊天窗口时，使用默认位置
        this.windowPosition = {
          x: this.defaultWindowPosition.x,
          y: this.defaultWindowPosition.y
        }
      }
      this.prepareConverse()
      console.log('new state:', this.isChatOpen)
    },
    openChat() {
      this.isChatOpen = true
    },
    closeChat() {
      this.isChatOpen = false
    },
    onMessageWasSent(message) {
      this.converse(message)
    },
    handleShortcut(event) {
      if ((event.ctrlKey || event.metaKey) && event.key === '/') {
        event.preventDefault()
        this.isChatOpen ? this.closeChat() : this.toggleChat()
      }
    },
    handleClickOutside(event) {
      if (this.isChatOpen && !event.target.closest('.sc-chat-window') && !event.target.closest('.chat-trigger')) {
        this.closeChat()
      }
    },
    // 处理点击事件
    handleTriggerClick(event) {
      if (!this.isDragged) {
        this.toggleChat()
      }
      // 避免重复重置
      // this.isDragged = false
    },

    // 开始拖动
    startDrag(event) {
      // 阻止默认行为和冒泡
      event.preventDefault()
      event.stopPropagation()

      this.dragging = true
      this.startMouseY = event.clientY
      this.startPositionY = this.triggerPosition

      // 添加拖动时的样式类
      if (this.$refs.chatTrigger) {
        this.$refs.chatTrigger.style.transition = 'none'
      }
    },

    // 触摸开始拖动
    startDragTouch(event) {
      // 阻止默认行为和冒泡
      event.preventDefault()
      event.stopPropagation()

      this.dragging = true
      this.startMouseY = event.touches[0].clientY
      this.startPositionY = this.triggerPosition

      // 添加拖动时的样式类
      if (this.$refs.chatTrigger) {
        this.$refs.chatTrigger.style.transition = 'none'
      }
    },

    // 拖动中
    onDrag(event) {
      if (!this.dragging) return

      // 计算移动距离
      const moveY = event.clientY - this.startMouseY

      // 如果移动距离超过阈值，标记为拖动状态
      if (Math.abs(moveY) > this.dragThreshold) {
        this.isDragged = true
      }

      // 计算新位置（不进行四舍五入，保持精确位置）
      let newPosition = this.startPositionY + moveY

      // 限制边界
      const windowHeight = window.innerHeight
      const triggerHeight = this.$refs.chatTrigger ? this.$refs.chatTrigger.offsetHeight : 42

      // 确保不会超出屏幕上下边界
      newPosition = Math.max(triggerHeight / 2, Math.min(windowHeight - triggerHeight / 2, newPosition))

      // 直接更新位置，不进行额外处理
      this.triggerPosition = newPosition

      // 阻止默认行为，防止页面滚动
      event.preventDefault()
    },

    // 触摸拖动中
    onDragTouch(event) {
      if (!this.dragging) return

      // 计算移动距离
      const moveY = event.touches[0].clientY - this.startMouseY

      // 如果移动距离超过阈值，标记为拖动状态
      if (Math.abs(moveY) > this.dragThreshold) {
        this.isDragged = true
      }

      // 计算新位置
      let newPosition = this.startPositionY + moveY

      // 限制边界
      const windowHeight = window.innerHeight
      const triggerHeight = this.$refs.chatTrigger ? this.$refs.chatTrigger.offsetHeight : 42

      // 确保不会超出屏幕上下边界
      newPosition = Math.max(triggerHeight / 2, Math.min(windowHeight - triggerHeight / 2, newPosition))

      // 直接更新位置
      this.triggerPosition = newPosition

      // 阻止默认行为，防止页面滚动
      event.preventDefault()
    },

    // 停止拖动
    stopDrag() {
      if (this.dragging) {
        this.dragging = false

        // 恢复过渡效果
        if (this.$refs.chatTrigger) {
          this.$refs.chatTrigger.style.transition = ''
        }

        // 保存位置到本地存储
        localStorage.setItem('chatTriggerPosition', this.triggerPosition.toString())

        // 判断是否为微小移动（非实际拖动）
        if (Math.abs(this.triggerPosition - this.startPositionY) < this.dragThreshold) {
          this.isDragged = false
        }

        // 添加一个小延迟后重置拖动状态，确保点击事件被正确处理
        setTimeout(() => {
          this.isDragged = false
        }, 100)
      }
    },

    // 触摸停止拖动
    stopDragTouch() {
      this.stopDrag()
    },

    async startNewChat() {
      // Clear local storage
      localStorage.removeItem('ai_session')
      // Clear message list
      this.messageList = []
      // Start new chat session
      await this.prepareConverse()
    },

    startWindowDrag(event) {
      if (event.target.closest('.new-chat-button')) return

      event.preventDefault()
      event.stopPropagation()

      const chatWindow = this.$refs.chatWindow
      const rect = chatWindow.getBoundingClientRect()

      // 记录鼠标在窗口内的相对位置
      this.startMousePos = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      }

      // 记录窗口当前位置
      this.startWindowPos = {
        x: this.windowPosition.x,
        y: this.windowPosition.y
      }

      // 设置拖动状态
      this.windowDragging = true

      // 添加全局事件监听
      window.addEventListener('mousemove', this.onWindowDrag)
      window.addEventListener('mouseup', this.stopWindowDrag)
    },

    onWindowDrag(event) {
      if (!this.windowDragging) return

      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight
      const chatWidth = 370
      const chatHeight = 550

      // 根据鼠标位置和初始偏移计算新位置
      let newX = event.clientX - this.startMousePos.x
      let newY = event.clientY - this.startMousePos.y

      // 限制边界
      newX = Math.max(0, Math.min(windowWidth - chatWidth, newX))
      newY = Math.max(0, Math.min(windowHeight - chatHeight, newY))

      this.windowPosition = {
        x: newX,
        y: newY
      }
    },

    startWindowDragTouch(event) {
      if (event.target.closest('.new-chat-button')) return

      event.preventDefault()
      event.stopPropagation()

      const chatWindow = this.$refs.chatWindow
      const rect = chatWindow.getBoundingClientRect()
      const touch = event.touches[0]

      this.startMousePos = {
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top
      }

      this.startWindowPos = {
        x: this.windowPosition.x,
        y: this.windowPosition.y
      }

      this.windowDragging = true

      window.addEventListener('touchmove', this.onWindowDragTouch)
      window.addEventListener('touchend', this.stopWindowDragTouch)
    },

    onWindowDragTouch(event) {
      if (!this.windowDragging) return

      const touch = event.touches[0]
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight
      const chatWidth = 370
      const chatHeight = 550

      let newX = touch.clientX - this.startMousePos.x
      let newY = touch.clientY - this.startMousePos.y

      newX = Math.max(0, Math.min(windowWidth - chatWidth, newX))
      newY = Math.max(0, Math.min(windowHeight - chatHeight, newY))

      this.windowPosition = {
        x: newX,
        y: newY
      }

      event.preventDefault()
    },

    stopWindowDrag() {
      if (this.windowDragging) {
        this.windowDragging = false
        window.removeEventListener('mousemove', this.onWindowDrag)
        window.removeEventListener('mouseup', this.stopWindowDrag)

        // --- Calculate and save anchors ---
        const windowWidth = window.innerWidth
        const windowHeight = window.innerHeight
        const chatWindow = this.$refs.chatWindow
        const chatWidth = chatWindow ? chatWindow.offsetWidth : 370
        const chatHeight = chatWindow ? chatWindow.offsetHeight : 550
        const finalX = this.windowPosition.x
        const finalY = this.windowPosition.y
        const threshold = this.anchorThreshold

        if (chatWidth > 0 && chatHeight > 0) { // Only calculate if dimensions are valid
          const isNearRight = windowWidth - finalX - chatWidth < threshold
          const isNearBottom = windowHeight - finalY - chatHeight < threshold
          const isNearLeft = finalX < threshold
          const isNearTop = finalY < threshold

          this.anchorX = isNearRight ? 'right' : (isNearLeft ? 'left' : null)
          this.anchorY = isNearBottom ? 'bottom' : (isNearTop ? 'top' : null)

          localStorage.setItem('chatAnchorX', this.anchorX || '') // Store empty string if null
          localStorage.setItem('chatAnchorY', this.anchorY || '') // Store empty string if null
        }
        // --- End anchor calculation ---

        // 保存位置到本地存储
        localStorage.setItem('chatWindowPosition', JSON.stringify(this.windowPosition))
      }
    },

    stopWindowDragTouch() {
      if (this.windowDragging) {
        this.windowDragging = false
        window.removeEventListener('touchmove', this.onWindowDragTouch)
        window.removeEventListener('touchend', this.stopWindowDragTouch)

        // --- Calculate and save anchors (Touch) ---
        const windowWidth = window.innerWidth
        const windowHeight = window.innerHeight
        const chatWindow = this.$refs.chatWindow
        const chatWidth = chatWindow ? chatWindow.offsetWidth : 370
        const chatHeight = chatWindow ? chatWindow.offsetHeight : 550
        const finalX = this.windowPosition.x
        const finalY = this.windowPosition.y
        const threshold = this.anchorThreshold

        if (chatWidth > 0 && chatHeight > 0) { // Only calculate if dimensions are valid
          const isNearRight = windowWidth - finalX - chatWidth < threshold
          const isNearBottom = windowHeight - finalY - chatHeight < threshold
          const isNearLeft = finalX < threshold
          const isNearTop = finalY < threshold

          this.anchorX = isNearRight ? 'right' : (isNearLeft ? 'left' : null)
          this.anchorY = isNearBottom ? 'bottom' : (isNearTop ? 'top' : null)

          localStorage.setItem('chatAnchorX', this.anchorX || '') // Store empty string if null
          localStorage.setItem('chatAnchorY', this.anchorY || '') // Store empty string if null
        }
        // --- End anchor calculation ---

        localStorage.setItem('chatWindowPosition', JSON.stringify(this.windowPosition))
      }
    },

    // 处理窗口大小调整
    handleResize() {
      // if (!this.isChatOpen) return; // Keep commented to allow initial positioning

      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight
      const chatWindow = this.$refs.chatWindow
      // Use default dimensions if ref not available or chat closed
      const chatWidth = (this.isChatOpen && chatWindow) ? chatWindow.offsetWidth : 370
      const chatHeight = (this.isChatOpen && chatWindow) ? chatWindow.offsetHeight : 550

      // Ensure chatWidth/Height are positive
      if (chatWidth <= 0 || chatHeight <= 0) return

      let targetX, targetY

      // Calculate target X based on anchor
      if (this.anchorX === 'right') {
        targetX = windowWidth - chatWidth
      } else if (this.anchorX === 'left') {
        targetX = 0
      } else {
        // Use current position if no horizontal anchor
        targetX = this.windowPosition.x
      }

      // Calculate target Y based on anchor
      if (this.anchorY === 'bottom') {
        targetY = windowHeight - chatHeight
      } else if (this.anchorY === 'top') {
        targetY = 0
      } else {
        // Use current position if no vertical anchor
        targetY = this.windowPosition.y
      }

      // Clamp positions to ensure visibility within the new window dimensions
      const newX = Math.max(0, Math.min(targetX, windowWidth - chatWidth))
      const newY = Math.max(0, Math.min(targetY, windowHeight - chatHeight))

      // Correct indentation here
      this.windowPosition = {
        x: newX,
        y: newY
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-trigger {
  position: fixed;
  right: 0;
  transform: translateY(-50%);
  width: 42px;
  height: 42px;
  background-color: #4996b8;
  border-radius: 8px 0 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: -2px 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  user-select: none; /* 防止拖动时选中文本 */
  touch-action: none; /* 防止触摸设备上的默认行为 */
  will-change: top; /* 提示浏览器优化位置变换 */

  /* 只在非拖动状态下应用过渡效果 */
  &:not(.dragging) {
    transition: width 0.3s ease;
  }

  i {
    color: white;
    font-size: 24px;
  }

  &:hover {
    width: 46px;
    background-color: #4996b8;
  }

  /* 拖动时的样式 */
  &.dragging {
    cursor: grabbing;
  }
}

.chat-window {
  position: fixed;
  width: 370px;
  z-index: 10000;
  transition: none;
  user-select: none;

  &.dragging {
    cursor: move;
    .chat-header {
      cursor: move;
    }
  }
}

.chat-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: -1;
}

:deep(.sc-chat-window) {
  width: 370px !important;
  height: 550px !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 10px 0 0 0 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  position: static !important;
  right: auto !important;
  bottom: auto !important;
  top: auto !important;
  left: auto !important;
  transform: none !important;
}

:deep(.sc-header){
  min-height: 50px !important;
  padding: 0 10px !important;
}
:deep(.sc-header--title) {
  font-size: 14px !important;
  font-weight: bold !important;
}
:deep(.sc-header--close-button) {

}
:deep(.sc-message-list),
:deep(.sc-user-input) {
  position: relative !important;
  transform: none !important;
  right: auto !important;
  bottom: auto !important;
  top: auto !important;
  left: auto !important;
}

:deep(.sc-launcher) {
  display: none !important;
}

:deep(.sc-message-list) {
  padding: 15px !important;
  height: calc(100% - 120px) !important;
}

:deep(.sc-user-input) {
  position: absolute !important;
  bottom: 0 !important;
  width: 100% !important;
}

/* 通过类名控制停止 */
.shake.stop {
  animation: none;
}

.loading-message {
  position: absolute;
  bottom: 80px;
  left: 20px;
  right: 20px;
  z-index: 1000;
}

.loading-bubble {
  background-color: #f5f5f5;
  border-radius: 15px;
  padding: 12px 18px;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  max-width: 80%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease;

  i {
    color: #4996b8;
    font-size: 16px;
  }

  span {
    color: #333;
    font-size: 14px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  color: white;
  font-size: 14px;
  font-weight: bold;
  cursor: grab;
  padding: 0 10px;

  &:active {
    cursor: grabbing;
  }

  .new-chat-button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    font-weight: normal;

    i {
      font-size: 14px;
    }

    span {
      font-size: 12px;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.25);
      border-color: rgba(255, 255, 255, 0.4);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }
}
</style>
