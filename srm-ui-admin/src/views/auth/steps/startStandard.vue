<template>
  <div class="startStandard">
    <authSupplier
      :auth-id="authId"
    />
    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('auth.supplierCertificationProgram') }}
        <i
          class="el-icon-arrow-down"
          :style="showDetail? '':{transform: 'rotate(180deg)'}"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showDetail= !showDetail"
        />
      </div>
      <div v-show="showDetail">
        <el-form ref="standardInfo0" :model="standardInfo" :rules="standardRules" label-width="180px" inline>
          <el-form-item class="standardInfoItem" :label="$t('auth.expectedCompletionTime')" prop="">
            <show-or-edit
              :disabled="viewOnly"
              :value="standardInfo.expectFinishDate"
              type="Date"
              class="deliveryHeadItem"
            >
              <el-date-picker
                v-model="standardInfo.expectFinishDate"
                :disabled="viewOnly"
                :picker-options="pickerOptions"
                :placeholder="$t('order.selectDate')"
                type="date"
                placement="bottom-start"
                value-format="yyyy-MM-dd"
              />
            </show-or-edit>

          </el-form-item>
          <el-form-item class="standardInfoItem" :label="$t('auth.planCompletionReviewwrittenReview')">
            <show-or-edit
              :disabled="viewOnly"
              class="deliveryHeadItem"

              :value="standardInfo.planFinishCheckDate"
              type="Date"
            >
              <el-date-picker
                v-model="standardInfo.planFinishCheckDate"
                :disabled="viewOnly"
                :picker-options="pickerOptions"
                :placeholder="$t('order.selectDate')"
                type="date"
                placement="bottom-start"
                value-format="yyyy-MM-dd"
              />
            </show-or-edit>

          </el-form-item>
          <div>
            <el-form-item class="standardInfoItem" :label="$t('auth.planToCompleteTheAdmissionApplication')" prop="expectFinishAdmittanceApplyDate">
              <show-or-edit
                :disabled="viewOnly"
                type="Date"
                class="deliveryHeadItem"

                :value="standardInfo.expectFinishAdmittanceApplyDate"
              >
                <el-date-picker
                  v-model="standardInfo.expectFinishAdmittanceApplyDate"
                  :disabled="viewOnly"
                  :picker-options="pickerOptions"

                  :placeholder="$t('order.selectDate')"
                  type="date"
                  placement="bottom-start"
                  value-format="yyyy-MM-dd"
                />
              </show-or-edit>

            </el-form-item>
            <el-form-item class="standardInfoItem" :label="$t('auth.plannedTimeForCompletionOfAdmissionApproval')" prop="expectFinishAdmittanceCheckDate">
              <show-or-edit
                :disabled="viewOnly"
                class="deliveryHeadItem"
                type="Date"
                :value="standardInfo.expectFinishAdmittanceCheckDate"
              >
                <el-date-picker
                  v-model="standardInfo.expectFinishAdmittanceCheckDate"

                  :picker-options="pickerOptions"
                  :disabled="viewOnly"

                  :placeholder="$t('order.selectDate')"
                  type="date"
                  placement="bottom-start"
                  value-format="yyyy-MM-dd"
                />
              </show-or-edit>

            </el-form-item>
          </div>

          <el-form-item class="standardInfoReason" :label="$t('auth.reasonsForStartingCertification')" prop="setupReason">
            <show-or-edit
              :value="standardInfo.setupReason"
              :disabled="viewOnly"
            >
              <el-input
                v-model="standardInfo.setupReason	"
                :disabled="viewOnly"
                maxlength="200"
                show-word-limit
                style="width: 630px"
                type="textarea"
                :rows="3"
              />
            </show-or-edit>

          </el-form-item>
          <el-form-item>
            <el-upload
              :class="viewOnly ? 'viewUpload':'upload-demo'"
              :disabled="viewOnly"
              :action="uploadUrl"
              :headers="getBaseHeader()"
              :before-upload="beforeUpload"
              :on-remove="onRemove"
              :on-preview="onPreview"
              :file-list="reasonFileList"
              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,standardInfo.id,'reasons_for_initiating_authentication')"
              multiple
            >
              <el-button v-if="!viewOnly" plain type="primary">{{ $t('auth.uploadAttachments') }}</el-button>
            </el-upload>

          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('auth.supplierCertificationStandards') }}
        <i
          class="el-icon-arrow-down"
          :style="showDetail0? '':{transform: 'rotate(180deg)'}"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showDetail0= !showDetail0"
        />
      </div>
      <el-form v-show="showDetail0" ref="standardInfo1" :model="standardInfo" :rules="standardRules" label-width="180px">
        <el-form-item :label="$t('auth.isItQuickCertification')" prop="fastAuthentication">
          <show-or-edit
            :value="standardInfo.fastAuthentication"
            :disabled="viewOnly"
            type="Boolean"
          >
            <el-radio-group
              v-model="standardInfo.fastAuthentication"
              :disabled="viewOnly"
            >
              <el-radio :label="true">{{ $t('auth.yes') }}</el-radio>
              <el-radio :label="false">{{ $t('auth.no') }}</el-radio>
            </el-radio-group>
          </show-or-edit>

        </el-form-item>
        <el-form-item :label="$t('auth.temporarySupplierOrNot')" prop="fastTemporary">
          <show-or-edit
            :value="standardInfo.fastTemporary"
            :disabled="viewOnly"
            type="Boolean"
          >
            <el-radio-group
              v-model="standardInfo.fastTemporary	"

              :disabled="viewOnly"
            >
              <el-radio :label="true">{{ $t('auth.yes') }}</el-radio>
              <el-radio :label="false">{{ $t('auth.no') }}</el-radio>
            </el-radio-group>

          </show-or-edit>
        </el-form-item>
        <div v-if="!standardInfo.fastAuthentication&&!standardInfo.fastTemporary">
          <el-form-item :label="$t('auth.keySupplierOrNot')" prop="keySupplier">

            <show-or-edit
              :value="standardInfo.keySupplier"
              :disabled="viewOnly"
              type="Boolean"
            >
              <el-radio-group
                v-model="standardInfo.keySupplier"
                :disabled="viewOnly"
                @change="changeCategory"
              >
                <el-radio :label="true">{{ $t('auth.yes') }}</el-radio>
                <el-radio :label="false">{{ $t('auth.no') }}</el-radio>
              </el-radio-group>

            </show-or-edit>

          </el-form-item>
          <el-form-item
            v-for="(item,index) in standardInfo.categoryRelList"
            :prop="`categoryRelList.${index}.checkIdList`"
            :rules="{
              required: true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }"
          >
            <template #label>
              <dict-tag :value="item.categoryId" :type="DICT_TYPE.COMMON_CATEGORY" />
            </template>
            <div style="display: flex">
              <show-or-edit
                :value="item.checkIdList"
                :disabled="viewOnly"
                :dict="DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY"
              >

                <el-radio-group
                  v-model="item.checkIdList"
                  :disabled="viewOnly"
                  style="padding-top: 8px"
                  @change="changeCategory"
                >

                  <el-radio
                    v-for="item in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY)"
                    :key="item.value"
                    :label="item.value"
                  > {{ item.label }}</el-radio>

                </el-radio-group>
              </show-or-edit>

              <el-upload
                :class="viewOnly ? 'viewUpload':'upload-demo'"
                :action="uploadUrl"
                :headers="getBaseHeader()"
                :before-upload="beforeUpload"
                :on-remove="onRemove"
                :disabled="viewOnly"
                :on-preview="onPreview"
                :file-list="item.fileList"
                :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,item.categoryId,'supplier_category')"
                multiple
              >
                <el-button v-if="!viewOnly" plain type="primary">{{ $t('auth.uploadDemandAnalysis') }}</el-button>
              </el-upload>
              <el-button v-if="!viewOnly && !item.tqcdmProjectId" plain type="primary" @click="doEvaluateTQCDM(item.categoryId)">{{ $t('能力矩阵评估') }}</el-button>
              <div v-if="item.tqcdmProjectId" style="display: flex">
                能力矩阵评估：
                <span v-if="item.tqcdmRecordDTO.status==='completed'" style="display: flex;align-items: center;">
                  <span
                    class="circle fulfilled"
                    :class="satisfactionMap[item.tqcdmRecordDTO?.evaluateResultColor]"
                  />
                  <span style="margin: 0 10px">
                  {{ item.tqcdmRecordDTO.capacityPercent*100 + '%' }}
                  </span>
                </span>
                <el-button v-else type="text" style="text-decoration: underline" @click="$router.push(`/tqcdm/evaluate/detail/${item.tqcdmProjectId}?no=${item.tqcdmRecordDTO.taskNo}&viewOnly=${viewOnly}`)">
                  {{ getDictDataLabel(DICT_TYPE.TQCDM_TQCDM_EVALUATE_PROJECT_STATUS, item.tqcdmRecordDTO.status) }}
                </el-button>
                <el-button v-if="!viewOnly" type="text" style="text-decoration: underline" @click="doEvaluateTQCDM(item.categoryId,item.tqcdmProjectId)">{{ $t('修改') }}</el-button>
              </div>
            </div>

          </el-form-item>
          <el-form-item :label="$t('auth.systemRecommendationProcess')" prop="">
            <dict-tag style="color: #4996b8" :type="DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_SYSTEM_RECOMMEND_PROCESS" :value="standardInfo.systemRecommendationProcess" />
            <span v-if="standardInfo.systemRecommendationProcess.length === 0" style="color: #4996b8">无</span>
          </el-form-item>
          <el-form-item :label="$t('auth.confirmTheProcessAdopted')" prop="confirmAdoptionProcess">
            <show-or-edit
              :value="standardInfo.confirmAdoptionProcess"
              :dict="DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_SYSTEM_USE_PROCESS"
              :disabled="viewOnly"
            >
              <el-checkbox-group
                v-model="standardInfo.confirmAdoptionProcess"

                :disabled="viewOnly"
              >
                <el-checkbox
                  v-for="item in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_SYSTEM_USE_PROCESS)"
                  :key="item.value"
                  :label="item.value"
                > {{ item.label }}</el-checkbox>
              </el-checkbox-group>
            </show-or-edit>

          </el-form-item>
          <el-form-item v-if="standardInfo.systemRecommendationProcess?.includes('spot_check')&&!standardInfo.confirmAdoptionProcess?.includes('spot_check')" :label="$t('auth.offSiteAuditRiskAssessment')" prop="">

            <el-upload
              :class="viewOnly ? 'viewUpload':'upload-demo'"
              :action="uploadUrl"
              :headers="getBaseHeader()"
              :before-upload="beforeUpload"
              :on-remove="onRemove"
              :on-preview="onPreview"
              :disabled="viewOnly"
              :file-list="noOnSiteAuditFileList"

              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,standardInfo.id,'risk_assessment_not_audited_on-site')"
              multiple
            >
              <el-button v-if="!viewOnly" plain type="primary">{{ $t('auth.uploadRiskAssessment') }}</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item v-if="standardInfo.systemRecommendationProcess?.includes('written_review_check')&&!standardInfo.confirmAdoptionProcess?.includes('written_review_check')&&!standardInfo.confirmAdoptionProcess?.includes('spot_check')" :label="$t('auth.riskAssessmentIsNotReviewedInWriting')" prop="">
            <el-upload
              :class="viewOnly ? 'viewUpload':'upload-demo'"
              :action="uploadUrl"
              :disabled="viewOnly"
              :headers="getBaseHeader()"
              :before-upload="beforeUpload"
              :on-remove="onRemove"
              :on-preview="onPreview"
              :file-list="noWrittenReviewFileList"

              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,standardInfo.id,'risk_assessments_not_written_review')"
              multiple
            >
              <el-button v-if="!viewOnly" plain type="primary">{{ $t('auth.uploadRiskAssessment') }}</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item v-if="standardInfo.confirmAdoptionProcess.includes('supplier_self-assessment')" :label="$t('auth.supplierSelfAssessmentForm')" prop="">
            <show-or-edit
              :custom-list="assessmentConfigList"
              :value="standardInfo.assessment"
            >
              <el-select
                v-model="standardInfo.assessment"
                :disabled="viewOnly"
                multiple
              >
                <el-option v-for="item in assessmentConfigList" :value="item.id" :label="item.name" />
              </el-select>
            </show-or-edit>

          </el-form-item>

        </div>
      </el-form>
    </el-card>
    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('auth.requirementsOfSupplierQualificationAgreement') }}
        <i
          class="el-icon-arrow-down"
          :style="showDetail1? '':{transform: 'rotate(180deg)'}"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showDetail1= !showDetail1"
        />
      </div>
      <el-form
        v-show="showDetail1"
        ref="standardInfo2"
        :model="standardInfo"
        :rules="standardRules"
        label-width="180px"
      >
        <el-form-item v-for="item in standardInfo.protocolConfigList">
          <template #label>
            {{ item.name }}

          </template>
          <div>
            <el-checkbox
              v-if="!viewOnly"
              v-model="item.checkAll"
              :indeterminate="item.isIndeterminate"
              @change="handleCheckAllChange(item)"
            >{{ $t('auth.selectAll') }}</el-checkbox>
          </div>

          <el-checkbox
            v-for="a in item.childList"
            v-model="a.checked"
            :disabled="viewOnly"
            style="width: 235px"
            :label="a.folderId"
            @change="handleCheckChange(item)"
          >{{ a.name }}</el-checkbox>
        </el-form-item>
        <el-form-item :label="$t('auth.addAnotherFile')">
          <show-or-edit
            :value="standardInfo.otherFolderDesc"
            :disabled="viewOnly"
          >
            <el-input
              v-model="standardInfo.otherFolderDesc"
              :disabled="viewOnly"
            />

          </show-or-edit>

        </el-form-item>
        <el-form-item :label="$t('auth.requiredSupplierCompletionTime')" prop="requireFinishDate">
          <show-or-edit
            :value="standardInfo.requireFinishDate"
            :disabled="viewOnly"
            type="Date"
          >
            <el-date-picker
              v-model="standardInfo.requireFinishDate"
              :disabled="viewOnly"
              :picker-options="pickerOptions"
              class="deliveryHeadItem"
              :placeholder="$t('order.selectDate')"
              type="date"
              placement="bottom-start"
              value-format="yyyy-MM-dd"
            />
          </show-or-edit>

        </el-form-item>
        <el-form-item :label="$t('auth.addAdditionalDescription')">
          <show-or-edit
            :value="standardInfo.otherContent"
            :disabled="viewOnly"
          >
            <el-input
              v-model="standardInfo.otherContent"
              :disabled="viewOnly"
              maxlength="200"
              show-word-limit
              type="textarea"
              :rows="3"
            />
          </show-or-edit>

        </el-form-item>
        <el-form-item :label="$t('auth.operatorOfNextNode')">
          <show-or-edit
            :value="standardInfo.nextOperateId"
            :disabled="viewOnly"
            :dict="DICT_TYPE.COMMON_USERS"
          >
            <el-select
              v-model="standardInfo.nextOperateId"
              :disabled="viewOnly"
              filterable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </show-or-edit>

        </el-form-item>
      </el-form>
      <div v-if="!viewOnly" style="text-align: center">
        <el-button class="bottomBtn" plain @click="closeTab">{{ $t('common.cancel') }}</el-button>
        <el-button v-has-permi="['auth:supplier-set-standard-step:back']" plain type="danger" class="bottomBtn" @click="backVisible =true">{{ $t('auth.return') }}</el-button>
        <el-button v-has-permi="['auth:supplier-set-standard-step:submit']" type="primary" :loading="loadingButton" class="bottomBtn" @click="submitStandard">{{ $t('common.submit') }}</el-button>
        <el-button v-has-permi="['auth:supplier-set-standard-step:save']" plain type="primary" :loading="loadingButton" class="bottomBtn" @click="saveStandard">{{ $t('common.save') }}</el-button>
      </div>

    </el-card>
    <el-dialog
      v-if="backVisible"
      width="400px"
      :title="$t('auth.pleaseEnterTheReturnReason')"
      :visible.sync="backVisible"
    >
      <el-input v-model="backReason" type="textarea" :rows="3" />
      <div slot="footer">
        <el-button @click="backVisible=false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" :loading="loadingButton" @click="submitBack">{{ $t('common.submit') }}</el-button>
      </div>
    </el-dialog>
    <!--    关联TQCDM评估单据弹框-->
    <el-dialog
      v-if="tqcdmVisible"
      :visible.sync="tqcdmVisible"
      :title="tqcdmDialogTitle"
      width="1000px"
    >
      <div v-for="(item, index) in tqcdmResult" :key="index" style="cursor:pointer;" @click="setTqcdm(index)">
        <el-descriptions
          :column="1"
          style="padding: 10px 50px;margin: 10px 0;border-radius: 5px"
          :style="item.selected?'border: 2px #4996b8 solid;' :'border: 2px #f2f2f2 solid;'"
          :content-style="{color:'#565656'}"
          :label-style="{width: '200px',color:'#929292', 'padding-left': '23px'}"
        >
          <template #title>
            <el-radio v-model="item.selected" :label="true" >
              <span style="color: #383838;margin-right: 10px;font-weight: 700;">{{ item.taskName }}</span>
              <dict-tag :type="DICT_TYPE.TQCDM_TQCDM_EVALUATE_PROJECT_STATUS" :value="item.status" />
            </el-radio>

          </template>
          <el-descriptions-item :label="$t('评估品类')" :span="1">
            <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="item.evaluateCategoryId" />
          </el-descriptions-item>

          <el-descriptions-item :label="$t('评估模板')" :span="1">
            {{ item.evaluateTemplateName }}
          </el-descriptions-item>

          <el-descriptions-item :label="$t('版本')" :span="1">
            {{ item.evaluateTemplateVersion }}
          </el-descriptions-item>

          <el-descriptions-item :label="$t('模板创建日期')" :span="1">
            {{ item.templateCreateTime }}
          </el-descriptions-item>

          <el-descriptions-item :label="$t('评估日期')" :span="1">
            {{ item.createTime }}

          </el-descriptions-item>

          <el-descriptions-item :label="$t('能力满足度')" :span="1">
            <div style="display: flex;align-items: center">
              <span
                class="circle fulfilled"
                :class="satisfactionMap[item.evaluateResultColor]"
              />
              <span v-if="item.capacityPercent" style="margin-left: 5px">{{ item.capacityPercent*100 + '%' }}</span>
            </div>

          </el-descriptions-item>

          <el-descriptions-item :label="$t('评估汇总意见')" :span="1">
            {{ item.evaluateSummary }}
          </el-descriptions-item>

        </el-descriptions>
      </div>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button
          @click="tqcdmVisible=false;$router.push(`/tqcdm/evaluate/create/0?viewOnly=false&authId=${authId}&categoryId=${tqcdmSelectedCategoryId}&supplierId=${standardInfo.supplierId}&supplierName=${standardInfo.supplierName}`)"
        >{{ $t('重新评估') }}</el-button>
        <el-button type="primary" @click="doBindWithTqcdmId(tqcdmSelectedCategoryId)">{{ $t('确认关联') }}</el-button>
      </div>
    </el-dialog>

<!--    重新创建评估单据的dialog-->
    <el-dialog
      v-if="createEvaluateVisible"
      :append-to-body="false"
      :modal-append-to-body="false"
      width="400px"
      :title="$t('请在能力矩阵任务单页面完成评估')"
      :visible.sync="createEvaluateVisible"
    >
      <div slot="footer">
        <el-button type="primary" @click="continueAuthProcess">{{ $t('继续认证') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  backSupplierSetStandardStep,
  deleteSupplierFileRel,
  getSubFolder,
  saveSupplierSetStandardStep, submitSupplierSetStandardStep,
  supplierSetStandardStep, bindWithTqcdm
} from '@/api/auth'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'
import dayjs from 'dayjs'
import { getBaseHeader } from '@/utils/request'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { hasEvaluate, hasTqcdmEvaluate } from '@/api/tqcdm'
import {getConfig} from "@/utils/config";

export default {
  name: 'StartStandard',
  components: {
    ShowOrEdit,
    authSupplier: () => import('../component/authSupplier')
  },
  props: ['viewOnly', 'authId'],
  data() {
    const validExpectFinishAdmittanceApplyDate = (rule, value, callback) => {
      if (dayjs(value).isBefore(this.standardInfo.planFinishCheckDate)) {
        callback(new Error(this.$t('auth.itShallNotBeLessThanThePlannedCompletionTimeOfAuditwrittenReview')))
      } else {
        callback()
      }
    }
    const expectFinishAdmittanceCheckDate = (rule, value, callback) => {
      if (dayjs(value).isBefore(this.standardInfo.expectFinishAdmittanceApplyDate)) {
        callback(new Error(this.$t('auth.noLessThanThePlannedCompletionOfAdmissionApplication')))
      } else {
        callback()
      }
    }
    return {
      // 涉及到数据库更新操作的按钮操作增加loading操作，防止重复操作
      loadingButton: false,
      showDetail: true,
      showDetail0: true,
      showDetail1: true,
      showDetail2: true,
      showDetail3: true,
      standardCategory: [],
      standardInfo: {
        authId: 0,
        categoryRelList: [],
        dictRelList: [],
        expectFinishAdmittanceApplyDate: '',
        expectFinishAdmittanceCheckDate: '',
        expectFinishDate: '',
        fastAuthentication: '',
        fastTemporary: '',
        folderRelList: [],
        id: 0,
        keySupplier: true,
        nextOperateId: '',
        otherContent: '',
        otherFolderDesc: '',
        planFinishCheckDate: '',
        requireFinishDate: '',
        setupReason: '',
        supplierId: '',
        confirmAdoptionProcess: [],
        systemRecommendationProcess: [],
        assessment: [],
        fileRelList: [],
        isSubmit: false
      },
      standardRules: {
        setupReason: [{ required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }],
        fastAuthentication: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        fastTemporary: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        keySupplier: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        confirmAdoptionProcess: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        requireFinishDate: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        expectFinishAdmittanceApplyDate: [{
          trigger: 'change',
          validator: validExpectFinishAdmittanceApplyDate
        }],
        expectFinishAdmittanceCheckDate: [{
          trigger: 'change',
          validator: expectFinishAdmittanceCheckDate
        }]
      },
      assessmentConfigList: [],
      uploadUrl: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload',
      getBaseHeader,
      reasonFileList: [],
      noOnSiteAuditFileList: [],
      noWrittenReviewFileList: [],
      backVisible: false,
      createEvaluateVisible: false, // 重新创建评估单据的dialog
      backReason: '',
      pickerOptions: {
        disabledDate(time) {
          return dayjs().isAfter(dayjs(time), 'day')
        }
      },
      tqcdmVisible: false, // TQCDM关联的单据dialog
      tqcdmDialogTitle: '', // TQCDM关联的dialog title
      tqcdmResult: [], // 供应商+品类的 TQCDM评估结果
      tqcdmSelectedCategoryId: undefined, // 选中的待评估的品类
      satisfactionMap: {
        green: 'fulfilled-selected',
        yellow: 'portion-selected',
        red: 'unsatisfactory-selected',
        '': 'default'
      }
    }
  },
  mounted() {
    this.getStandardCategory()
    this.getAssessment()
  },
  methods: {
    getDictDataLabel,
    /**
     * 供应商+品类确认绑定TQCDM模块的单据，用于回显TQCDM部分评估结果
     */
    doBindWithTqcdmId(categoryId, tqcdmId) {
      bindWithTqcdm({
        authId: this.authId,
        categoryId: categoryId,
        tqcdmId: this.tqcdmResult.find(a => a.selected)?.id
      }).then(res => {
        this.tqcdmVisible = false
        this.$message.success(this.$t('操作成功'))
        // 仅刷新品类数组
        this.standardInfo.categoryRelList = res.data
      })
    },
    continueAuthProcess() {
      this.createEvaluateVisible = false
      this.getStandardCategory()
    },
    /**
     * TQCDM相关-能力矩阵评估
     * 供应商+品类是否做过or正在做 评估，如果没有则直接带着品类+供应商进行 新建评估
     */
    doEvaluateTQCDM(categoryId, id) {
      hasTqcdmEvaluate({
        supplierId: this.standardInfo.supplierId,
        categoryId: categoryId
      }).then(res => {
        if (res.data?.length > 0) {
          // 已经做过了tqcdm认证，则需要打开dialog
          this.tqcdmVisible = true
          res.data.forEach((a, index) => {
            if (id) {
              a.selected = a.id === id
            } else if (index === 0) {
              a.selected = true
            } else {
              a.selected = false
            }
          })
          this.tqcdmResult = res.data
          this.tqcdmSelectedCategoryId = categoryId
          if (res.data.length > 1) {
            this.tqcdmDialogTitle = '请选择关联的评估结果'
          } else {
            this.tqcdmDialogTitle = '是否关联已有的评估结果'
          }
        } else {
          this.createEvaluateVisible = true
          this.$router.push(`/tqcdm/evaluate/create/0?viewOnly=false&authId=${this.authId}&categoryId=${categoryId}&supplierId=${this.standardInfo.supplierId}&supplierName=${this.standardInfo.supplierName}`)
        }
      })
    },
    getStandardCategory() {
      supplierSetStandardStep({
        authId: this.authId
      }).then(res => {
        res.data.confirmAdoptionProcess = res.data.dictRelList.find(item => item.businessType === DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_SYSTEM_USE_PROCESS)?.checkIdList || []
        res.data.systemRecommendationProcess = res.data.dictRelList.find(item => item.businessType === DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_SYSTEM_RECOMMEND_PROCESS)?.checkIdList || []
        res.data.fileRelList.forEach(item => {
          item.fileRelList.forEach(a => {
            a.name = a.fileName
          })
        })
        this.reasonFileList = res.data.fileRelList.find(item => item.businessValue === 'reasons_for_initiating_authentication')?.fileRelList || []
        this.noOnSiteAuditFileList = res.data.fileRelList.find(item => item.businessValue === 'risk_assessment_not_audited_on-site')?.fileRelList || []
        this.noWrittenReviewFileList = res.data.fileRelList.find(item => item.businessValue === 'risk_assessments_not_written_review')?.fileRelList || []
        const categoryFileList = res.data.fileRelList.find(item => item.businessValue === 'supplier_category')?.fileRelList || []
        res.data.categoryRelList.map(item => {
          item.fileList = categoryFileList.filter(file => file.businessId === item.categoryId)
          item.checkIdList = item.checkIdList.at(0)
        })
        res.data.fileRelList.length = 0
        res.data.assessment = res.data.assessmentList.map(item => item.folderId)
        res.data.protocolConfigList.map(item => {
          item.checkAll = item.childList.every(a => a.checked)
          item.isIndeterminate = item.childList.some(a => a.checked) && !item.checkAll
        })
        // res.data.assessment = res.data.dictRelList.find(item => item.businessType === DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_ASSESSMENT)?.checkIdList || []

        this.standardInfo = { ...this.standardInfo, ...res.data }
        this.standardInfo.nextOperateId = res.data.nextOperateId || this.$store.getters.userId
        this.$nextTick(() => {
          for (let i = 0; i < 3; i++) {
            this.$refs[`standardInfo${i}`].clearValidate()
          }
        })
      })
    },
    getAssessment() {
      getSubFolder({
        type: 'supplier_self-assessment'
      }).then(res => {
        this.assessmentConfigList = res.data
      })
    },
    saveStandard() {
      this.loadingButton = true
      this.standardInfo.folderRelList = []
      this.standardInfo.dictRelList = []

      this.standardInfo.protocolConfigList.map(item => {
        item.childList.map(a => {
          if (a.checked) {
            this.standardInfo.folderRelList.push({
              folderId: a.folderId,
              type: 'certification'
            })
          }
        })
      })
      // if (this.standardInfo.folderRelList.length === 0) {
      //   this.$message.error('请选择供应商资质协议要求')
      //   return
      // }

      this.standardInfo.categoryRelList.map(item => {
        if (item.checkIdList) {
          this.standardInfo.dictRelList.push({
            businessId: item.categoryId,
            businessType: DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY,
            value: item.checkIdList
          })
        }
      })
      this.standardInfo.systemRecommendationProcess.map(item => {
        this.standardInfo.dictRelList.push({
          businessId: this.standardInfo.id,
          businessType: DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_SYSTEM_RECOMMEND_PROCESS,
          value: item
        })
      })

      this.standardInfo.confirmAdoptionProcess.map(item => {
        this.standardInfo.dictRelList.push({
          businessId: this.standardInfo.id,
          businessType: DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_SYSTEM_USE_PROCESS,
          value: item
        })
      })
      this.standardInfo.assessment.map(item => {
        this.standardInfo.folderRelList.push({
          folderId: item,
          type: 'self_assessment'
        })
      })
      let process = ''
      if (this.standardInfo.confirmAdoptionProcess.includes('written_review_check')) {
        process = 'written_review_check'
      } else if (this.standardInfo.confirmAdoptionProcess.includes('spot_check')) {
        process = 'spot_check'
      } else {
        process = 'allow_apply'
      }
      saveSupplierSetStandardStep({ ...this.standardInfo,
        process
      }).then(res => {
        this.loadingButton = false
        this.standardInfo.id = res.data
        this.getStandardCategory()
        this.$message.success(this.$t('common.savedSuccessfully'))
      }).catch(_ => {
        this.loadingButton = false
      })
    },
    changeCategory() {
      this.standardInfo.systemRecommendationProcess.length = 0
      if (this.standardInfo.categoryRelList.some(item => ['bottleneck', 'lever', 'strategy'].includes(item.checkIdList)) || this.standardInfo.keySupplier) {
        this.standardInfo.systemRecommendationProcess.push('spot_check')
      } else {
        this.standardInfo.systemRecommendationProcess.push('written_review_check')
      }
    },
    beforeUpload(file) {
      // if (file.size > 5242880) {
      //   $modal.msgError(this.$t('common.uploadFileSizeCannotExceedm'))
      //   return false
      // }
      // if (this.fileList.length > 5) {
      //   $modal.msgError(this.$t('order.onlyUpToAttachmentsAreSupported'))
      //   return false
      // }
      // if (!this.fileType.split(',').includes(file.name.slice(file.name.lastIndexOf('.') + 1))) {
      //   $modal.msgError(this.$t('order.onlySupported') + this.fileType + this.$t('order.format'))
      //   return false
      // }
    },
    onSuccess(response, file, fileList, businessId, businessValue) {
      this.standardInfo.fileRelList.push({
        authId: this.standardInfo.authId,
        businessId,
        businessType: 'auth_supplier_upload_business_type',
        businessValue,
        fileId: response.data.id,
        fileName: response.data.name
      })
    },
    onRemove(file) {
      if (file.id) {
        deleteSupplierFileRel({ id: file.id }).then(_ => {
          this.$message.success(this.$t('common.delSuccess'))
        })
      } else {
        this.standardInfo.fileRelList = this.standardInfo.fileRelList.filter(a => a.fileId !== file?.response.data.id)
      }
    },
    onPreview(file) {
      if (file.filePath) {
        window.open(file.filePath)
      }
      if (file.response.data.url) {
        window.open(file.response.data.url)
      }
    },
    submitStandard() {
      this.$confirm(this.$t('auth.confirmSubmission'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        let pass = true
        for (let i = 0; i < 3; i++) {
          this.$refs[`standardInfo${i}`].validate(valid => {
            if (!valid) {
              pass = false
            }
          })
        }
        if (!pass) {
          this.$message.error(this.$t('auth.pleaseCompleteTheInformation'))
          return
        }
        this.standardInfo.folderRelList = []
        this.standardInfo.dictRelList = []

        this.standardInfo.protocolConfigList.map(item => {
          item.childList.map(a => {
            if (a.checked) {
              this.standardInfo.folderRelList.push({
                folderId: a.folderId,
                type: 'certification'
              })
            }
          })
        })
        if (this.standardInfo.folderRelList.length === 0) {
          this.$message.error(this.$t('auth.pleaseSelectTheSupplierQualificationAgreementRequirements'))
          return
        }
        // UFFF-1268
        this.loadingButton = true

        this.standardInfo.categoryRelList.map(item => {
          if (item.checkIdList) {
            this.standardInfo.dictRelList.push({
              businessId: item.categoryId,
              businessType: DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY,
              value: item.checkIdList
            })
          }
        })
        this.standardInfo.systemRecommendationProcess.map(item => {
          this.standardInfo.dictRelList.push({
            businessId: this.standardInfo.id,
            businessType: DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_SYSTEM_RECOMMEND_PROCESS,
            value: item
          })
        })

        this.standardInfo.confirmAdoptionProcess.map(item => {
          this.standardInfo.dictRelList.push({
            businessId: this.standardInfo.id,
            businessType: DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_SYSTEM_USE_PROCESS,
            value: item
          })
        })
        this.standardInfo.assessment.map(item => {
          this.standardInfo.folderRelList.push({
            folderId: item,
            type: 'self_assessment'
          })
        })
        let process = ''
        if (this.standardInfo.confirmAdoptionProcess.includes('written_review_check')) {
          process = 'written_review_check'
        } else if (this.standardInfo.confirmAdoptionProcess.includes('spot_check')) {
          process = 'spot_check'
        } else {
          process = 'allow_apply'
        }
        submitSupplierSetStandardStep({ ...this.standardInfo,
          isSubmit: true,
          process
        }).then(res => {
          this.standardInfo.id = res.data
          this.$message.success(this.$t('supplier.submittedSuccessfully'))
          this.$tab.closeOpenPage('/auth/authindex')
          this.loadingButton = false
        }).catch(_ => {
          this.loadingButton = false
        })
      })
    },
    submitBack() {
      if (!this.backReason) {
        this.$message.error(this.$t('auth.pleaseEnterTheReturnReason'))
        return
      }
      this.loadingButton = true
      backSupplierSetStandardStep(
        { authId: this.standardInfo.authId,
          reason: this.backReason
        }).then(res => {
        this.backReason = ''
        this.backVisible = false
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
        this.$tab.closeOpenPage('/auth/authindex')
        this.loadingButton = false
      }).catch(_ => {
        this.loadingButton = false
      })
    },
    handleCheckAllChange(item) {
      if (item.checkAll) {
        item.childList.map(a => {
          a.checked = true
        })
      } else {
        item.childList.map(a => {
          a.checked = false
        })
      }
      // this.checkedCities = item.checkAll ? cityOptions : []
      item.isIndeterminate = false
    },
    handleCheckChange(item) {
      const checkedCount = item.childList.filter(w => w.checked).length
      item.checkAll = checkedCount === item.childList.length
      item.isIndeterminate = checkedCount > 0 && checkedCount < item.childList.length
    },
    // 关闭当前tab
    closeTab() {
      this.$tab.closeOpenPage('/auth/authindex')
    },
    setTqcdm(index) {
      this.tqcdmResult.forEach((a, tindex) => {
        a.selected = index === tindex
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.startStandard{
  //padding: 25px 15px;
}
.standardInfoItem{
  width: 33.3%;
  ::v-deep .el-form-item__content {
    width: calc(100% - 180px);
  }
}
.standardInfoReason{
  width: 66.6%;
  ::v-deep .el-form-item__content {
    width: calc(100% - 180px);
  }
}
.upload-demo{
  width: 400px;
  margin-left: 20px;
}

.viewUpload{
  width: 400px;
  margin-left: 20px;
  ::v-deep .el-upload--text{
    display: none;
  }
}
.commonCard{
  margin: 10px 0 ;
}
::v-deep .el-card__header{
  border-top: 3px solid #4996b8;
  border-bottom: none;
}
.mainTab{
  color: #4996b8;
  font-size: 16px;
  font-weight: 700;
}
.bottomBtn{
  width: 80px;
}
::v-deep .el-input__inner {
  color: #606266 !important;
  //background: #ffffff!important;
}

.circle {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background-color: white;
  font-size: 20px;
}

.fulfilled{
  //border: 2px solid #27ae60;
  &-selected{
    background-color: #67C23A;
  }
}

.portion{
  //border: 2px solid #ff8f3b;
  &-selected{
    background-color: #FF994F;
  }
}

.unsatisfactory{
  //border: 2px solid #FA5151;
  &-selected{
    background-color: #FA5151;
  }
}
.default{
  border: 2px solid #939393;
  //background-color: #F9F9F9;
}

</style>
