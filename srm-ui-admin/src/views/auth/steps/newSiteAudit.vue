<template>
  <div v-disable-all="viewOnly" class="startStandard">
    <authSupplier
      :auth-id="authId"
    >
      <el-button v-if="!viewOnly" type="primary">查看资源开发申请</el-button>
    </authSupplier>
    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('现场审核邀请') }}
        <i
          :style="showDetail2? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-down"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showDetail2= !showDetail2"
        />
      </div>
      <div v-show="showDetail2">
        <el-form
          ref="siteAuditInfo"
          :model="siteAuditInfo"
          :rules="auditRule"
          label-width="150px"
          label-position="left"
        >
          <div>
            <el-form-item label="审核内容" prop="checkItem">
              <show-or-edit
                :disabled="viewOnly"
                :dict="DICT_TYPE.AUTH_SPOT_CHECK_COMMON_TYPE"
                :value="siteAuditInfo.checkItem"
              >
                <el-checkbox-group v-model="siteAuditInfo.checkItem">
                  <el-checkbox v-for="item in getDictDatas(DICT_TYPE.AUTH_SPOT_CHECK_COMMON_TYPE)" :label="item.value">{{ item.label }}</el-checkbox>

                </el-checkbox-group>
              </show-or-edit>

            </el-form-item>
            <el-row>
              <el-col :span="12">
                <el-form-item label="预定现场审核时间" prop="dateRange">
                  <el-date-picker
                    v-model="siteAuditInfo.extraInfo.reserveCheckStartDate"
                    :disabled="viewOnly"
                    style="margin-right:30px"
                    type="datetime"
                    placeholder="选择开始时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  />
                  <el-date-picker
                    v-model="siteAuditInfo.extraInfo.reserveCheckEndDate"
                    :disabled="viewOnly"
                    type="datetime"
                    placeholder="选择结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  />

                </el-form-item>

              </el-col>
              <el-col :span="12">
                <el-form-item label="预定现场审核代表" prop="extraInfo.reserveSupplierProcessor">
                  <show-or-edit
                    :disabled="viewOnly"
                    :dict="DICT_TYPE.COMMON_USERS"
                    :value="siteAuditInfo.extraInfo.reserveSupplierProcessor"
                  >
                    <el-select v-model="siteAuditInfo.extraInfo.reserveSupplierProcessor" multiple filterable>

                      <el-option
                        v-for="(item,index) in getDictDatas(DICT_TYPE.COMMON_USERS)"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                      />

                    </el-select>
                  </show-or-edit>
                </el-form-item>

              </el-col>
            </el-row>

            <el-form-item label="给供应商的备注">
              <show-or-edit
                :disabled="viewOnly"
                :value="siteAuditInfo.extraInfo.supplierRemark"
              >
                <el-input
                  v-model="siteAuditInfo.extraInfo.supplierRemark"
                  type="textarea"
                  autosize
                  :disabled="writeFlag"
                />
              </show-or-edit>
            </el-form-item>
            <div v-show="siteAuditInfo.extraInfo.lastSendMailDate" style="text-align: right;margin: 0 10px 10px">
              上次发送时间{{ dayjs(siteAuditInfo.extraInfo.lastSendMailDate).format('YYYY-MM-DD HH:mm:ss') }}</div>
            <div style="text-align: right">
              <el-button v-if="!viewOnly" :loading="auditLoading" type="primary" :disabled="writeFlag" @click="submitAudit">发送现场审核邀请</el-button>

            </div>
          </div>

        </el-form>
      </div>
    </el-card>
    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('现场审核检查项目评分人邀请') }}
        <i
          :style="showDetail3? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-down"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showDetail3= !showDetail3"
        />
      </div>
      <div v-show="showDetail3">
        <el-button v-if="!viewOnly" type="primary" @click="inviteRate">邀请评分</el-button>
        <el-table :data="reviewerRelList">

          <el-table-column label="审核内容" prop="configType">
            <template slot-scope="scope">
              <dict-tag :value="scope.row.configType" :type="DICT_TYPE.AUTH_SPOT_CHECK_COMMON_TYPE" />
            </template>
          </el-table-column>
          <el-table-column label="邀请评分人" prop="userId">
            <template slot-scope="scope">
              <el-select v-model="scope.row.userId" :disabled="!['uninvited',''].includes(scope.row.checkStatus) ||viewOnly" filterable>
                <el-option
                  v-for="(item,index) in getDictDatas(DICT_TYPE.COMMON_USERS)"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="checkStatus">
            <template slot-scope="scope">
              <dict-tag :value="scope.row.checkStatus" :type="DICT_TYPE.AUTH_SUPPLIER_SCORE_CHECK_STATUS" />
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button v-if="!['uninvited',''].includes(scope.row.checkStatus )&&!viewOnly" type="text" @click="restAudit(scope.row)">重置</el-button>
              <el-button v-if="!scope.row?.isProcessor && scope.row.checkStatus ==='submitted'&&!viewOnly" type="text" @click="returnVisible = true;backObj.scoreType = scope.row.configType;backObj.backReason = ''">退回</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('auth.onSiteAuditResults') }}
        <i
          :style="showDetail? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-down"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showDetail= !showDetail"
        />
      </div>
      <div v-show="showDetail">
        <el-form ref="siteAuditInfo0" :model="siteAuditInfo" :rules="standardRules" inline label-width="150px">
          <div>
            <el-form-item :label="$t('供应商是否接受邀请')" class="siteItem" prop="checkDate">
              <el-radio v-model="siteAuditInfo.extraInfo.acceptInvite" :label="true" :disabled="true">是</el-radio>
              <el-radio v-model="siteAuditInfo.extraInfo.acceptInvite" :label="false" :disabled="true">否</el-radio>

            </el-form-item>
          </div>

          <el-form-item :label="$t('auth.onSiteAuditDate')" class="siteItem" prop="checkDate">
            <show-or-edit
              :disabled="viewOnly"
              :value="siteAuditInfo.checkDate"
              type="Date"
            >
              <el-date-picker
                v-model="siteAuditInfo.checkDate"
                :disabled="viewOnly"
                :picker-options="pickerOptions"
                :placeholder="$t('order.selectDate')"
                placement="bottom-start"
                style="width: 100%;"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </show-or-edit>

          </el-form-item>
          <el-form-item :label="$t('auth.onSiteAuditTeamLeader')" class="siteItem" prop="checkLeader">
            <show-or-edit
              :disabled="viewOnly"
              :value="siteAuditInfo.checkLeader"
            >
              <el-input v-model="siteAuditInfo.checkLeader" :disabled="viewOnly" />

            </show-or-edit>
          </el-form-item>
          <el-form-item :label="$t('auth.onSiteAuditTeam')" class="siteItem" prop="checkGroup">
            <show-or-edit
              :disabled="viewOnly"
              :value="siteAuditInfo.checkGroup"
            >
              <el-input v-model="siteAuditInfo.checkGroup" :disabled="viewOnly" />

            </show-or-edit>
          </el-form-item>
          <div style="display: flex">
            <div style="flex: 0 0 33% ">
              <el-form-item :label="$t('auth.totalAuditScore')" class="siteItem" style="width: 100%">
                {{ siteAuditInfo.score }}
                <!--                <el-input v-model="siteAuditInfo.score" disabled />-->
              </el-form-item>
            </div>

            <div style="flex: 0 0 66% ">
              <el-form-item
                v-for="(item,index) in siteAuditInfo.siteAuditScore"
                :key="item.value"
                :label="item.label"
                :prop="`siteAuditScore.${index}.score`"
                class="siteItem"
                style="width: 33%"
              >
                <show-or-edit
                  :disabled="true"
                  :value="item.score"
                >
                  <el-input v-model.number="item.score" :disabled="viewOnly" style="width: 70%" type="number" @blur="onblurValue(item)" />

                </show-or-edit>
                <!--                <el-input-number v-model="item.score" :min="0" :precision="2" />-->
              </el-form-item>
            </div>
          </div>
          <span style="color: #ff4949; margin-right: 4px;position:relative;top:7px;font-size: 15px; font-weight: bolder; padding: 0 0 0 30px;">*</span>
          <el-form-item>
            <el-upload
              :action="uploadUrl"
              :class="'upload-demo'"
              :disabled="viewOnly"
              :file-list="siteAuditFileList"
              :headers="getBaseHeader()"
              :on-preview="onPreview"
              :on-remove="onRemove"
              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,siteAuditInfo.id,'check_report')"
              multiple
            >
              <el-button type="primary" :disabled="viewOnly">{{ $t('auth.uploadAuditReport') }}</el-button>
            </el-upload>
          </el-form-item>

          <div>
            <el-form-item :label="$t('auth.numberOfNonconformities')" />

            <el-form-item
              v-for="(item,index) in siteAuditInfo.siteAuditQuantity"
              :key="item.value"
              :label="item.label"
              :prop="`siteAuditQuantity.${index}.score`"
            >
              <show-or-edit
                :disabled="true"
                :value="item.score"
              >
                <el-input v-model.number="item.score" :disabled="viewOnly" style="width: 80px" type="number" @blur="onblurValue(item)" />

              </show-or-edit>
              <!--              <el-input-number v-model="item.score" :min="0" :precision="2" />-->
            </el-form-item>
            <el-form-item>
              <el-button
                v-has-permi="['auth:supplier-spot-check-step:back']"
                style="margin-left: 10px"
                plain
                :disabled="viewOnly"
                type="primary"
                @click="checkScar"
              >{{ $t('auth.createSCAR') }}</el-button>
            </el-form-item>
          </div>
          <span style="color: #ff4949; margin-right: 4px;position:relative;top:7px;font-size: 15px; font-weight: bolder; padding: 0 0 0 36px;" />

          <el-form-item
            class="siteItem"
            label="现场审核查验"
          >
            <i
              v-if="!viewOnly"
              class="el-icon-s-check"
              style="font-size: 25px;cursor:pointer;color:#4d93b9"
              @click="$router.push(`/auth/qa/${$route.query.no}?authId=${authId}&supplierId=${siteAuditInfo.supplierId}`)"
            />

          </el-form-item>
          <el-form-item
            class="siteItem"
            label="现场审核报告"
          >
            <i
              class="el-icon-document-checked"
              style="font-size: 25px;cursor:pointer;color:#4d93b9"
              @click="$router.push(`/auth/auditReport/${$route.query.no}?authId=${authId}&supplierId=${siteAuditInfo.supplierId}`)"
            />

          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('auth.onSiteAuditConclusion') }}
        <i
          :style="showDetail1? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-down"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showDetail1= !showDetail1"
        />
      </div>
      <el-form
        v-show="showDetail1"
        ref="siteAuditInfo1"
        :model="siteAuditInfo"
        :rules="standardRules"
        label-width="180px"
      >
        <el-form-item :label="$t('auth.onSiteAuditConclusion')">
          <show-or-edit
            :dict="DICT_TYPE.AUTH_SUPPLIER_CHECK_RESULT_TYPE"
            :disabled="true"
            :value="siteAuditInfo.conclusion"
          >
            <el-checkbox-group
              v-model="siteAuditInfo.conclusion"
            >
              <el-checkbox
                v-for="item in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_CHECK_RESULT_TYPE)"
                :key="item.value"
                :label="item.value"
                disabled
              > {{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </show-or-edit>

        </el-form-item>
        <el-form-item :label="$t('auth.addAdditionalDescription')">
          <show-or-edit
            :disabled="viewOnly"
            :value="siteAuditInfo.otherContent"
          >
            <el-input
              v-model="siteAuditInfo.otherContent"

              :rows="3"
              maxlength="200"
              show-word-limit
              type="textarea"
            />
          </show-or-edit>

        </el-form-item>
        <el-form-item :label="$t('auth.operatorOfNextNode')">
          <show-or-edit
            :dict="DICT_TYPE.COMMON_USERS"
            :disabled="viewOnly"
            :value="siteAuditInfo.nextOperateId"
          >
            <el-select
              v-model="siteAuditInfo.nextOperateId"
              :disabled="viewOnly"
              filterable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </show-or-edit>

        </el-form-item>
      </el-form>
      <div v-if="!viewOnly" style="text-align: center">
        <el-button class="bottomBtn" plain @click="closeTab">{{ $t('common.cancel') }}</el-button>
        <el-button v-has-permi="['auth:supplier-spot-check-step:back']" class="bottomBtn" plain type="danger" @click="backVisible = true">{{ $t('auth.return') }}</el-button>
        <el-button v-has-permi="['auth:supplier-spot-check-step:submit']" class="bottomBtn" type="primary" @click="submitSiteAudit">{{ $t('common.submit') }}</el-button>
        <el-button v-has-permi="['auth:supplier-spot-check-step:save']" class="bottomBtn" plain type="primary" @click="saveSiteAudit">{{ $t('common.save') }}</el-button>
      </div>
    </el-card>
    <el-dialog
      v-if="backVisible"
      :title="$t('auth.pleaseEnterTheReturnReason')"
      :visible.sync="backVisible"
      width="400px"
    >
      <el-input v-model="backReason" :rows="3" type="textarea" />
      <div slot="footer">
        <el-button @click="backVisible=false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitBack">{{ $t('common.submit') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="returnVisible"
      :title="$t('退回原因')"
      :visible.sync="returnVisible"
      width="400px"
    >
      <el-input v-model="backObj.backReason" :rows="3" type="textarea" />

      <div slot="footer">
        <el-button
          type="primary"
          plain
          @click="backAudit"
        >{{ $t('order.determine') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  backSupplierSpotCheckStep,
  deleteSupplierFileRel, inviteRatingSupplierSpotCheckStep, resetSupplierSpotCheckStep, rollbackSupplierSpotCheckStep,
  saveSupplierSpotCheckStep, sendEmailBySpotCheck,
  submitSupplierSpotCheckStep,
  supplierSpotCheckStep
} from '@/api/auth'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getBaseHeader } from '@/utils/request'
import { siteCheckHasRelSARecord, triggerNewScarSARecord } from '@/api/auth/supplier'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import dayjs from 'dayjs'
import {getConfig} from "@/utils/config";

export default {
  // 现场审核
  name: 'Newsiteaudit',
  components: {
    ShowOrEdit,
    authSupplier: () => import('../component/authSupplier')
  },
  props: ['viewOnly', 'authId'],
  data() {
    return {
      showDetail: true,
      showDetail1: true,
      showDetail2: true,
      showDetail3: true,
      siteAuditInfo: {
        authId: this.authId,
        supplierId: '',
        checkDate: '',
        checkLeader: '',
        checkGroup: null,
        score: null,
        checkResult: '',
        checkResultVersion: null,
        otherContent: null,
        sectionStatus: '',
        scoreVersion: null,
        acceptInvite: null,
        supplierProcessor: null,
        supplierAddress: null,
        supplierContact: null,
        supplierEmail: null,
        id: null,
        nextOperateId: '',
        dictRelList: [],
        fileRelList: [],
        checkItem: [],
        reviewerRelList: [],
        scoreConfigRelList: [], // 基础评分大项集合
        advancedSpotCheck: null,
        isSubmit: false,
        extraInfo: {
          authId: 0,
          relId: 0,
          acceptInvite: true,
          supplierProcessor: null,
          supplierAddress: null,
          supplierContact: null,
          supplierEmail: null,
          reserveCheckStartDate: '',
          reserveCheckEndDate: '',
          reserveSupplierProcessor: [],
          supplierRemark: '',
          id: 0,
          lastSendMailDate: '',
          createTime: null
        }
      },
      standardRules: {
        checkDate: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        checkGroup: [{ required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }],
        checkLeader: [{ required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }]
      },
      uploadUrl: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload',
      getBaseHeader,
      siteAuditFileList: [],
      backVisible: false,
      backReason: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      auditRule: {
        is_review: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        dateRange: [
          { validator: (rule, value, callback) => {
            if (!this.siteAuditInfo.extraInfo.reserveCheckStartDate) {
              callback(new Error('请填写完整'))
            } else if (!this.siteAuditInfo.extraInfo.reserveCheckEndDate) {
              callback(new Error('请填写完整'))
            } else if (this.siteAuditInfo.extraInfo.reserveCheckStartDate > this.siteAuditInfo.extraInfo.reserveCheckEndDate) {
              callback(new Error('开始时间不能大于结束时间'))
            } else {
              callback()
            }
          }, trigger: 'change' }
        ],
        checkItem: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        'extraInfo.reserveSupplierProcessor': [
          { required: true, message: '请选择', trigger: 'blur' }
        ]
      },
      checkList: [],
      writeFlag: false,
      lastSendActul: false,
      auditLoading: false,
      backObj: {
        scoreType: '',
        backReason: ''
      },
      returnVisible: false
    }
  },
  computed: {
    reviewerRelList() {
      return this.siteAuditInfo.reviewerRelList?.filter(a => this.siteAuditInfo.checkItem.includes(a.configType))
    }
  },
  mounted() {
    this.getSiteAudit()
    this.siteAuditInfo.siteAuditScore = getDictDatas(DICT_TYPE.AUTH_SUPPLIER_SPOT_CHECK_SCORE_ITEM_V2).map(item => {
      return {
        ...item,
        score: ''
      }
    })
    this.siteAuditInfo.siteAuditQuantity = getDictDatas(DICT_TYPE.AUTH_SUPPLIER_SPOT_CHECK_NOT_MATCH_ITEM_QUANTITY).map(item => {
      return {
        ...item,
        score: ''
      }
    })
  },
  methods: {
    dayjs,
    onblurValue(item) {
      if (item.score < 0) {
        item.score = 0
      }
      item.score =
        ('' + item.score) // 第一步转成字符串
          .replace(/[^\d^\.]+/g, '') // 第二步把不是数字，不是小数点的过滤掉
          .replace(/^0+(\d)/, '$1') // 第三步第一位0开头，0后面为数字，则过滤掉，取后面的数字
          .replace(/^\./, '0.') // 第四步如果输入的第一位为小数点，则替换成 0. 实现自动补全
          .match(/^\d*(\.?\d{0,2})/g)[0] || ''// 第五步最终匹配得到结果 以数字开头，只有一个小数点，而且小数点后面只能有0到2位小数
    },
    getSiteAudit() {
      supplierSpotCheckStep({
        authId: this.authId
      }).then(res => {
        this.siteAuditInfo.conclusion = []
        this.siteAuditInfo.conclusion.push(res.data.checkResult)
        res.data.extraInfo.reserveCheckStartDate = res.data.extraInfo.reserveCheckStartDate ? dayjs(res.data.extraInfo.reserveCheckStartDate).format('YYYY-MM-DD HH:mm:ss') : ''
        res.data.extraInfo.reserveCheckEndDate = res.data.extraInfo.reserveCheckEndDate ? dayjs(res.data.extraInfo.reserveCheckEndDate).format('YYYY-MM-DD HH:mm:ss') : ''
        res.data.scoreItem = res.data.dictRelList.filter(item => item.businessType === DICT_TYPE.AUTH_SUPPLIER_SPOT_CHECK_SCORE_ITEM_V2)
        res.data.notMatchItem = res.data.dictRelList.filter(item => item.businessType === DICT_TYPE.AUTH_SUPPLIER_SPOT_CHECK_NOT_MATCH_ITEM_QUANTITY)
        res.data.checkItem = res.data.reviewerRelList?.map(item => item.configType) || []
        res.data.reviewerRelList = getDictDatas(DICT_TYPE.AUTH_SPOT_CHECK_COMMON_TYPE).map(a => {
          if (res.data.reviewerRelList?.find(item => item.configType === a.value)) {
            return res.data.reviewerRelList.find(item => item.configType === a.value)
          } else {
            return {
              checkStatus: a.checkStatus || 'uninvited',
              configType: a.value,
              userId: null
            }
          }
        })
        this.siteAuditInfo.siteAuditScore.forEach(item => {
          item.score = Number(res.data.scoreItem.find(data => data.dictValue === item.value)?.value) || 0
        })
        this.siteAuditInfo.siteAuditQuantity.forEach(item => {
          item.score = Number(res.data.notMatchItem.find(data => data.dictValue === item.value)?.value) || 0
        })
        res.data.fileRelList.forEach(item => {
          item.fileRelList.forEach(a => {
            a.name = a.fileName
          })
        })
        this.siteAuditFileList = res.data.fileRelList.find(item => item.businessValue === 'check_report')?.fileRelList || []
        res.data.fileRelList.length = 0
        this.siteAuditInfo = { ...this.siteAuditInfo, ...res.data }
        this.siteAuditInfo.nextOperateId = res.data.nextOperateId || this.$store.getters.userId
        this.$nextTick(() => {
          this.$refs.siteAuditInfo0.clearValidate()
        })
      })
    },
    async saveSiteAudit(messageFlag = true) {
      this.siteAuditInfo.dictRelList = []

      // 处理评分项的configId赋值
      this.reviewerRelList.forEach(rel => {
        this.siteAuditInfo.scoreConfigRelList.filter(configRel => {
          if (configRel.configType === rel.configType) {
            rel.configId = configRel.id
          }
        })
      })
      const res = await saveSupplierSpotCheckStep({
        ...this.siteAuditInfo,
        reviewerRelList: this.reviewerRelList

      })
      this.siteAuditInfo.id = res.data
      this.getSiteAudit()
      if (messageFlag) {
        this.$message.success(this.$t('common.savedSuccessfully'))
      }
    },
    onSuccess(response, file, fileList, businessId, businessValue) {
      this.siteAuditInfo.fileRelList.push({
        authId: this.siteAuditInfo.authId,
        businessId,
        businessType: 'auth_supplier_upload_business_type',
        businessValue,
        fileId: response.data.id,
        fileName: response.data.name
      })
    },
    onRemove(file) {
      if (file.id) {
        deleteSupplierFileRel({ id: file.id }).then(_ => {
          this.$message.success(this.$t('common.delSuccess'))
        })
      } else {
        this.siteAuditInfo.fileRelList = this.siteAuditInfo.fileRelList.filter(a => a.fileId !== file?.response.data.id)
      }
    },
    onPreview(file) {
      if (file.filePath) {
        window.open(file.filePath)
      }
      if (file.response.data.url) {
        window.open(file.response.data.url)
      }
    },
    submitSiteAudit() {
      this.$confirm(this.$t('auth.confirmSubmission'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        let pass = true
        for (let i = 0; i < 2; i++) {
          this.$refs[`siteAuditInfo${i}`].validate(valid => {
            if (!valid) {
              pass = false
            }
          })
        }
        if (!pass) {
          this.$message.error(this.$t('auth.pleaseCompleteTheInformation'))
          return
        }

        this.siteAuditInfo.dictRelList = []

        // 处理评分项的configId赋值
        this.reviewerRelList.forEach(rel => {
          this.siteAuditInfo.scoreConfigRelList.filter(configRel => {
            if (configRel.configType === rel.configType) {
              rel.configId = configRel.id
            }
          })
        })
        submitSupplierSpotCheckStep({ ...this.siteAuditInfo,
          reviewerRelList: this.reviewerRelList,
          isSubmit: true
        }).then(res => {
          this.$message.success(this.$t('supplier.submittedSuccessfully'))
          this.$tab.closeOpenPage('/auth/authindex')
        })
      })
    },
    // 现场审核节点检查是否存在关联的SA单据
    // 【UFFF-1716】如单据无对应的SA单据则跳转至SA单据的新建页面,同时SA单据保存后会回写scarId至认证单据记录中。
    // 如果存在对应的scar单据，则再次点击按钮则跳转至scar的总览列表
    checkScar() {
      this.saveSiteAudit(false)
      siteCheckHasRelSARecord(this.siteAuditInfo.authId).then(res => {
        if (!res.data) {
          // 触发sa单据的生成。
          triggerNewScarSARecord(this.siteAuditInfo.authId).then(r => {
            this.$message.success(this.$t('supplier.submittedSuccessfully'))
            this.$router.push(`/scar/sa/${r.data.scarCode}?id=${r.data.scarId}`)
          })
        } else {
          this.$router.push(`/scar/scarindex`)
        }
      }).catch(_ => {

      })
    },
    submitBack() {
      if (!this.backReason) {
        this.$message.error(this.$t('auth.pleaseEnterTheReturnReason'))
        return
      }
      backSupplierSpotCheckStep(
        { authId: this.siteAuditInfo.authId,
          reason: this.backReason
        }).then(res => {
        this.backReason = ''
        this.backVisible = false
        this.$tab.closeOpenPage('/auth/authindex')
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
      })
    },
    // 关闭当前tab
    closeTab() {
      this.$tab.closeOpenPage('/auth/authindex')
    },
    submitAudit() {
      // 处理评分项的configId赋值
      this.$refs.siteAuditInfo.validate(valid => {
        if (valid) {
          this.reviewerRelList.forEach(rel => {
            this.siteAuditInfo.scoreConfigRelList.filter(configRel => {
              if (configRel.configType === rel.configType) {
                rel.configId = configRel.id
              }
            })
          })
          sendEmailBySpotCheck({ ...this.siteAuditInfo,
            reviewerRelList: this.reviewerRelList
          }).then(res => {
            this.getSiteAudit()
            this.$message.success(this.$t('supplier.submittedSuccessfully'))
          })
        }
      })
    },
    /**
     * 邀请评分
     */
    async inviteRate() {
      if (this.reviewerRelList?.length === 0) {
        this.$message.error(this.$t('评分项为空，请先选择至少一项评分项'))
        return
      }
      await this.saveSiteAudit(false)

      inviteRatingSupplierSpotCheckStep({
        authId: this.authId
      }).then(res => {
        this.getSiteAudit()
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
      })
    },
    backAudit() {
      rollbackSupplierSpotCheckStep({
        authId: this.authId,
        ...this.backObj
      }).then(res => {
        this.returnVisible = false
        this.getSiteAudit()
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
      })
    },
    showRest() {
      this.backVisible = true
    },
    async restAudit(row) {
      await this.saveSiteAudit(false)
      resetSupplierSpotCheckStep({
        authId: this.authId,
        scoreType: row.configType
      }).then(res => {
        this.getSiteAudit()
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.siteItem{
  width: 33%;
  margin-right: 0;
  ::v-deep .el-form-item__content{
    width: calc(100% - 160px);
  }}

.startStandard{
  //padding: 25px 15px;
}
.upload-demo{
  width: 400px;
}
.viewUpload{
  width: 400px;
  ::v-deep .el-upload--text{
    display: none;
  }
}
.commonCard{
  margin: 10px 0 ;
}
::v-deep .el-card__header{
  border-top: 3px solid #4996b8;
  border-bottom: none;
}
.mainTab{
  color: #4996b8;
  font-size: 16px;
  font-weight: 700;
}
.bottomBtn{
  width: 80px;
}
</style>
