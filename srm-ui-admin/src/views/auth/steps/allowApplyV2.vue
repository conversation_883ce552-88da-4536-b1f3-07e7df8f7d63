<template>
  <div class="allowApply">
    <authSupplier
      :auth-id="authId"
    />
    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('auth.supplierCertificationProcessReport') }}
        <i
          :style="showDetail4? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-down"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showDetail4= !showDetail4"
        />
      </div>
      <el-descriptions v-show="showDetail4" :column="4" label-class-name="commonLabel">
        <el-descriptions-item :label="$t('auth.onSiteAuditReport')" :span="4">
          <el-upload
            :action="uploadUrl"
            :class="viewOnly ? 'viewUpload':'upload-demo'"
            :disabled="viewOnly"
            :headers="getBaseHeader()"
            :on-preview="onPreview"
            :on-remove="onRemove"
            :file-list="applyInfo.checkReport"
            :on-success="(response, file, fileList)=>onSuccess(response, file, fileList, null,'check_report')"
            multiple
            style="margin-left: 10px;flex: none;display: flex"
          >
            <el-button v-if="!viewOnly" size="mini" plain type="primary">{{
                $t('点击上传')
              }}
            </el-button>
          </el-upload>

        </el-descriptions-item>
        <el-descriptions-item :label="$t('其他审核报告')" :span="4">
          <el-upload
            :action="uploadUrl"
            :class="viewOnly ? 'viewUpload':'upload-demo'"
            :disabled="viewOnly"
            :headers="getBaseHeader()"
            :on-preview="onPreview"
            :on-remove="onRemove"
            :file-list="applyInfo.assessmentList"
            :on-success="(response, file, fileList)=>onSuccess(response, file, fileList, null,'allow_apply_v2_other_report')"
            multiple
            style="margin-left: 10px;flex: none;display: flex"
          >
            <el-button v-if="!viewOnly" size="mini" plain type="primary">{{
                $t('点击上传')
              }}
            </el-button>
          </el-upload>

        </el-descriptions-item>

      </el-descriptions>
    </el-card>
    <el-card class="commonCard">
      <div id="requirementsFiles" slot="header" class="mainTab">
        {{ $t('auth.requirementsOfSupplierQualificationAgreement') }}
        <i
          :style="showDetail1? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-down"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showDetail1= !showDetail1"
        />
        <el-button
          v-if="!viewOnly"
          v-has-permi="['auth:supplier-allow-apply-step:back']"
          style="float: right;margin-right: 15px"
          type="primary"
          plain
          @click="followUp"
        >{{ $t('auth.followUpSuppliers') }}
        </el-button>
      </div>
      <el-table
        v-show="showDetail1"
        :data="applyInfo.protocolConfigList"
      >
        <el-table-column :label="$t('auth.nameOfQualificationAgreement')" prop="folderName" />
        <el-table-column :label="$t('auth.attachmentBrowsing')" prop="isSigned">
          <template #default="scope">
            <div v-for="(item, index) in scope.row.docRepositoryFileList" :key="index">
              <el-button type="text" @click="onPreview(item)">
                {{ item.fileName }}
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('auth.uploadAttachments')" prop="isSigned">
          <template #default="scope">
            <el-button v-if="!viewOnly" type="text" @click="showUpload(scope.row)">{{ $t('common.upload') }}</el-button>
          </template>
        </el-table-column>
        <el-table-column :label="$t('auth.whetherToSign')" prop="isSigned">
          <template #default="scope">
            {{ scope.row.isSigned ? $t('auth.yes') : $t('auth.no') }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('auth.defaultremarks')" prop="isSigned">
          <template #default="scope">
            <!--            此处产品要求付款资料和营业执照无需上传缺省报告  2022.12.21-->
            <el-upload
              v-if="scope.row.docRepositoryFileList.length===0 && scope.row.noUploadFolderIdList.includes(scope.row.folderId)"
              :action="uploadUrl"
              :class="viewOnly ? 'viewUpload':'upload-demo'"
              :disabled="viewOnly"
              :headers="getBaseHeader()"
              :on-preview="onPreview"
              :on-remove="onRemove"
              :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,applyInfo.id,'default_report',scope.row)"
              :show-file-list="false"
              multiple
            >
              <el-button v-if="!viewOnly" type="text">{{ $t('auth.uploadDefaultReport') }}</el-button>
            </el-upload>
            <div v-for="(item, index) in scope.row.fileRelList" :key="index">
              <el-button type="text" @click="onPreview(item)">
                {{ item.fileName }}
              </el-button>
            </div>
          </template>
        </el-table-column>
<!--        仅展示-->
        <el-table-column :label="$t('邀请其他人上传')" prop="nickName">
          <template #default="scope">
            <el-button :disabled="viewOnly" type="text" @click="showInvite(scope.row)">
              <dict-tag v-if="scope.row.userId" :type="DICT_TYPE.COMMON_USERS" :value="scope.row.userId" @click="showInvite(scope.row)"/>
              <span v-else-if="!viewOnly && !scope.row.userId">{{ $t('邀请上传') }}</span>
              </el-button>
          </template>
        </el-table-column>

      </el-table>
    </el-card>

    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('auth.supplierAccessApplicationLoginInformation') }}
        <i
          :style="showDetail0? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-down"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showDetail0= !showDetail0"
        />
      </div>
      <el-form
        v-show="showDetail0"
        ref="applyInfo"
        :model="applyInfo"
        inline
        label-width="170px"
      >
        <div>
          <el-form-item
            :label="$t('auth.loginType')"
            :rules="{
              required: true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }"
            prop="loginType"
          >
            <show-or-edit
              :value="applyInfo.loginType"
              :disabled="viewOnly"
              :dict="applyInfo.fastTemporary?DICT_TYPE.AUTH_SUPPLIER_TEMP_SUPPLIER_LOGIN_TYPE:
                DICT_TYPE.AUTH_SUPPLIER_CHECK_RESULT_TYPE"
            >
              <el-radio-group v-model="applyInfo.loginType">
                <div
                  v-if="!applyInfo.fastTemporary"
                >
                  <el-radio
                    v-for="item in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_CHECK_RESULT_TYPE)"
                    :key="item.value"
                    :label="item.value"
                    :value="item.value"
                  > {{ item.label }}
                  </el-radio>
                </div>
                <div v-else>
                  <el-radio
                    v-for="item in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_TEMP_SUPPLIER_LOGIN_TYPE)"
                    :key="item.value"
                    :label="item.value"
                    :value="item.value"
                  > {{ item.label }}
                  </el-radio>
                </div>

              </el-radio-group>

            </show-or-edit>
          </el-form-item>
        </div>
        <el-form-item
          :label="$t('supplier.shortNameOfSupplier')"
          :rules="{
            required: true,
            message: $t('common.pleaseEnter'),
            trigger: 'blur'
          }"
          class="loginInfoItem"
          prop="nameShort"
        >
          <show-or-edit
            :value="applyInfo.nameShort"
            :disabled="viewOnly"
          >
            <el-input v-model="applyInfo.nameShort " />

          </show-or-edit>
        </el-form-item>
        <el-form-item
          v-if="applyInfo.loginType === 'trial_supplier'"
          :label="$t('auth.trialStartDate')"
          class="loginInfoItem"
        >
          <show-or-edit
            :value="applyInfo.time.join('-')"
            :disabled="viewOnly"
          >
            <el-date-picker
              v-model="applyInfo.time"
              :disabled="viewOnly"
              :placeholder="$t('order.selectDate')"
              class="deliveryHeadItem"
              placement="bottom-start"
              type="daterange"
              value-format="yyyy-MM-dd"
            />
          </show-or-edit>

        </el-form-item>
        <el-form-item
          v-if="applyInfo.loginType === 'temp_supplier'"
          :label="$t('auth.temporaryStartDate')"
          class="loginInfoItem"
        >
          <show-or-edit
            :value="applyInfo.time.join('-')"
            :disabled="viewOnly"
          >
            <el-date-picker
              v-model="applyInfo.time"
              :disabled="viewOnly"
              :placeholder="$t('order.selectDate')"
              class="deliveryHeadItem"
              placement="bottom-start"
              type="daterange"
              value-format="yyyy-MM-dd"
            />
          </show-or-edit>

        </el-form-item>
        <el-card v-for="(item,index) in applyInfo.supplierCompanyRelList" :key="index" class="loginInfoCard">
          <el-form-item
            :label="$t('order.company')"
            :prop="`supplierCompanyRelList.${index}.companyId`"

            :rules="{
              required: true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }"
            class="loginInfoItem"
          >
            <show-or-edit
              :value="item.companyId"
              :dict="DICT_TYPE.COMMON_COMPANY"
              :disabled="viewOnly"
            >
              <el-select
                v-model="item.companyId"
                :disabled="viewOnly"
                :placeholder="$t('auth.pleaseSelectADataSource')"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_COMPANY)"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>
            </show-or-edit>

          </el-form-item>
          <el-form-item
            :label="$t('supplier.reconciliationAccount')"
            :prop="`supplierCompanyRelList.${index}.reconciliationAccount`"

            :rules="{
              required: true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }"
            class="loginInfoItem"
          >
            <show-or-edit
              :value="item.reconciliationAccount"
              :dict="DICT_TYPE.SUPPLIER_CONTROLLER_ACCOUNT"
              :disabled="viewOnly"
            >
              <el-select
                v-model="item.reconciliationAccount"
                :disabled="viewOnly"
                :placeholder="$t('common.pleaseSelect')"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_CONTROLLER_ACCOUNT)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>

          </el-form-item>
          <el-form-item
            :label="$t('auth.supplierAccountGroup')"
            :prop="`supplierCompanyRelList.${index}.accountGroup`"
            :rules="{
              required: true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }"
            class="loginInfoItem"
          >
            <show-or-edit
              :value="item.accountGroup"
              :disabled="viewOnly"
              :dict="DICT_TYPE.SUPPLIER_ACCOUNT_GROUP"
            >
              <el-select
                v-model="item.accountGroup	"
                :disabled="viewOnly"
                :placeholder="$t('common.pleaseSelect')"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_ACCOUNT_GROUP)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>

            <div v-if="!viewOnly" style="float: right;margin-right: 20px">

              <i
                class="el-icon-circle-plus"
                style="margin-left:10px;font-size: 18px;cursor: pointer"
                @click="applyInfo.supplierCompanyRelList.push({
                  accountGroup: '',
                  // authId,
                  companyId: '',
                  // createTime: '',
                  // creator: '',
                  // deleted: true,
                  // id: 0,
                  manufacturerNature: '',
                  reconciliationAccount: '',
                })"
              />
              <i
                class="el-icon-remove"
                style="margin-left:10px;font-size: 18px;cursor: pointer"
                @click="applyInfo.supplierCompanyRelList.length===1?
                  applyInfo.supplierCompanyRelList= [
                    {
                      accountGroup: '',
                      // authId,
                      companyId: '',
                      // createTime: '',
                      // creator: '',
                      // deleted: true,
                      // id: 0,
                      manufacturerNature: '',
                      reconciliationAccount: '',
                    }
                  ]
                  :applyInfo.supplierCompanyRelList.splice(index,1)"
              />
            </div>
          </el-form-item>
          <el-form-item
            :label="$t('auth.natureOfManufacturer')"
            :prop="`supplierCompanyRelList.${index}.manufacturerNature`"
            :rules="{
              required: true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }"
            class="loginInfoItem"
          >
            <show-or-edit
              :value="item.manufacturerNature"
              :dict="DICT_TYPE.AUTH_SUPPLIER_MANUFACTURER_NATURE"
              :disabled="viewOnly"
            >
              <el-select
                v-model="item.manufacturerNature	"
                :disabled="viewOnly"
                :placeholder="$t('common.pleaseSelect')"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_MANUFACTURER_NATURE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>
        </el-card>
        <el-card v-for="(item,index) in applyInfo.supplierPurchaseOrgRelList" :key="index" class="loginInfoCard">
          <el-form-item
            :label="$t('supplier.purchasingOrganization')"
            :prop="`supplierPurchaseOrgRelList.${index}.orgId`"
            :rules="{
              required: true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }"
            class="loginInfoItem"
          >
            <show-or-edit
              :disabled="viewOnly"
              :value="item.orgId"
              :dict="DICT_TYPE.COMMON_PURCHASEORG"
            >
              <el-select
                v-model="item.orgId	"
                :disabled="viewOnly"
                :placeholder="$t('auth.pleaseSelectAPurchasingOrganization')"
                clearable
              >
                <el-option
                  v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG)"
                  :key="item.value"
                  :label="item.name"
                  :value="item.id"
                />

              </el-select>
            </show-or-edit></el-form-item>
          <el-form-item
            :label="$t('material.procurementGroup')"
            :prop="`supplierPurchaseOrgRelList.${index}.pgId`"
            :rules="{
              required: true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }"
            class="loginInfoItem"
          >
            <show-or-edit
              :value="item.pgId"
              :dict="DICT_TYPE.SYSTEM_PURCHASE_GROUP"
              :disabled="viewOnly"
            >
              <el-select
                v-model="item.pgId	"
                :disabled="viewOnly"
                :placeholder="$t('common.pleaseSelect')"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_PURCHASE_GROUP)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>

          </el-form-item>
          <el-form-item
            :label="$t('auth.invoiceBasedReceiptVerification')"
            :prop="`supplierPurchaseOrgRelList.${index}.basedOnInvoice`"
            :rules="{
              required: true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }"
            class="loginInfoItem"
          >
            <el-checkbox v-model="item.basedOnInvoice" :disabled="viewOnly" />
            <!--            <el-radio-group v-model="">-->
            <!--              <el-radio :label="true">是</el-radio>-->
            <!--              <el-radio :label="false">否</el-radio>-->
            <!--            </el-radio-group>-->
            <div v-if="!viewOnly" style="float: right;margin-right: 20px">
              <i
                class="el-icon-circle-plus"
                style="margin-left:10px;font-size: 18px;cursor: pointer"
                @click="applyInfo.supplierPurchaseOrgRelList.push({
                  basedOnInvoice: '',
                  orgId: '',
                  pgId: '',
                  supplierGrade: '',
                  supplierTier: ''
                })"
              />
              <i
                class="el-icon-remove"
                style="margin-left:10px;font-size: 18px;cursor: pointer"
                @click="applyInfo.supplierPurchaseOrgRelList.length===1?
                  applyInfo.supplierPurchaseOrgRelList= [
                    {
                      basedOnInvoice: '',
                      orgId: '',
                      pgId: '',
                      supplierGrade: '',
                      supplierTier: ''
                    }
                  ]
                  :applyInfo.supplierPurchaseOrgRelList.splice(index,1)"
              />
            </div>

          </el-form-item>
          <el-form-item
            :label="$t('auth.supplierGrade')"
            :prop="`supplierPurchaseOrgRelList.${index}.supplierGrade`"
            :rules="{
              required: true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }"
            class="loginInfoItem"
          >
            <show-or-edit
              :disabled="viewOnly"
              :value="item.supplierGrade"
              :dict="DICT_TYPE.AUTH_SUPPLIER_SUPPLIER_GRADE"
            >
              <el-select
                v-model="item.supplierGrade"
                :disabled="viewOnly"
                :placeholder="$t('common.pleaseSelect')"
                clearable
              >
                <el-option
                  v-for="item in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_SUPPLIER_GRADE)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />

              </el-select>
            </show-or-edit>

          </el-form-item>
          <el-form-item
            :label="$t('supplier.supplierEchelonLevel')"
            :prop="`supplierPurchaseOrgRelList.${index}.supplierTier`"
            :rules="{
              required: true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }"
            class="loginInfoItem"
          >
            <show-or-edit
              :value="item.supplierTier"
              :dict="DICT_TYPE.SUPPLIER_TIER_LEVEL"
              :disabled="viewOnly"
            >
              <el-select
                v-model="item.supplierTier"
                :disabled="viewOnly"
                :placeholder="$t('common.pleaseSelect')"
                clearable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_TIER_LEVEL)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </show-or-edit>

          </el-form-item>
        </el-card>
      </el-form>
    </el-card>

    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('auth.multiDepartmentReviewInvitation') }}
        <i
          :style="showDetail2? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-down"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showDetail2= !showDetail2"
        />
      </div>
      <show-or-edit
        :value="applyInfo.nextOperateId"
        :disabled="viewOnly"
        :dict="DICT_TYPE.COMMON_USERS"
      >
        <el-select
          v-show="showDetail2"
          v-model="applyInfo.nextOperateId"
          :disabled="viewOnly"
          :multiple-limit="3"
          filterable
          multiple
          style="width: 500px"
        >
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </show-or-edit>

    </el-card>

    <el-card class="commonCard">
      <div slot="header" class="mainTab">
        {{ $t('auth.otherInstructionsForAdmissionApplication') }}
        <el-button
          style="margin-left: 8px;font-size: inherit;text-decoration:underline;font-weight:bolder"
          type="text"
          @click="openDataOperate"
        >
          {{ $t('common.operationRecord') }}
        </el-button>
        <i
          :style="showDetail3? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-down"
          style="font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: bold;"
          @click="showDetail3= !showDetail3"
        />
      </div>
      <el-form
        v-show="showDetail3"
        label-position="left"
        label-width="180px"
      >
        <el-form-item :label="$t('auth.addAdditionalDescription')" prop="otherContent">
          <show-or-edit
            :value="applyInfo.otherContent"
            :disabled="viewOnly"
          >
            <el-input
              v-model="applyInfo.otherContent"
              :disabled="viewOnly"
              :rows="3"
              maxlength="200"
              show-word-limit
              type="textarea"
            />
          </show-or-edit>

        </el-form-item>
      </el-form>
      <div v-if="!viewOnly" style="text-align: center">
        <el-button class="bottomBtn" plain @click="closeTab">{{ $t('common.cancel') }}</el-button>
        <el-button
          v-has-permi="['auth:supplier-allow-apply-step:back']"
          class="bottomBtn"
          plain
          type="danger"
          @click="backVisible =true"
        >{{ $t('auth.return') }}
        </el-button>
        <el-button
          v-has-permi="['auth:supplier-allow-apply-step:submit']"
          class="bottomBtn"
          type="primary"
          @click="submitAllowApply"
        >{{ $t('common.submit') }}
        </el-button>
        <el-button
          v-has-permi="['auth:supplier-allow-apply-step:save']"
          class="bottomBtn"
          plain
          type="primary"
          @click="saveAllowApply"
        >{{ $t('common.save') }}
        </el-button>
      </div>

    </el-card>

    <el-dialog
      v-if="backVisible"
      width="400px"
      :title="$t('auth.pleaseEnterTheReturnReason')"
      :visible.sync="backVisible"
    >
      <el-input v-model="backReason" :rows="3" maxlength="200" show-word-limit type="textarea" />
      <div slot="footer">
        <el-button @click="backVisible=false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitBack">{{ $t('common.submit') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="uploadVisible"
      :title="$t('common.uploadFile')"
      :visible.sync="uploadVisible"
      width="400px"
    >
      <el-form ref="uploadForm" :model="uploadInfo" :rules="uploadRules" inline label-width="110px">
        <el-form-item
          :label="$t('supplier.permanentlyValid')"
          prop="alwaysValid"
        >
          <el-radio-group v-model="uploadInfo.alwaysValid">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="uploadInfo.alwaysValid ==='false'"
          :label="$t('supplier.validUntil')"
          prop="expireDate"
        >
          <el-date-picker
            v-model="uploadInfo.expireDate"
            type="date"
          />
        </el-form-item>
      </el-form>
      <el-upload
        ref="upload"
        :action="uploadUrl"
        :auto-upload="false"
        :class="viewOnly ? 'viewUpload':'upload-demo'"
        :headers="getBaseHeader()"
        :on-success="(response, file, fileList)=>onDocSuccess(response, file, fileList, uploadInfo.folderId, uploadInfo)"
        class="upload-demo"
        drag
        multiple
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
      </el-upload>
      <div slot="footer">
        <el-button type="primary" @click="submitUpload">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>

    <!--    操作记录组件-->
    <authDataOperate ref="authDataOperate" :auth-id="authId" />

    <!--    邀请其他人上传dialog-->
    <el-dialog :title="$t('邀请其他人上传')" :visible.sync="showInviteUser" append-to-body width="500px">
      <el-form ref="form" :model="inviteUserForm" label-width="80px">
        <el-form-item :label="$t('supplier.user')" prop="userId">
          <el-select v-model="inviteUserForm.userId" class="searchValue" filterable>
            <el-option v-for="item in userList" :key="item.id" :label="item.nickname" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  backSupplierAllowApplyStep,
  deleteSupplierFileRel,
  getSupplierAllowApplyStep,
  pushSupplierAllowApplyStep,
  saveSupplierAllowApplyStep,
  submitSupplierAllowApplyStep,
  saveSupplierAllowApplyDocFolderUserRel
} from '@/api/auth'
import { getBaseHeader } from '@/utils/request'
import { repositoryFileBind } from '@/api/supplier/docRepository'
import ShowOrEdit from '@/components/ShowOrEdit'
import { DICT_TYPE } from '@/utils/dict'
import { getUsersCache } from "@/api/system/user";
import {getConfig} from "@/utils/config";
export default {
  // 准入申请
  name: 'Allowapply',
  components: {
    authSupplier: () => import('../component/authSupplier'),
    authDataOperate: () => import('../component/dataOperate'),
    ShowOrEdit
  },
  props: ['viewOnly', 'authId'],
  data() {
    return {
      showInviteUser: false,
      // 表单参数
      inviteUserForm: {},
      userList: [],
      showDetail0: true,
      showDetail1: true,
      showDetail2: true,
      showDetail3: true,
      showDetail4: true,
      applyInfo: {
        time: '',
        checkReport: [],
        assessmentList: [],
        fileRelList: []
      },
      uploadUrl: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload',
      getBaseHeader,
      backVisible: false,
      backReason: '',
      uploadVisible: false,
      uploadInfo: {
        alwaysValid: 'false'
      },
      uploadRules: {
        alwaysValid: [{ required: true, message: this.$t('common.pleaseSelect'), trigger: 'change' }],
        expireDate: [{ required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }]
      },
      riskVisible: false
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    }
  },
  mounted() {
    this.init()
    this.getUserList()
  },
  methods: {
    /** 表单重置 */
    reset() {
      this.form = {
        nickName: ''
      }
      this.resetForm('form')
    },
    /** 取消按钮 */
    cancel() {
      this.showInviteUser = false
      this.reset()
    },
    showInvite(item) {
      this.showInviteUser = true
      this.inviteUserForm = { ...this.inviteUserForm, ...item }
    },
    submitForm() {
      this.applyInfo.protocolConfigList.forEach(i => {
        if (i.folderId === this.inviteUserForm.folderId) {
          i.nickName = this.inviteUserForm.nickName
          i.userId = this.inviteUserForm.userId

          // 保存人员关系
          saveSupplierAllowApplyDocFolderUserRel({
            authId: this.authId,
            folderId: i.folderId,
            userId: i.userId,
            nickName: i.nickName,
          })
        }
      })
      this.cancel()
    },
    getUserList() {
      getUsersCache({
        status: 0,
        isExternal: 1
      }).then(response => {
        this.userList = []
        this.userList.push(...response.data)
      })
    },
    init() {
      getSupplierAllowApplyStep({
        authId: this.authId
      }).then(res => {
        res.data.time = [res.data.startDate || '', res.data.endDate || '']
        const categoryFileList = res.data.fileRelList.find(item => item.businessValue === 'supplier_category')?.fileRelList || []
        const categoryFileRequestList = res.data.fileRelList.find(item => item.businessValue === 'request_match_report')?.fileRelList || []
        res.data.fileRelList.forEach(item => {
          item.fileRelList.forEach(a => {
            a.name = a.fileName
          })
        })
        res.data.categoryRelList.forEach(item => {
          item.checkIdList = item.checkIdList.at(0)
          item.fileList = categoryFileList.filter(file => file.businessId === item.categoryId)
          item.fileRequestList = categoryFileRequestList.filter(file => file.businessId === item.categoryId)
        })
        if (res.data.supplierCompanyRelList.length === 0) {
          res.data.supplierCompanyRelList.push(
            {
              accountGroup: '',
              companyId: '',
              manufacturerNature: '',
              reconciliationAccount: ''
            }
          )
        }
        if (res.data.supplierPurchaseOrgRelList.length === 0) {
          res.data.supplierPurchaseOrgRelList.push(
            {
              basedOnInvoice: '',
              orgId: '',
              pgId: '',
              supplierGrade: '',
              supplierTier: ''
            }
          )
        }
        res.data.nextOperateId = res.data.supplierApproveList.map(item => item.userId)
        res.data.checkReport = res.data.fileRelList.find(item => item.businessValue === 'check_report')?.fileRelList || []
        res.data.assessmentList = res.data.fileRelList.find(item => item.businessValue === 'allow_apply_v2_other_report')?.fileRelList || []
        res.data.fileRelList.length = 0
        res.data.protocolConfigList.forEach(i => i.nickName = '')

        this.applyInfo = { ...this.applyInfo, ...res.data }
        // this.applyInfo.nextOperateId = res.data.nextOperateId.length ? res.data.nextOperateId : [this.$store.getters.userId]
        // 反馈不要默认
        this.$nextTick(() => {
          this.$refs.applyInfo.clearValidate()
        })
      })
    },
    saveAllowApply() {
      this.applyInfo.startDate = this.applyInfo.time.at(0)
      this.applyInfo.endDate = this.applyInfo.time.at(1)
      saveSupplierAllowApplyStep({
        ...this.applyInfo,
        supplierApproveList: this.applyInfo.nextOperateId.map((item, index) => {
          return {
            userId: item,
            sort: index
          }
        }),
        supplierId: this.applyInfo.supplierId,
        isSubmit: false
      }).then(res => {
        this.applyInfo.id = res.data
        this.init()
        this.$message.success(this.$t('common.savedSuccessfully'))
      })
    },
    submitAllowApply() {
      this.$confirm(this.$t('auth.confirmSubmission'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        this.$refs.applyInfo.validate(valid => {
          const isUnqualified = this.applyInfo.loginType === 'disqualification_supplier'
          if (isUnqualified) {
            // 不合格供应商无需校验必填
            valid = true
          }
          if (valid) {
            if (this.applyInfo.supplierPurchaseOrgRelList.some(item => !item.basedOnInvoice) && !isUnqualified) {
              this.$message.error(this.$t('auth.pleaseSelectReceiptVerificationBasedOnInvoice'))
              return
            }
            if (!this.applyInfo.protocolConfigList.every(item => item.docRepositoryFileList.length || item.fileRelList.length) && !isUnqualified) {
              this.$message.error(this.$t('auth.pleaseUploadFilesOrDefaultReports'))
              document.getElementById('requirementsFiles').scrollIntoView()
              return
            }
            this.applyInfo.startDate = this.applyInfo.time.at(0)
            this.applyInfo.endDate = this.applyInfo.time.at(1)
            submitSupplierAllowApplyStep({
              ...this.applyInfo,
              supplierApproveList: this.applyInfo.nextOperateId.map((item, index) => {
                return {
                  userId: item,
                  sort: index
                }
              }),
              supplierId: this.applyInfo.supplierId,
              isSubmit: true
            }).then(res => {
              this.$message.success(this.$t('supplier.submittedSuccessfully'))
              this.$tab.closeOpenPage('/auth/authindex')
            })
          } else {
            document.getElementById('loginInfo').scrollIntoView()
          }
        })
      })
    },
    onSuccess(response, file, fileList, businessId, businessValue, item) {
      this.applyInfo.fileRelList.push({
        authId: this.applyInfo.authId,
        businessId,
        businessType: 'auth_supplier_upload_business_type',
        businessValue,
        fileId: response.data.id,
        fileName: response.data.name,
        folderId: item?.folderId
      })
      if (item) {
        item.fileRelList.push({
          fileName: response.data.name,
          filePath: response.data.url
        })
      }
    },
    onRemove(file) {
      if (file.id) {
        deleteSupplierFileRel({ id: file.id }).then(_ => {
          this.$message.success(this.$t('common.delSuccess'))
        })
      } else {
        this.applyInfo.fileRelList = this.applyInfo.fileRelList.filter(a => a.fileId !== file?.response.data.id)
      }
    },
    onPreview(file) {
      if (file.filePath) {
        window.open(file.filePath)
      }
      if (file?.response?.data.url) {
        window.open(file.response.data.url)
      }
    },
    onDocSuccess(response, file, fileList, folderId, item) {
      repositoryFileBind({
        alwaysValid: item.alwaysValid,
        expireDate: item.expireDate,
        files: [{
          fileId: response.data.id,
          name: file.name,
          size: file.size
        }],
        folderId,
        supplierId: this.applyInfo.supplierId
      }).then(res => {
        const temp = this.applyInfo.protocolConfigList.find(item => item.folderId === folderId)
        temp.isSigned = true
        if (item.docRepositoryFileList.length) {
          item.docRepositoryFileList[0].fileName = response.data.name
          item.docRepositoryFileList[0].filePath = response.data.url
          item.docRepositoryFileList[0].fileId = response.data.id
        } else {
          item.docRepositoryFileList.push({
            fileId: response.data.id,
            fileName: response.data.name,
            filePath: response.data.url
          })
        }
        this.$message.success(this.$t('common.uploadSucceeded'))
      })
    },
    followUp() {
      pushSupplierAllowApplyStep({
        authId: this.applyInfo.authId,
        supplierId: this.applyInfo.supplierId
      }).then(res => {
        this.$message.success(this.$t('rfq.followUpSuccess'))
      })
    },
    submitBack() {
      if (!this.backReason) {
        this.$message.error(this.$t('auth.pleaseEnterTheReturnReason'))
        return
      }
      backSupplierAllowApplyStep(
        {
          authId: this.applyInfo.authId,
          reason: this.backReason
        }).then(res => {
        this.backReason = ''
        this.backVisible = false
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
        this.$tab.closeOpenPage('/auth/authindex')
      })
    },
    // 打开操作记录弹框
    openDataOperate() {
      this.$refs.authDataOperate.doGetDataOperateRecords()
    },
    showUpload(item) {
      this.uploadVisible = true
      this.uploadInfo = { ...this.uploadInfo, ...item }
    },
    submitUpload() {
      this.$refs.uploadForm.validate(
        (valid) => {
          if (valid) {
            this.$refs.upload.submit()
            this.uploadVisible = false
          }
        }
      )
    },
    // 关闭当前tab
    closeTab() {
      this.$tab.closeOpenPage('/auth/authindex')
    },
    downloadReport(url) {
      window.open(url)
    }
  }
}
</script>

<style lang="scss" scoped>
.bottomBtn {
  width: 80px;
}

.loginInfoCard {
  margin-bottom: 10px;

  ::v-deep .el-card__body {
    padding: 10px 0;
  }
}

.viewUpload {
  ::v-deep .el-upload--text {
    display: none;
  }
}

.loginInfoItem {
  width: 33%;
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 170px);
  }
}

::v-deep .el-card__header {
  border-top: 3px solid #4996b8;
  border-bottom: none;
}

.mainTab {
  color: rgba(73, 150, 184, 0.99);
  font-size: 16px;
  font-weight: 700;
}

.commonCard {
  margin: 10px 0;
}

::v-deep .commonLabel {
  width: 120px;
  font-size: 14px;
}

.upload-demo {
}

::v-deep label {
  font-size: 14px;
  font-weight: normal;
}
.tqcdmHome {
  padding: 25px 15px;
  &-tag{
    font-size: 14px;
    font-weight: bold;
    margin: 15px 0 15px 12px;
    position: relative;
  }
  &-tag::before{
    content: "";
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: #4d93b9;
  }
}
.circle {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background-color: white;
  font-size: 20px;
}

.default{
  border: 2px solid #939393;
  //background-color: #F9F9F9;
}
.upload-demo{
  ::v-deep .el-upload-list__item:first-child{
    margin-top: 0;
  }
  ::v-deep ul{
    margin-left: 40px;
  }
}
</style>
