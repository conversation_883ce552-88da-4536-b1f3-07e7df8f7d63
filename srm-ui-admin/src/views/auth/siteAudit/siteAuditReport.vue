<template>
  <div v-loading="loading" style="display: flex;justify-content: center;">
    <div class="report">
      <div style="margin-bottom: 30px;font-size: 20px">
        <span style="font-weight: bold;margin-left: 10px">{{ $t('auth.onSiteAuditReport') }}</span>
        <span style="margin: 0 10px;color:#929292 ;">|</span>

        <span>{{ supplierInfo.name }}</span>
      </div>
      <div style="display: flex">
        <div :class="{auditTab:true, active:activeAudit}" @click="activeAudit =!activeAudit">
          <span>{{ $t('auth.findingsOfAudit') }}</span>
        </div>
        <div :class="{auditTab:true, active:!activeAudit}" @click="activeAudit =!activeAudit">
          <span>{{ $t('auth.reviewInformation') }}</span>
        </div>

      </div>
      <div v-show="activeAudit" style="background: #fff;padding: 20px">
        <span class="title_tag">
          <span class="title_tag_text">|</span>
          <span>{{ $t('auth.inspectionItems') }}</span>
        </span>

        <el-row style="margin-top: 10px;">
          <div class="navBox">
            <div
              v-for="(item,index) in navList"
              :key="index"
              :class="['navItem', navActive === item.id ? 'active' : '']"
              @click="activeTab(item.id, item.id)"
            >
              <span style="font-size: 17px">
                {{ item.label }}
                <span
                  style="color: #565656;font-size: 14px"
                >
                  {{ item.num }}
                </span>
              </span>
            </div>
          </div>
        </el-row>

        <el-table :data="filterQuestion" style="border-radius: 5px;margin-top: 15px">
          <el-table-column :label="$t('auth.subject')" prop="question" width="400">
            <template #default="scope">
              <div style="display: flex">
                <div style="flex: 0 0 5%">
                  <span style="margin-right: 3px">{{ scope.row.itemNo }}</span>

                </div>

                <div style="flex: 0 1 95%">

                  <div>
                    <span>{{ scope.row.question }}</span>
                  </div>
                  <div
                    v-if="scope.row.extraType === 'fix'"
                    style="color: #929292;margin: 5px 0"
                  >
                    <span>{{ $t('auth.answer') }}</span>
                    <dict-tag v-if="scope.row.dictType" :value="scope.row.fromValues" :type="scope.row.dictType" />
                    <span v-else>{{ scope.row.fromValues?.join(',') }}</span>

                  </div>
                  <div v-else>
                    {{ scope.row.val }}
                  </div>
                </div>
              </div>

            </template>
          </el-table-column>
          <el-table-column :label="$t('auth.toExamine')" prop="audit">
            <template #default="scope">
              <div
                v-if="scope.row.extraType === 'fix'"
              >
                <dict-tag :value="scope.row.value" :type="DICT_TYPE.AUTH_SPOT_CHECK_SCORE_QUESTION_OPTION" />
                <div v-if="scope.row.value === 'inconsistent'" style="margin-top: 3px;">
                  <div v-if="['common','selfDecimal','number'].includes(scope.row.inconsistentValueType)">
                    <vue-ellipsis
                      :visible-line="2"
                      :ellipsis="scope.row.inconsistentValueOmission"
                      :text="scope.row.inconsistentValue"
                    >
                      <template #ellipsisNode>
                        ...
                        <el-button type="text" @click="scope.row.inconsistentValueOmission =!scope.row.inconsistentValueOmission ">
                          {{ scope.row.inconsistentValueOmission ? $t('auth.open'):$t('common.putItAway') }}
                        </el-button>
                      </template>
                      <template #unellipsisNode>
                        <el-button type="text" @click="scope.row.inconsistentValueOmission =!scope.row.inconsistentValueOmission ">
                          {{ scope.row.inconsistentValueOmission ? $t('auth.open'):$t('common.putItAway') }}
                        </el-button>
                      </template>
                    </vue-ellipsis>

                  </div>
                  <div v-else-if="scope.row.inconsistentValueType === 'date'">
                    {{ dayjs(scope.row.inconsistentValue).format('YYYY-MM-DD') }}
                  </div>
                  <dict-tag v-else :value="scope.row.inconsistentValue" :type="scope.row.dictType" />

                </div>
              </div>
              <div v-else>
                <dict-tag :type="DICT_TYPE.SUPPLIER_LEAN_PROCESS" :value="scope.row.value" />
                <dict-tag :type="DICT_TYPE.AUTH_SPOT_CHECK_QUESTION_SIMPLE_OPTION" :value="scope.row.value" />
                <dict-tag :type="DICT_TYPE.SUPPLIER_MAN_MAIN_RESPONSIBILITY" :value="scope.row.value" />
                <dict-tag :type="DICT_TYPE.AUTH_SPOT_CHECK_QUESTION_COMPLEX_OPTION" :value="scope.row.value" />
                <div v-if="['auditInput','auditInteger','auditFloat'].includes(scope.row.valueType)">
                  {{ scope.row.value }}
                </div>

              </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('sp.score')" prop="score" width="50">
            <template #default="scope">
              {{ scope.row.scoreJson ?
                Object.values(JSON.parse(scope.row.scoreJson)).reduce((a,b)=>a+b) : 0 }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('system.label')" prop="flag" width="120">
            <template #default="scope">
              {{ scope.row.flag || '-' }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('auth.nonCompliantItems')" prop="inconsistent">
            <template #default="scope">
              <div style="display: flex">

                <dict-tag
                  v-if="scope.row.severity"
                  :class="riskClass(scope.row.severity)"
                  :value="scope.row.severity"
                  :type="DICT_TYPE.AUTH_SPOT_CHECK_SCORE_QUESTION_INCONSISTENT_OPTION"
                />
                <div v-if="scope.row.riskDescription || scope.row.riskMeasures">

                  <span style="margin-left: 10px;cursor:pointer;color: #4d93b9" @click="scope.row.showRisk=!scope.row.showRisk">
                    {{ scope.row.showRisk ? $t('common.putItAway'):$t('auth.expandNotes') }}
                    <i
                      class="el-icon-arrow-up"
                      :style="scope.row.showRisk? '':{transform: 'rotate(180deg)'}"
                    />
                  </span>
                </div>
              </div>

              <div v-show="scope.row.showRisk">
                <div>
                  <span style="color: #929292 ">{{ $t('auth.riskDescription') }}</span>
                  <span>
                    {{ scope.row.riskDescription }}
                  </span>
                </div>
                <div>
                  <span style="color: #929292 ">{{ $t('auth.riskManagement') }}</span>
                  <span>
                    {{ scope.row.riskDescription }}
                  </span>
                </div>
              </div>

            </template>
          </el-table-column>

        </el-table>

        <div style="margin-top: 25px">
          <span class="title_tag">
            <span class="title_tag_text">|</span>
            <span>{{ $t('sp.score') }}</span></span>
        </div>
        <div style="display: flex;justify-content: center;margin: 30px 0">

          <div style="border: 0.5px solid #929292;border-radius: 8px; width: 740px;padding: 25px;display: flex">
            <div
              style="flex: 1 0 40%;display: flex;border-right: 1px solid #C3C3C3;
            justify-content: center;flex-direction: column;align-items: center;"
            >
              <span class="ft16" style="color: #4d93b9">{{ $t('auth.totalScore') }}</span>

              <div style="font-size: 32px;font-weight: bold">{{ spotCheckStepInfo.score }}</div>
            </div>
            <div style="flex: 1 0 60%;display: flex;justify-content: space-evenly">
              <div v-for="item in scoreList" style="display: flex;">
                <div style="display: flex;flex-direction: column;align-items: center">
                  <span class="ft16" style="color: #929292">{{ item.label }}</span>
                  <div style="font-size: 24px;font-weight: bold;margin-top: 7px">{{ item.value }}</div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <div v-show="!activeAudit" style="background: #fff;padding: 20px">
        <span class="title_tag">
          <span class="title_tag_text">|</span>
          <span>{{ $t('auth.onSiteInformation') }}</span></span>
        <el-descriptions
          style="padding: 10px 26px;margin: 10px 0"
          :column="1"
          :label-style="{width: '200px',color:'#929292'}"
        >
          <el-descriptions-item :label="$t('auth.supplierReceptionPersonnelAndTheirPositions')">
            {{
              supplierInfo.contactCreateReqVO?.filter(v => v.businessDictRelStr.includes('the_main_contact')).map(v => v.name).join(',')
            }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('auth.supplierInterviewAddress')">

            {{ spotCheckStepInfo.extraInfo?.supplierAddress }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('auth.onSiteAuditors')">
            {{ spotCheckStepInfo.extraInfo?.supplierProcessor }}
          </el-descriptions-item>
          <el-descriptions-item :label="$t('auth.visitContent')">
            {{ getDictDatas(DICT_TYPE.AUTH_SPOT_CHECK_COMMON_TYPE).map(v => v.label).join('/') }}

          </el-descriptions-item>
          <el-descriptions-item :label="$t('auth.supplierProfile')">
            <span>{{ $t('auth.currentlyUnavailable') }}</span>
          </el-descriptions-item>
          <el-descriptions-item :label="$t('auth.reviewTime')">
            {{ spotCheckStepInfo.extraInfo?.reserveCheckStartDate }} -
            {{ spotCheckStepInfo.extraInfo?.reserveCheckEndDate }}

          </el-descriptions-item>

        </el-descriptions>

        <span class="title_tag">
          <span class="title_tag_text">|</span>
          <span>{{ $t('tender.supplierInformation') }}</span></span>

        <el-descriptions
          style="padding: 10px 26px;margin: 10px 0"
          :column="1"
          :label-style="{width: '200px',color:'#929292'}"
        >
          <el-descriptions-item :label="$t('auth.address')">{{ supplierInfo.registeredAddress }}</el-descriptions-item>
          <el-descriptions-item :label="$t('sp.companyType')">
            <dict-tag :type="DICT_TYPE.SUPPLIER_TYPE" :value="simpleInfo.companyCategory" />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.natureOfTheCompany')">
            <dict-tag :type="DICT_TYPE.SUPPLIER_COMPANY_NATURE" :value="simpleInfo.companyNature" />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('supplier.numberOfEmployees')">
            {{ supplierInfo.dictRelCreateReqVO?.find(v => v.dictDataValue === 'all_staff').value }}人
          </el-descriptions-item>
          <el-descriptions-item :label="$t('auth.marketingRevenue')">
            {{ simpleInfo.turnover }}
            <dict-tag :type="DICT_TYPE.SUPPLIER_CURRENCY_UNIT" :value="simpleInfo.turnoverUnit	" />
            <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="simpleInfo.turnoverCurrency	" />

          </el-descriptions-item>
          <el-descriptions-item :label="$t('material.category')">
            <show-or-edit
              :dict="DICT_TYPE.COMMON_CATEGORY"
              :disabled="true"
              :value="categoryIds"
            />
          </el-descriptions-item>

        </el-descriptions>

      </div>

    </div>
  </div>
</template>
<script>
import { getAllAuthSupplierCategoryRel, getSimpleInfo } from '@/api/auth/supplier'
import { getBaseInfo } from '@/api/supplier/resourceRepository'
import { getRelAndDefList, relScoreItemList, supplierSpotCheckStep } from '@/api/auth'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'

export default {
  name: 'Auditreport/:id',
  components: { ShowOrEdit },
  data() {
    return {
      loading: false,
      supplierInfo: {},
      navActive: 'common',
      question: [],
      authId: 0,
      supplierId: 0,
      scoreMap: {
        technical_section_score:
          this.$t('auth.qualityq'),
        quality_section_score:
          this.$t('auth.technologyt'),
        cost_section_score:
          this.$t('auth.costc')
      },
      spotCheckStepInfo: {},
      activeAudit: true,
      simpleInfo: {},
      categoryIds: []
    }
  },
  computed: {
    navList() {
      return this.question.reduce((a, b) => {
        const nav = a.find(c => c.id === b.sectionType)
        if (nav) {
          nav.num++
        } else {
          a.push({
            label: getDictDataLabel(DICT_TYPE.AUTH_SPOT_CHECK_COMMON_TYPE, b.sectionType),
            id: b.sectionType,
            num: 1
          })
        }
        return a
      }, [])
    },
    filterQuestion() {
      return this.question.filter(a => a.sectionType === this.navActive)
    },
    scoreList() {
      return Object.keys(this.scoreMap).map(a => {
        return {
          label: this.scoreMap[a],
          value: this.spotCheckStepInfo.dictRelList?.find(b => b.dictValue === a)?.value || 0
        }
      })
    }
  },
  mounted() {
    this.authId = this.$route.query.authId
    this.supplierId = this.$route.query.supplierId
    this.init()
    this.getQuestion()
  },
  methods: {
    init() {
      const reqData = { authId: this.authId }
      getSimpleInfo(reqData).then(res => {
        this.simpleInfo = res.data
        getBaseInfo(this.simpleInfo.supplierId).then(res => {
          this.supplierInfo = res.data
        })
      })
      getAllAuthSupplierCategoryRel(reqData).then(res => {
        if (res.data) {
          this.categoryIds = res.data.map(v => v.categoryId)
        }
      })
      supplierSpotCheckStep(reqData).then(res => {
        this.spotCheckStepInfo = res.data
      })
    },
    async getQuestion() {
      var selectedTypes = []
      await relScoreItemList({ authId: this.authId }).then(res => {
        this.navList = this.navList.filter(a => {
          a.canEdit = res.data.scoreitemRelList?.find(b => b.configType === a.id)?.canEdit
          return res.data.scoreitemRelList.map(b => b.configType).includes(a.id)
        })
        selectedTypes = res.data.scoreitemRelList.map(v => v.configType)
      })
      this.loading = true
      getRelAndDefList({ authId: this.authId, supplierId: this.supplierId, showMoreDetail: true }).then(res => {
        res.data.forEach(a => {
          a.inconsistentValueOmission = true
          a.showRisk = false
          if (a.valueType === 'leanProcess') {
            a.value = a.value ? a.value.split(',') : []
          }
          if (['managementSoftware', 'qualitySystem'].includes(a.inconsistentValueType)) {
            a.inconsistentValue = a.inconsistentValue ? a.inconsistentValue.split(',') : []
          }
        })
        this.loading = false
        // 再过滤一遍 当前认证单据勾选的评分项
        res.data = res.data.filter(a => selectedTypes.includes(a.sectionType))
        this.question = res.data
      })
    },
    activeTab(id, index) {
      this.navActive = index
    },
    riskClass(risk) {
      if (risk === 'important') {
        return 'mediumRisk'
      } else if (risk === 'serious') {
        return 'highRisk'
      } else {
        return 'commonRisk'
      }
    }
  }
}
</script>

<style scoped lang="scss">
.ft16 {
  font-size: 16px;
}

.title_tag {
  font-weight: bold;
  font-size: 16px;

  &_text {
    color: #4d93b9;
    margin-right: 5px
  }
}

.mediumRisk::before {
  background: #FF994F;
  position: absolute;
  content: '';
  top: 12px;
  left: -3px;
  border-radius: 50%;
  width: 5px;
  height: 5px;
}

.highRisk::before {
  background: #FA5151;
  position: absolute;
  content: '';
  top: 12px;
  left: -3px;
  border-radius: 50%;
  width: 5px;
  height: 5px;
}

.commonRisk::before{
  background: #4d93b9;
  position: absolute;
  content: '';
  top: 12px;
  left: -3px;
  border-radius: 50%;
  width: 5px;
  height: 5px;
}

::v-deep .el-table__cell {
  vertical-align: top;
  padding-top: 3px;
}

.auditTab {
  padding: 10px 20px;
  font-size: 17px;
  border-radius: 7px;
  cursor: pointer;
}

.active {
  color: #4d93b9;
  background: #ffffff;
  font-weight: bold;
}

.report {
  background: #F8F8F8;
  padding: 26px 60px;
  border-radius: 8px;
  width: 1200px;
  margin: 20px 0
}

.navBox {
  margin-left: 10px;
  display: flex;
  border-bottom: 2px solid rgba(0, 0, 0, 0.09);
}

.navItem {
  font-weight: 400;
  color: #000000;
  padding: 8px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.navItem:hover {
  color: #4996b8;
}

.navItem.active {
  color: #4996b8;
  position: relative;
  font-weight: bold;

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 50%;
    height: 2px;
    background-color: #4996b8;
  }
}

</style>
