<template>
  <div style="padding: 15px 25px">
    <common-card
      :title="$t('ppv.projectInformation')"
    >
      <el-descriptions
        :label-style="{
          width: '120px'
        }"
        :column="2"
      >
        <el-descriptions-item :label="$t('auth.authenticationDocumentCode')">{{ ppvInfo.no }}</el-descriptions-item>
        <el-descriptions-item :label="$t('common.status')">
          <dict-tag :value="ppvInfo.status" :type="DICT_TYPE.PPV_PROJECT_STATUS" />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('rfq.createdBy')">
          <dict-tag :value="ppvInfo.creator" :type="DICT_TYPE.COMMON_USERS" />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('common.createTime')">
          {{ ppvInfo.createTime?dayjs(ppvInfo.createTime).format('YYYY-MM-DD'):'' }}
        </el-descriptions-item>
      </el-descriptions>
    </common-card>
    <common-card
      :title="$t('ppv.ppvList')"
      style="margin-bottom: 35px"
    >
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.searchText	"
          style="flex: 0 1 40%"
          :placeholder="$t('ppv.manufacturerManufacturerPartNumberSupplierName')"
          clearable
          @keyup.enter.native="getList"
        />
        <el-button
          type="primary"
          plain
          @click="queryParams.pageNo = 1;getList();"
        >{{ $t('common.search') }}
        </el-button>
        <el-button
          plain
          style="margin-left: 0"
          @click="doReset"
        >{{ $t('common.reset') }}
        </el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button
            type="text"
          >
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            class="el-icon-arrow-up"
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>

      </div>

      <!--高级搜索选项-->
      <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="120px">

        <el-form-item
          class="searchItem"
          :label="$t('material.manufacturer')"
          prop="processor"
        >
          <el-input v-model="queryParams.mfg" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />

        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('ppv.erpPn')"
          prop="authStatus"
        >
          <el-input v-model="queryParams.erpPn" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />

        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('common.status')"
          prop="category"
        >
          <el-select v-model="queryParams.materialStatus" class="searchValue" multiple clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.PPV_MATERIAL_SUPPLIER_STATUS)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-form-item>

        <el-form-item
          class="searchItem"
          :label="$t('material.manufacturersPartNumber')"
          prop="purchaseOrgId"
        >
          <el-input v-model="queryParams.mpn" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('sp.supplier')"
          prop="purchaseOrgId"
        >
          <el-input v-model="queryParams.supplierName" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />

        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('ppv.meetTpPrices')"
          prop="category"
        >
          <el-select v-model="queryParams.meetTp" class="searchValue" clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('order.timeType')"
          prop="dateType"
        >
          <el-select v-model="queryParams.dateType" class="searchValue" clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.PPV_TIME_TYPE)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-form-item>
        <el-form-item class="searchItem" label=" ">
          <el-date-picker
            v-model="queryParams.time"
            class="searchValue"
            type="daterange"
            :start-placeholder="$t('common.startDate')"
            :end-placeholder="$t('common.endDate')"
            :range-separator="$t('order.to')"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('ppv.feedback')"
          prop="companyId"
        >
          <el-select
            v-model="queryParams.action"
            :placeholder="$t('auth.pleaseSelectACompany')"
            class="searchValue"
            multiple
            clearable
            filterable
          >
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.PPV_ACTIONS)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <!--    总览列表展示-->
      <vxe-grid
        ref="overview"
        :data="list"
        :loading="loading"
        v-bind="gridOption"
      >
        <template #meetTp="{row}">
          <dict-tag v-if="row.meetTp" :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="row.meetTp==1" />
        </template>

        <template #packageMethod="{row}">
          <dict-tag :type="DICT_TYPE.PPV_PACKAGE" :value="row.packageMethod" />
        </template>

        <template #longTermSupport="{row}">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="row.longTermSupport" />
        </template>

        <template #source="{row}">
          <dict-tag :type="DICT_TYPE.PPV_SOURCE" :value="row.source" />
        </template>

        <template #package2="{row}">
          <dict-tag :type="DICT_TYPE.PPV_PACKAGE" :value="row.package2" />
        </template>

        <template #status="{row}">
          <dict-tag :type="DICT_TYPE.PPV_MATERIAL_SUPPLIER_STATUS" :value="row.status" />
        </template>

        <template #action="{row}">
          <dict-tag :type="DICT_TYPE.PPV_ACTIONS" :value="row.action" />
        </template>

        <template #creator="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.creator" />
        </template>

        <template #projectStatus="{row}">
          <dict-tag :type="DICT_TYPE.PPV_PROJECT_STATUS" :value="row.projectStatus" />
        </template>
        <template
          #number="{row,column}"
        >
          <number-format :value="row[column.property]" />
        </template>
        <template
          #number2="{row,column}"
        >
          <number-format :value="row[column.property]" />
        </template>

        <template #toolbar_buttons>
          <el-row :gutter="10" style="width: 100%" class="mb8">

            <el-col :span="1.5">
              <el-button
                v-if="ppvInfo.hasToQuote"
                type="primary"
                size="mini"
                plain
                @click="quoteUpload.open=true"
              >  {{ $t('ppv.uploadQuotationData') }}</el-button>
            </el-col>
            <el-col :span="1.5">

              <el-button
                size="mini"
                plain
                @click="doExport"
              >  {{ $t('order.download') }}</el-button>
            </el-col>

            <right-toolbar :list-id="gridOption.id" :show-search.sync="showSearch" :custom-columns.sync="gridOption.columns" @queryTable="getList" />
          </el-row>
        </template>

        <template #operate="{row}">
          <OperateDropDown
            :menu-item="[
              {
                name: $t('ppv.replyToQuotation'),
                show: row.status=='to_quote',
                action: (row)=>showMaterial(row),
                para: row
              },
            ]"
          />
        </template>
      </vxe-grid>
      <!--分页组件-->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <detail
        v-if="materialVisible"
        ref="material"
        :material-visible.sync="materialVisible"
        @getTable="getList"
      />
    </common-card>
    <div class="fixedBottom">
      <el-button @click="$router.back()">{{ $t('sp.cancel') }}</el-button>
      <el-button v-if="ppvInfo.hasToQuote" type="primary" @click="submit">{{ $t('ppv.submitAQuotation') }}</el-button>
    </div>
    <!--    上传报价数据-->
    <el-dialog :title="quoteUpload.title" :visible.sync="quoteUpload.open" append-to-body width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="quoteUpload"
          :action="quoteUpload.url"
          :auto-upload="false"
          :disabled="quoteUpload.isUploading"
          :headers="quoteUpload.headers"
          :limit="1"
          :on-preview="onPreview"
          :on-progress="handleQuoteFileUploadProgress"
          :on-success="handleQuoteFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="quoteUpload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button plain type="primary" @click="exportQuoteTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" :loading="quoteUpload.loading" @click="importQuoteTemplateData"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getConfig } from '@/utils/config'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import {
  getProjectPpv,
  pageCustomerOverview, supplierSubmitProject
} from '@/api/ppv'
import { getBaseHeader } from '@/utils/request'
import { exportOverview, exportQuoteTemplateForSupplier } from '@/api/ppv/overview'
import dayjs from 'dayjs'
import detail from '@/views/ppv/detail.vue'

export default {
  name: 'Supplierdetail/:no',
  components: { detail, OperateDropDown },
  data() {
    return {
      ppvInfo: {
        id: '',
        status: '',
        no: '',
        createTime: ''
      },
      queryParams: {
        latestPpv: false,
        searchText: '',
        mfg: '',
        mpn: '',
        erpPn: '',
        materialStatus: [],
        supplierName: '',
        meetTp: '',
        action: [],
        beginDate: '',
        endDate: '',
        dateType: '',
        projectId: 0,
        supplierId: null,
        pageNo: 1,
        pageSize: 10,
        sortField: 'id',
        sortBy: 'ASC',
        time: []
      },
      list: [],
      showSearch: false,
      loading: false,
      total: 0,
      pageNo: 1,
      pageSize: 10,
      gridOption: {
        id: 'ppvList',
        align: 'left',
        border: true,
        keepSource: false,
        maxHeight: 700,
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        columnConfig: {
          resizable: true
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          {
            field: 'operate',
            showOverflow: false,
            slots: { default: 'operate' },
            fixed: 'left',
            title: this.$t('common.operate'),
            visible: true,
            width: 35
          },
          { title: this.$t('ppv.quotationDate'), field: 'offerDate', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('sp.supplier'), field: 'supplierName', visible: true, width: 135, fixed: 'left' },
          { title: this.$t('supplier.contacts'), field: 'name', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('supplier.mailbox'), field: 'email', visible: true, width: 135, fixed: 'left' },
          { title: this.$t('ppv.erpPn'), field: 'erpPn', visible: true, width: 135, fixed: 'left' },
          { title: this.$t('material.factory'), field: 'division', visible: true, width: 100 },
          { title: this.$t('ppv.erpManufacturerPartNumber'), field: 'erpMpn', visible: true, width: 135 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 135 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 135 },
          { title: this.$t('rfq.targetPrice'), field: 'targetPrice', slots: { default: 'number' }, visible: true, width: 60, align: 'right' },
          { title: this.$t('ppv.quantityDemanded'), field: 'prQty', slots: { default: 'number2' }, visible: true, width: 100, align: 'right' },
          { title: this.$t('ppv.requirementRemarks'), field: 'prRemark', visible: true, width: 135 },
          { title: this.$t('ppv.meetTpPrices'), slots: { default: 'meetTp' }, field: 'meetTp', visible: true, width: 100 },
          { title: this.$t('ppv.packagingMethod'), slots: { default: 'packageMethod' }, field: 'packageMethod', visible: true, width: 100 },
          { title: this.$t('ppv.existingInventory'), field: 'stockAvailable', visible: true, width: 100 },
          { title: this.$t('ppv.moq'), field: 'moq', visible: true, width: 60, align: 'right' },
          { title: this.$t('ppv.mpq'), field: 'mpq', visible: true, width: 60, align: 'right' },
          { title: this.$t('ppv.productionCycledays'), field: 'ltDay', visible: true, width: 100 },
          { title: this.$t('ppv.longTermSupport'), slots: { default: 'longTermSupport' }, field: 'longTermSupport', visible: true, width: 100 },
          { title: this.$t('rfq.price'), field: 'price', slots: { default: 'number' }, visible: true, width: 80, align: 'right' },
          { title: this.$t('order.dateOfManufacture'), field: 'dc', visible: true, width: 100 },
          { title: this.$t('avpl.source'), slots: { default: 'source' }, field: 'source', visible: true, width: 100 },
          { title: this.$t('common.remarks'), field: 'remark', visible: true, width: 135 },
          { title: this.$t('ppv.rebate'), field: 'rebate', visible: true, width: 100 },
          { title: this.$t('ppv.releaseDate'), field: 'dateRelease', visible: true, width: 100 },
          { title: this.$t('ppv.deliveryDate'), field: 'deliveryDate', visible: true, width: 100 },
          { title: this.$t('ppv.mpq2'), field: 'mpq2', visible: true, width: 60, align: 'right' },
          { title: this.$t('rfq.deliveryCycle'), field: 'leadTime', visible: true, width: 100 },
          { title: this.$t('ppv.packagingMethod2'), slots: { default: 'package2' }, field: 'package2', visible: true, width: 100 },
          { title: this.$t('ppv.priceValidityPeriod'), field: 'validDate', visible: true, width: 100 },
          { title: this.$t('ppv.feedbackRemarks'), field: 'comment', visible: true, width: 135 },
          { title: this.$t('ppv.feedback'), slots: { default: 'action' }, field: 'action', visible: true, width: 110 },
          { title: this.$t('common.status'), slots: { default: 'status' }, field: 'status', visible: true, width: 100 }
          // { title: this.$t('common.operate'), slots: { default: 'operate' }, field: 'createTime', visible: true, width: 135 }
        ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      // 报价上传
      quoteUpload: {
        title: this.$t('ppv.uploadQuotationData'),
        open: false,
        isUploading: false,
        loading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/ppv/project-material-rel-ppv/import-supplier-quote-excel?projectId=' + this.projectId
      },
      materialVisible: false
    }
  },
  mounted() {
    this.projectId = this.$route.query.projectId
    this.queryParams.supplierId = this.$route.query.supplierId
    this.quoteUpload.url = getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/ppv/project-material-supplier-quote/import-supplier-quote-excel?projectId=' + this.projectId + '&supplierId=' + this.queryParams.supplierId
    this.init()
    this.getList()
  },
  methods: {
    dayjs,
    init() {
      getProjectPpv({
        projectId: this.projectId,
        supplierId: this.queryParams.supplierId
      }).then(res => {
        this.ppvInfo = res.data
      })
    },
    getList() {
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      pageCustomerOverview({
        ...this.queryParams,
        projectId: this.projectId
      }).then(res => {
        this.list = res.data.list
        this.total = res.data.total
      })
    },

    doReset() {
      this.queryParams = {
        latestPpv: false,
        searchText: '',
        mfg: '',
        mpn: '',
        erpPn: '',
        materialStatus: [],
        supplierName: '',
        meetTp: '',
        action: [],
        beginDate: '',
        endDate: '',
        dateType: '',
        projectId: 0,
        supplierId: null,
        pageNo: 1,
        pageSize: 10,
        sortField: 'id',
        sortBy: 'ASC',
        time: []
      }
      this.getList()
    },
    submit() {
      supplierSubmitProject({
        projectId: this.projectId,
        supplierId: this.$route.query.supplierId
      }).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.$tab.closeOpenPage('/ppv/ppvsupplierindex')
      })
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.url) {
        window.open(file.url)
      }
    },
    // 导出报价数据模板
    exportQuoteTemplate() {
      exportQuoteTemplateForSupplier(this.projectId, this.queryParams.supplierId).then(response => {
        this.$download.excel(response, this.$t('ppv.quotationDataTemplateXlsx'))
      })
    },
    // 导入报价上传数据
    importQuoteTemplateData() {
      this.$refs.quoteUpload.submit()
      this.quoteUpload.loading = true
    },

    // 报价文件上传中处理
    handleQuoteFileUploadProgress(event, file, fileList) {
      this.quoteUpload.isUploading = true
    },
    // 报价文件上传成功处理
    handleQuoteFileSuccess(response, file, fileList) {
      this.quoteUpload.loading = false
      this.quoteUpload.open = false
      this.quoteUpload.isUploading = false
      this.$refs.quoteUpload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.successNo) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.successNo.length
      }
      if (data.failureNo) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + Object.keys(data.failureNo).length
        for (const index in data.failureNo) {
          text += '<br />&nbsp;&nbsp;&nbsp;&nbsp; ' + this.$t('rfq.lineNumber') + index + '：' + data.failureNo[index]
        }
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 导出列表
    doExport() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      this.queryParams.projectId = this.projectId
      exportOverview(this.queryParams).then(res => {
        this.$download.excel(res, this.$t('ppv.quotationListXlsx'))
        this.loading = false
      }).catch(_ => {
        this.loading = false
      })
    },
    showMaterial(row) {
      this.materialVisible = true
      this.$nextTick(() => {
        this.$refs.material.showMaterialDetail(row.materialRelId, row.quoteId)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

::v-deep .gray-row {
  background: #f1f2f6;;
}

.searchTimeItem {
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 82px);
  }
}

.searchValue {
  width: 95%;
}

</style>
