<template>
  <div class="app-container">
    <statistics-card
      :default-show-head="true"
      :default-show="true"
      content-style="space-around"
    />

    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input
        v-model="queryParams.searchText	"
        style="flex: 0 1 40%"
        :placeholder="$t('ppv.documentNumberManufacturerManufacturerPartNumberSupplierName')"
        clearable
        @keyup.enter.native="doSearch"
      />
      <el-button
        type="primary"
        plain
        @click="queryParams.pageNo = 1;doSearch();"
      >{{ $t('common.search') }}
      </el-button>
      <el-button
        plain
        style="margin-left: 0"
        @click="doReset"
      >{{ $t('common.reset') }}
      </el-button>

      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button type="text">
          {{ $t('common.advancedSearch') }}
        </el-button>
        <i
          class="el-icon-arrow-up"
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>

    </div>
    <!--高级搜索选项-->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="170px">
      <el-form-item class="searchItem" :label="$t('material.manufacturer')" prop="mfg">
        <el-input
          v-model="queryParams.mfg"
          class="searchValue"
          :placeholder="$t('material.pleaseEnterTheManufacturer')"
          clearable
        />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('ppv.erpPn')" prop="erpPn">
        <el-input
          v-model="queryParams.erpPn"
          class="searchValue"
          :placeholder="$t('ppv.pleaseEnterErpPn')"
          clearable
        />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('common.status')" prop="materialStatus">
        <el-select v-model="queryParams.materialStatus" class="searchValue" clearable multiple>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.PPV_MATERIAL_SUPPLIER_STATUS)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('material.manufacturersPartNumber')" prop="mpn">
        <el-input
          v-model="queryParams.mpn"
          class="searchValue"
          :placeholder="$t('material.pleaseEnterTheManufacturersPartNumber')"
          clearable
        />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('sp.supplier')" prop="supplierName">
        <el-input
          v-model="queryParams.supplierName"
          class="searchValue"
          :placeholder="$t('avpl.pleaseEnterTheSupplier')"
          clearable
        />
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('ppv.meetTpPrices')" prop="meetTp">
        <el-select v-model="queryParams.meetTp" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" :label="$t('order.timeType')" prop="dateType">
        <el-select v-model="queryParams.dateType" class="searchValue" clearable>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.PPV_TIME_TYPE)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item class="searchItem" label=" ">
        <el-date-picker
          v-model="queryParams.time"
          class="searchValue"
          type="daterange"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item class="searchItem" :label="$t('ppv.feedback')" prop="action">
        <el-select v-model="queryParams.action" class="searchValue" clearable multiple>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.PPV_ACTIONS)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!--      FIXME 后端未加这个参数-->
      <el-form-item class="searchItem" :label="$t('ppv.latestBatchPpv')" prop="action">
        <el-checkbox v-model="queryParams.latestPpv" class="searchValue" style="margin-left: 20px" />
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <vxe-grid
      ref="customerOverview"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
    >
      <template #no="{row}">
        <!--   跳转 【项目明细页面sheet】-->
        <copyButton
          type="text"
          @click="$router.push(`/ppv/ppvdetail/${row.no}?projectId=${row.projectId}`)"
        >
          {{ row.no }}
        </copyButton>
      </template>
      <template #meetTp="{row}">
        <dict-tag v-if="row.meetTp" :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="row.meetTp==1" />
      </template>

      <template #packageMethod="{row}">
        <dict-tag :type="DICT_TYPE.PPV_PACKAGE" :value="row.packageMethod" />
      </template>

      <template #longTermSupport="{row}">
        <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="row.longTermSupport" />
      </template>

      <template #source="{row}">
        <dict-tag :type="DICT_TYPE.PPV_SOURCE" :value="row.source" />
      </template>

      <template #package2="{row}">
        <dict-tag :type="DICT_TYPE.PPV_PACKAGE" :value="row.package2" />
      </template>

      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.PPV_MATERIAL_SUPPLIER_STATUS" :value="row.status" />
      </template>

      <template #action="{row}">
        <dict-tag :type="DICT_TYPE.PPV_ACTIONS" :value="row.action" />
      </template>

      <template #creator="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.creator" />
      </template>

      <template #projectStatus="{row}">
        <dict-tag :type="DICT_TYPE.PPV_PROJECT_STATUS" :value="row.projectStatus" />
      </template>

      <template #toolbar_buttons>
        <el-row :gutter="10" style="width: 100%" class="mb8">

          <el-col :span="1.5">
            <el-button
              type="primary"
              size="mini"
              @click="upload.open=true"
            >  {{ $t('ppv.uploadPpvData') }}</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="primary"
              size="mini"
              @click="doOpenFeedback"
            >  {{ $t('ppv.batchMaintenanceOfFeedbackData') }}</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="primary"
              size="mini"
              @click="doExport"
            >  {{ $t('order.download') }}</el-button>
          </el-col>
          <right-toolbar :list-id="girdOption.id" :show-search.sync="showSearch" :custom-columns.sync="girdOption.columns" @queryTable="doSearch" />
        </el-row>
      </template>
    </vxe-grid>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="doSearch"
    />

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="$t('ppv.feedbackInformation')" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" label-width="80px">

        <el-form-item :label="$t('ppv.feedback')" prop="action">
          <el-select v-model="form.action" class="searchValue" clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.PPV_ACTIONS)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('ppv.feedbackRemarks')" prop="comment">
          <el-input v-model="form.comment" type="textarea" :rows="4" :placeholder="$t('ppv.pleaseEnterFeedbackNotes')" maxlength="255" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="doCancelFeedback">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="doSubmitFeedback">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
    <!--    PPV上传数据-->
    <el-dialog ref="upload" :title="upload.title" :visible.sync="upload.open" append-to-body width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url"
          :auto-upload="false"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-preview="onPreview"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :before-upload="beforeUploadXls"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button plain type="primary" @click="exportPpvTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" :loading="upload.loading" @click="importPPvTemplateData"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

    <!--    P3反馈详情页面组件-->
    <feedback-detail
      ref="feedbackDetail"
      :material-visible.sync="feedbackDetailVisible"
      @getTable="doSearch()"
    />
  </div>
</template>

<script>

import { batchFeedback, exportOverview, exportPpvTemplate, pageCustomerOverview } from '@/api/ppv/overview'
import { getBaseHeader } from '@/utils/request'
import { statisticsProjectPpv } from '@/api/ppv'
import $modal from "@/plugins/modal";
import {getConfig} from "@/utils/config";

export default {
  name: 'CustomerIndex',
  components: {
    feedbackDetail: () => import('./detail.vue'),
    statisticsCard: () => import('./statisticCard.vue')
  },
  data() {
    return {
      // 单行数据选中时的 反馈按钮弹框
      feedbackDetailVisible: false,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 系统模块列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        searchText: '',
        mfg: null,
        mpn: null,
        erpPn: null,
        materialStatus: null,
        supplierName: null,
        meetTp: null,
        dateType: null,
        time: [],
        action: null,
        latestPpv: true
      },
      // 表单参数
      form: {},
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'customerOverview',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        filterConfig: {
          remote: true
        },
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          { title: this.$t('auth.authenticationDocumentCode'), field: 'no', slots: { default: 'no' }, visible: true, width: 110, fixed: 'left' },
          { title: this.$t('ppv.quotationDate'), field: 'offerDate', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('sp.supplier'), field: 'supplierName', visible: true, width: 135, fixed: 'left' },
          { title: this.$t('supplier.contacts'), field: 'name', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('supplier.mailbox'), field: 'email', visible: true, width: 135, fixed: 'left' },
          { title: this.$t('ppv.erpPn'), field: 'erpPn', visible: true, width: 135, fixed: 'left' },
          { title: this.$t('material.factory'), field: 'division', visible: true, width: 100 },
          { title: this.$t('ppv.erpManufacturerPartNumber'), field: 'erpMpn', visible: true, width: 135 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 135 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 135 },
          { title: this.$t('rfq.targetPrice'), field: 'targetPrice', visible: true, width: 60, align: 'right' },
          { title: this.$t('ppv.quantityDemanded'), field: 'prQty', visible: true, width: 100, align: 'right' },
          { title: this.$t('ppv.requirementRemarks'), field: 'prRemark', visible: true, width: 135 },
          { title: this.$t('ppv.meetTpPrices'), slots: { default: 'meetTp' }, field: 'meetTp', visible: true, width: 100 },
          { title: this.$t('ppv.packagingMethod'), slots: { default: 'packageMethod' }, field: 'packageMethod', visible: true, width: 100 },
          { title: this.$t('ppv.existingInventory'), field: 'stockAvailable', visible: true, width: 100 },
          { title: this.$t('ppv.moq'), field: 'moq', visible: true, width: 60, align: 'right' },
          { title: this.$t('ppv.mpq'), field: 'mpq', visible: true, width: 60, align: 'right' },
          { title: this.$t('ppv.productionCycledays'), field: 'ltDay', visible: true, width: 100 },
          { title: this.$t('ppv.longTermSupport'), slots: { default: 'longTermSupport' }, field: 'longTermSupport', visible: true, width: 100 },
          { title: this.$t('rfq.price'), field: 'price', visible: true, width: 80, align: 'right' },
          { title: this.$t('order.dateOfManufacture'), field: 'dc', visible: true, width: 100 },
          { title: this.$t('avpl.source'), slots: { default: 'source' }, field: 'source', visible: true, width: 100 },
          { title: this.$t('common.remarks'), field: 'remark', visible: true, width: 135 },
          { title: this.$t('ppv.rebate'), field: 'rebate', visible: true, width: 100 },
          { title: this.$t('ppv.releaseDate'), field: 'dateRelease', visible: true, width: 100 },
          { title: this.$t('ppv.deliveryDate'), field: 'deliveryDate', visible: true, width: 100 },
          { title: this.$t('ppv.mpq2'), field: 'mpq2', visible: true, width: 60, align: 'right' },
          { title: this.$t('rfq.deliveryCycle'), field: 'leadTime', visible: true, width: 100 },
          { title: this.$t('ppv.packagingMethod2'), slots: { default: 'package2' }, field: 'package2', visible: true, width: 100 },
          { title: this.$t('ppv.priceValidityPeriod'), field: 'validDate', visible: true, width: 100 },
          { title: this.$t('ppv.feedbackRemarks'), field: 'comment', visible: true, width: 135 },
          { title: this.$t('ppv.feedback'), slots: { default: 'action' }, field: 'action', visible: true, width: 110 },
          { title: this.$t('common.status'), slots: { default: 'status' }, field: 'status', visible: true, width: 100 },
          { title: this.$t('ppv.projectCreationTime'), field: 'createTime', visible: true, width: 100 },
          { title: this.$t('ppv.projectCreator'), slots: { default: 'creator' }, field: 'creator', visible: true, width: 100 },
          { title: this.$t('rfq.projectStatus'), slots: { default: 'projectStatus' }, field: 'projectStatus', visible: true, width: 100 }
        ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      needFeedbackList: [], // 批量反馈的数据集合
      // 上传相关
      upload: {
        title: this.$t('ppv.uploadPpvData'),
        open: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        isUploading: false,
        loading: false,
        // 上传的地址
        url: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/ppv/project-material-rel-ppv/import-ppv-material-excel'
      },
      statisticsData: [{
        value: 2344,
        label: 'YTD累计询价物料数'
      },
      {
        value: 18.6,
        label: '报价覆盖率'
      },
      {
        value: 18.6,
        label: '报价成功率'
      },
      {
        value: 32000,
        label: 'YTD累计降本额（KCNY）'
      },
      {
        value: 12,
        label: 'YTD累计品牌'
      }
      ]
    }
  },
  created() {
    this.doSearch()
  },
  methods: {
    // 取消批量反馈弹出页面
    doCancelFeedback() {
      this.form = {}
      this.open = false
      this.needFeedbackList = []
    },
    /**
     * 【反馈】
     *   【✔】a.选择列表数据进行反馈，并且物料状态是【待反馈】的才能进行反馈，否则提示【只有【待反馈】的物料才能进行反馈！】
     *   【✔】b.验证项目状态是【进行中】才能反馈，否则提示【只有【进行中】状态的单据才能进行反馈！】
     *   c.如果选择一条数据，则弹出【P3编辑页面】进行维护反馈信息。
     *   d.如果选择多条数据，则弹出【批量反馈弹出页面】进行维护
     */
    doOpenFeedback() {
      const data = this.$refs.customerOverview.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      let materialErr = false
      let projectErr = false
      data.forEach(i => {
        if (i.status !== 'to_feedback') {
          materialErr = true
        }
        if (i.projectStatus !== 'ongoing') {
          projectErr = true
        }
      })

      if (materialErr) {
        this.$message.error(this.$t('ppv.onlyMaterialsThatAreAwaitingFeedbackCanBeGivenFeedback'))
        return
      }

      if (projectErr) {
        this.$message.error(this.$t('ppv.onlyDocumentsInTheinProgressStatusCanReceiveFeedback'))
        return
      }

      if (data.length > 1) {
        // d.如果选择多条数据，则弹出【批量反馈弹出页面】进行维护
        this.needFeedbackList = data
        this.open = true
      } else {
        // c.如果选择一条数据，则弹出【P3编辑页面】进行维护反馈信息。
        this.feedbackDetailVisible = true
        this.$refs.feedbackDetail.showMaterialDetail(data[0].materialRelId, data[0].quoteId)
      }
    },
    // 提交反馈信息
    // 针对选中的多行数据根据 quotaId批量更新 反馈和反馈备注
    doSubmitFeedback() {
      if (this.needFeedbackList?.length <= 0) {
        this.$message.error(this.$t('supplier.pleaseSelectData'))
      }
      batchFeedback({
        quoteIds: this.needFeedbackList.map(i => i.quoteId),
        action: this.form.action,
        comment: this.form.comment
      }).then(res => {
        this.$message.success(this.$t('ppv.feedbackSuccessful'))
        this.doCancelFeedback()
      })
    },
    // 分页请求
    doSearch() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      pageCustomerOverview(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      }).catch(_ => {

      })
    },
    // 重置
    doReset() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        searchText: '',
        mfg: null,
        mpn: null,
        erpPn: null,
        materialStatus: null,
        supplierName: null,
        meetTp: null,
        dateType: null,
        time: [],
        action: null,
        latestPpv: true
      }
      this.doSearch()
    },
    // 导出列表
    doExport() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      exportOverview(this.queryParams).then(res => {
        this.$download.excel(res, this.$t('ppv.overviewPagecustomerPerspectiveXlsx'))
        this.loading = false
      }).catch(_ => {
        this.loading = false
      })
    },
    // 导出ppv上传模板
    exportPpvTemplate() {
      exportPpvTemplate().then(response => {
        this.$download.excel(response, this.$t('ppv.ppvTemplateXlsx'))
      })
    },
    // 导入ppv上传数据
    importPPvTemplateData() {
      this.upload.loading = true
      this.$refs.upload.submit()
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.url) {
        window.open(file.url)
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    beforeUploadXls(file) {
      if (file.size > getConfig('VUE_APP_FILESIZE', process.env.VUE_APP_FILESIZE) * 1024 * 1024) {
        $modal.msgError(this.$t('common.uploadFileSizeCannotExceedm', { fileSize: getConfig('VUE_APP_FILESIZE', process.env.VUE_APP_FILESIZE) }))
        this.upload.loading = false
        return false
      }
      if (!['xls', 'xlsx'].includes(file.name.slice(file.name.lastIndexOf('.') + 1))) {
        this.upload.loading = false
        $modal.msgError(this.$t('supplier.unsupportedFileFormat'))
        return false
      }
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.loading = false
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.successNo) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.successNo.length
      }
      if (data.failureNo) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + Object.keys(data.failureNo).length
        for (const index in data.failureNo) {
          text += '<br />&nbsp;&nbsp;&nbsp;&nbsp; ' + this.$t('rfq.lineNumber') + index + '：' + data.failureNo[index]
        }
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.doSearch()
    }
  }
}
</script>

<style lang="scss" scoped>

.searchItem{
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content{
    width: calc(100% - 170px);
  }
}
.searchValue{
  width: 95%;
}
</style>
