<template>

  <div style="padding: 15px 25px">
    <common-card
      :title="$t('ppv.projectInformation')"
    >
      <el-descriptions
        :label-style="{
          width: '120px'
        }"
        :column="2"
      >
        <el-descriptions-item :label="$t('auth.authenticationDocumentCode')">{{ ppvInfo.no }}</el-descriptions-item>
        <el-descriptions-item :label="$t('common.status')">
          <dict-tag :value="ppvInfo.status" :type="DICT_TYPE.PPV_PROJECT_STATUS" />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('rfq.createdBy')">
          <dict-tag :value="ppvInfo.creator" :type="DICT_TYPE.COMMON_USERS" />
        </el-descriptions-item>
        <el-descriptions-item :label="$t('common.createTime')">
          {{ ppvInfo.createTime?dayjs(ppvInfo.createTime).format('YYYY-MM-DD'):'' }}
        </el-descriptions-item>
      </el-descriptions>
    </common-card>
    <common-card
      :title="$t('ppv.quotationSupplier')"
    >

      <el-button v-if="ppvInfo.status === 'new'" type="primary" @click="newSupplier()">{{ $t('tender.addSupplier') }}</el-button>
      <el-table :data="supplierList">
        <el-table-column
          :label="$t('supplier.supplierCode')"
          prop="supplierCode"
        />
        <el-table-column
          :label="$t('auth.supplierName')"
          prop="supplierName"
        />
        <el-table-column
          :label="$t('supplier.contacts')"
          prop="name"
        />
        <el-table-column
          :label="$t('supplier.contactNumber')"
          prop="phone"
        />
        <el-table-column
          :label="$t('supplier.mailbox')"
          prop="email"
        />
        <el-table-column
          v-if="ppvInfo.status === 'new'"
          :label="$t('common.operate')"
          prop=""
        >
          <template #default="scope">
            <el-button type="text" @click="delSupplier(scope.row)">{{ $t('common.del') }}</el-button>
          </template>
        </el-table-column>

      </el-table>
      <el-dialog
        v-if="showSupplier"
        :title="$t('supplier.addNewSupplier')"
        width="400px"
        :visible.sync="showSupplier"
      >
        <el-form
          ref="supplierAuthForm"
          :model="supplierInfo"
          label-position="left"
          label-width="160px"
        >
          <el-form-item :label="$t('rfq.selectSupplier')" prop="supplierId">
            <el-select
              v-model="supplierInfo.supplierId"
              :placeholder="$t('scar.pleaseEnterKeywordsForFuzzySearch')"
              class="valueStyle"
              clearable
              filterable
              :remote-method="doListNewAuthSupplier"
              remote
              @change="setSupplierInfo"
              @clear="clearSupplier"
            >
              <el-option
                v-for="item in canAuthSuppliers"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('supplier.shortNameOfSupplier')" prop="nameShort">
            <el-input
              v-model="supplierInfo.nameShort"
              disabled
              class="valueStyle"
            />
          </el-form-item>
          <el-form-item :label="$t('supplier.supplierCode')" prop="nameShort">
            <el-input
              v-model="supplierInfo.supplierCode"
              disabled
              class="valueStyle"
            />
          </el-form-item>
          <el-form-item size="large">
            <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
            <el-button
              :loading="loadingButton"
              type="primary"
              @click="submit"
            >{{ $t('common.submit') }}
            </el-button>
          </el-form-item>
        </el-form>

      </el-dialog>

    </common-card>
    <common-card
      style="margin-bottom: 35px"
      :title="$t('ppv.ppvList')"
    >
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.searchText	"
          style="flex: 0 1 40%"
          :placeholder="$t('ppv.manufacturerManufacturerPartNumberSupplierName')"
          clearable
          @keyup.enter.native="getList"
        />
        <el-button
          type="primary"
          plain
          @click="queryParams.pageNo = 1;getList();"
        >{{ $t('common.search') }}
        </el-button>
        <el-button
          plain
          style="margin-left: 0"
          @click="doReset"
        >{{ $t('common.reset') }}
        </el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button
            type="text"
          >
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            class="el-icon-arrow-up"
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>

      </div>

      <!--高级搜索选项-->
      <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="120px">

        <el-form-item
          class="searchItem"
          :label="$t('material.manufacturer')"
          prop="processor"
        >
          <el-input v-model="queryParams.mfg" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />

        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('ppv.erpPn')"
          prop="authStatus"
        >
          <el-input v-model="queryParams.erpPn" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />

        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('common.status')"
          prop="category"
        >
          <el-select v-model="queryParams.materialStatus" class="searchValue" multiple clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.PPV_MATERIAL_SUPPLIER_STATUS)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-form-item>

        <el-form-item
          class="searchItem"
          :label="$t('material.manufacturersPartNumber')"
          prop="purchaseOrgId"
        >
          <el-input v-model="queryParams.mpn" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />
        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('sp.supplier')"
          prop="purchaseOrgId"
        >
          <el-input v-model="queryParams.supplierName" class="searchValue" :placeholder="$t('common.pleaseEnter')" clearable />

        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('ppv.meetTpPrices')"
          prop="category"
        >
          <el-select v-model="queryParams.meetTp" class="searchValue" clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.INFRA_BOOLEAN_STRING)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('order.timeType')"
          prop="dateType"
        >
          <el-select v-model="queryParams.dateType" class="searchValue" clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.PPV_TIME_TYPE)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-form-item>
        <el-form-item class="searchItem" label=" ">
          <el-date-picker
            v-model="queryParams.time"
            class="searchValue"
            type="daterange"
            :start-placeholder="$t('common.startDate')"
            :end-placeholder="$t('common.endDate')"
            :range-separator="$t('order.to')"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item
          class="searchItem"
          :label="$t('ppv.feedback')"
          prop="companyId"
        >
          <el-select
            v-model="queryParams.action"
            :placeholder="$t('auth.pleaseSelectACompany')"
            class="searchValue"
            multiple
            clearable
            filterable
          >
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.PPV_ACTIONS)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <!--    总览列表展示-->
      <vxe-grid
        ref="overview"
        :data="list"
        :loading="loading"
        v-bind="gridOption"
      >
        <template #meetTp="{row}">
          <dict-tag v-if="row.meetTp" :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="row.meetTp==1" />
        </template>

        <template #packageMethod="{row}">
          <dict-tag :type="DICT_TYPE.PPV_PACKAGE" :value="row.packageMethod" />
        </template>

        <template #longTermSupport="{row}">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="row.longTermSupport" />
        </template>

        <template #source="{row}">
          <dict-tag :type="DICT_TYPE.PPV_SOURCE" :value="row.source" />
        </template>

        <template #package2="{row}">
          <dict-tag :type="DICT_TYPE.PPV_PACKAGE" :value="row.package2" />
        </template>

        <template #status="{row}">
          <dict-tag :type="DICT_TYPE.PPV_MATERIAL_SUPPLIER_STATUS" :value="row.status" />
        </template>

        <template #action="{row}">
          <dict-tag :type="DICT_TYPE.PPV_ACTIONS" :value="row.action" />
        </template>

        <template #creator="{row}">
          <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.creator" />
        </template>

        <template #projectStatus="{row}">
          <dict-tag :type="DICT_TYPE.PPV_PROJECT_STATUS" :value="row.projectStatus" />
        </template>

        <template #toolbar_buttons>
          <el-row :gutter="10" style="width: 100%" class="mb8">

            <el-col :span="1.5">
              <el-button
                v-if="ppvInfo.status === 'new'"
                type="primary"
                size="mini"
                plain
                @click="ppvUpload.open=true"
              >  {{ $t('ppv.uploadPpvData') }}</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                v-if="ppvInfo.status === 'new'"
                type="danger"
                size="mini"
                plain
                @click="delMaterial"
              >  {{ $t('common.del') }}</el-button>
            </el-col>
            <!--            <el-col :span="1.5">
              <el-button
                v-if="ppvInfo.status === 'ongoing'&&ppvInfo."
                type="primary"
                size="mini"
                plain
                @click="quoteUpload.open=true"
              >  {{ $t('ppv.uploadQuotationData') }}</el-button>
            </el-col>-->
            <el-col :span="1.5">
              <el-button
                v-if="ppvInfo.status === 'ongoing'&&ppvInfo.hasToFeedback"
                type="primary"
                size="mini"
                @click="doOpenFeedback"
              >  {{ $t('ppv.batchMaintenanceOfFeedbackData') }}</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                v-if="ppvInfo.status === 'ongoing'&&ppvInfo.hasToFeedback"
                type="primary"
                size="mini"
                plain
                @click="feedbackUpload.open=true"
              >  {{ $t('ppv.uploadFeedbackData') }}</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                v-if="ppvInfo.status === 'ongoing'"
                type="danger"
                size="mini"
                plain
                @click="terminateMaterial"
              >  {{ $t('rfq.termination') }}</el-button>
              <el-button
                size="mini"
                plain
                @click="doExport"
              >  {{ $t('order.download') }}</el-button>
            </el-col>

            <right-toolbar :list-id="gridOption.id" :show-search.sync="showSearch" :custom-columns.sync="gridOption.columns" @queryTable="getList" />
          </el-row>
        </template>

        <template #operate="{row}">
          <OperateDropDown
            :menu-item="[
              {
                name: row.status=='to_release'?$t('ppv.editRequirements'):$t('ppv.maintainFeedbackData'),
                show: ['to_feedback','to_release'].includes(row.status),
                action: (row)=>showMaterial(row.materialRelId,row.quoteId),
                para: row
              },
            ]"
          />
        </template>

      </vxe-grid>
      <!--分页组件-->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <detail
        v-if="materialVisible"
        ref="material"
        :material-visible.sync="materialVisible"
        @getTable="getList"
      />
    </common-card>
    <div class="fixedBottom">
      <el-button @click="$router.back()">{{ $t('sp.cancel') }}</el-button>
      <el-button v-if="!['done','terminated'].includes(ppvInfo.status)" type="danger" @click="closeProject">{{ $t('ppv.terminateTheProject') }}</el-button>
      <el-button v-if="ppvInfo.status === 'new'" type="primary" @click="sumbitSupplier">{{ $t('common.submit') }}</el-button>
      <el-button v-if="ppvInfo.status === 'ongoing'&&ppvInfo.hasToFeedback" type="primary" @click="toSupplier">{{ $t('ppv.feedbackToTheSupplier') }}</el-button>
    </div>

    <!--    PPV上传数据-->
    <el-dialog :title="ppvUpload.title" :visible.sync="ppvUpload.open" append-to-body width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="ppvUpload"
          :action="ppvUpload.url"
          :auto-upload="false"
          :disabled="ppvUpload.isUploading"
          :headers="ppvUpload.headers"
          :limit="1"
          :on-preview="onPreview"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="ppvUpload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button plain type="primary" @click="exportPpvTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" :loading="ppvUpload.loading" @click="importPPvTemplateData"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <!--    上传报价数据-->
    <el-dialog :title="quoteUpload.title" :visible.sync="quoteUpload.open" append-to-body width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="quoteUpload"
          :action="quoteUpload.url"
          :auto-upload="false"
          :disabled="quoteUpload.isUploading"
          :headers="quoteUpload.headers"
          :limit="1"
          :on-preview="onPreview"
          :on-progress="handleQuoteFileUploadProgress"
          :on-success="handleQuoteFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="quoteUpload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button plain type="primary" @click="exportQuoteTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" :loading="quoteUpload.loading" @click="importQuoteTemplateData"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <!--    上传反馈数据-->
    <el-dialog :title="feedbackUpload.title" :visible.sync="feedbackUpload.open" append-to-body width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="feedbackUpload"
          :action="feedbackUpload.url"
          :auto-upload="false"
          :data="queryParams"
          :disabled="feedbackUpload.isUploading"
          :headers="feedbackUpload.headers"
          :limit="1"
          :on-preview="onPreview"
          :on-progress="handleFeedbackFileUploadProgress"
          :on-success="handleFeedbackFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="feedbackUpload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button plain type="primary" @click="exportFeedbackTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" :loading="feedbackUpload.loading" @click="importFeedbackTemplateData"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="$t('ppv.feedbackInformation')" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" label-width="80px">

        <el-form-item :label="$t('ppv.feedback')" prop="action">
          <el-select v-model="form.action" class="searchValue" clearable>
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.PPV_ACTIONS)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('ppv.feedbackRemarks')" prop="comment">
          <el-input v-model="form.comment" type="textarea" :rows="4" :placeholder="$t('ppv.pleaseEnterFeedbackNotes')" maxlength="255" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="doCancelFeedback">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="doSubmitFeedback">{{ $t('order.determine') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getConfig } from '@/utils/config'
import {
  createProjectSupplierRel, deleteProjectMaterialRelPpv,
  deleteProjectSupplierRel, feedbackProject,
  getProjectPpv,
  getProjectSupplierRelList,
  listNewAuthSupplier, pageCustomerOverview, submitProject, terminateProject, terminateProjectMaterialRelPpv
} from '@/api/ppv'
import dayjs from 'dayjs'
import detail from './detail.vue'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { getBaseHeader } from '@/utils/request'
import {
  batchFeedback,
  exportFeedbackTemplate,
  exportOverview,
  exportPpvTemplate,
  exportQuoteTemplate
} from '@/api/ppv/overview'

export default {
  name: 'Ppvdetail/:no',
  components: { OperateDropDown, detail
  },
  data() {
    return {
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      projectId: null,
      ppvInfo: {
        id: '',
        status: '',
        no: '',
        createTime: ''
      },
      showSupplier: false,
      loadingButton: false,
      supplierInfo: {
        supplierId: '',
        supplierName: '',
        supplierCode: '',
        nameShort: ''
      },
      canAuthSuppliers: [],
      supplierList: [],
      loading: false,
      showSearch: true,
      queryParams: {
        latestPpv: false,
        searchText: '',
        mfg: '',
        mpn: '',
        erpPn: '',
        materialStatus: [],
        supplierName: '',
        meetTp: '',
        action: [],
        beginDate: '',
        endDate: '',
        dateType: '',
        projectId: 0,
        supplierId: null,
        pageNo: 1,
        pageSize: 10,
        sortField: 'id',
        sortBy: 'ASC',
        time: []
      },
      list: [],
      total: 0,
      pageNo: 1,
      pageSize: 10,
      gridOption: {
        id: 'ppvList',
        align: 'left',
        border: true,
        keepSource: false,
        maxHeight: 700,
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        columnConfig: {
          resizable: true
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { type: 'checkbox', width: 30, fixed: 'left' },
          {
            field: 'operate',
            showOverflow: false,
            slots: { default: 'operate' },
            fixed: 'right',
            title: this.$t('common.operate'),
            visible: true,
            width: 35
          },
          { title: this.$t('ppv.quotationDate'), field: 'offerDate', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('sp.supplier'), field: 'supplierName', visible: true, width: 135, fixed: 'left' },
          { title: this.$t('supplier.contacts'), field: 'name', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('supplier.mailbox'), field: 'email', visible: true, width: 135, fixed: 'left' },
          { title: this.$t('ppv.erpPn'), field: 'erpPn', visible: true, width: 135, fixed: 'left' },
          { title: this.$t('material.factory'), field: 'division', visible: true, width: 135 },
          { title: this.$t('ppv.erpManufacturerPartNumber'), field: 'erpMpn', visible: true, width: 135 },
          { title: this.$t('material.manufacturer'), field: 'mfg', visible: true, width: 135 },
          { title: this.$t('material.manufacturersPartNumber'), field: 'mpn', visible: true, width: 135 },
          { title: this.$t('rfq.targetPrice'), field: 'targetPrice', visible: true, width: 60, align: 'right' },
          { title: this.$t('ppv.quantityDemanded'), field: 'prQty', visible: true, width: 100, align: 'right' },
          { title: this.$t('ppv.requirementRemarks'), field: 'prRemark', visible: true, width: 135 },
          { title: this.$t('ppv.meetTpPrices'), slots: { default: 'meetTp' }, field: 'meetTp', visible: true, width: 100 },
          { title: this.$t('ppv.packagingMethod'), slots: { default: 'packageMethod' }, field: 'packageMethod', visible: true, width: 100 },
          { title: this.$t('ppv.existingInventory'), field: 'stockAvailable', visible: true, width: 100 },
          { title: this.$t('ppv.moq'), field: 'moq', visible: true, width: 60, align: 'right' },
          { title: this.$t('ppv.mpq'), field: 'mpq', visible: true, width: 60, align: 'right' },
          { title: this.$t('ppv.productionCycledays'), field: 'ltDay', visible: true, width: 100 },
          { title: this.$t('ppv.longTermSupport'), slots: { default: 'longTermSupport' }, field: 'longTermSupport', visible: true, width: 100 },
          { title: this.$t('rfq.price'), field: 'price', visible: true, width: 80, align: 'right' },
          { title: this.$t('order.dateOfManufacture'), field: 'dc', visible: true, width: 100 },
          { title: this.$t('avpl.source'), slots: { default: 'source' }, field: 'source', visible: true, width: 100 },
          { title: this.$t('common.remarks'), field: 'remark', visible: true, width: 135 },
          { title: this.$t('ppv.rebate'), field: 'rebate', visible: true, width: 100 },
          { title: this.$t('ppv.releaseDate'), field: 'dateRelease', visible: true, width: 100 },
          { title: this.$t('ppv.deliveryDate'), field: 'deliveryDate', visible: true, width: 100 },
          { title: this.$t('ppv.mpq2'), field: 'mpq2', visible: true, width: 60, align: 'right' },
          { title: this.$t('rfq.deliveryCycle'), field: 'leadTime', visible: true, width: 100 },
          { title: this.$t('ppv.packagingMethod2'), slots: { default: 'package2' }, field: 'package2', visible: true, width: 100 },
          { title: this.$t('ppv.priceValidityPeriod'), field: 'validDate', visible: true, width: 100 },
          { title: this.$t('ppv.feedbackRemarks'), field: 'comment', visible: true, width: 135 },
          { title: this.$t('ppv.feedback'), slots: { default: 'action' }, field: 'action', visible: true, width: 110 },
          { title: this.$t('common.status'), slots: { default: 'status' }, field: 'status', visible: true, width: 100 }
        ],
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      ppvUpload: {
        title: this.$t('ppv.uploadPpvData'),
        open: false,
        isUploading: false,
        loading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/ppv/project-material-rel-ppv/import-ppv-material-excel-by-project?projectId=' + this.projectId
      },
      // 报价上传
      quoteUpload: {
        title: this.$t('ppv.uploadQuotationData'),
        open: false,
        isUploading: false,
        loading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/ppv/project-material-rel-ppv/import-supplier-quote-excel?projectId=' + this.projectId
      },
      // 反馈上传
      feedbackUpload: {
        title: this.$t('ppv.uploadFeedbackData'),
        open: false,
        isUploading: false,
        loading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/ppv/project-material-rel-ppv/import-supplier-quote-excel?projectId=' + this.projectId
      },
      materialVisible: false
    }
  },
  mounted() {
    this.projectId = this.$route.query.projectId
    this.init()
    this.getSupplier()
    this.getList()
    this.ppvUpload.url = getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/ppv/project-material-rel-ppv/import-ppv-material-excel-by-project?projectId=' + this.projectId
    this.quoteUpload.url = getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/ppv/project-material-supplier-quote/import-supplier-quote-excel?projectId=' + this.projectId
    this.feedbackUpload.url = getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/ppv/project-material-supplier-feedback/import-supplier-quote-excel?projectId=' + this.projectId
  },
  methods: {
    dayjs,
    init() {
      getProjectPpv({
        projectId: this.projectId,
        supplierId: null
      }).then(res => {
        this.ppvInfo = res.data
      })
    },
    getList() {
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      pageCustomerOverview({
        ...this.queryParams,
        projectId: this.projectId
      }).then(res => {
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    getSupplier() {
      getProjectSupplierRelList(this.projectId).then(res => {
        this.supplierList = res.data
      })
    },
    doListNewAuthSupplier(query) {
      if (query) {
        listNewAuthSupplier(query).then(res => {
          this.canAuthSuppliers = res.data
        })
      }
    },
    submit() {
      this.$refs['supplierAuthForm'].validate(valid => {
        if (valid) {
          this.loadingButton = true
          createProjectSupplierRel({ ...this.supplierInfo,

            projectId: this.projectId
          }).then(res => {
            this.$message.success(this.$t('supplier.submittedSuccessfully'))
            this.getSupplier()
            this.getList()
            this.cancel()
            this.loadingButton = false
          }).catch(_ => {
            this.loadingButton = false
          })
        }
      })
    },
    cancel() {
      this.$refs['supplierAuthForm'].resetFields()
      this.showSupplier = false
    },
    clearSupplier() {
      this.$refs['supplierAuthForm'].resetFields()
      this.showSupplier = false
    },
    setSupplierInfo(id) {
      this.supplierInfo.supplierName = this.canAuthSuppliers.find(item => item.id === this.supplierInfo.supplierId).name
      this.supplierInfo.supplierCode = this.canAuthSuppliers.find(item => item.id === this.supplierInfo.supplierId).code
    },
    newSupplier() {
      this.showSupplier = true
    },
    delSupplier(row) {
      this.$modal.confirm(this.$t('ppv.areYouSureYouWantToDeleteIt')).then(() => {
        deleteProjectSupplierRel(row.id).then(res => {
          this.$message.success(this.$t('common.delSuccess'))
          this.getSupplier()
          this.getList()
        })
      })
    },
    doReset() {
      this.queryParams = {
        latestPpv: false,
        searchText: '',
        mfg: '',
        mpn: '',
        erpPn: '',
        materialStatus: [],
        supplierName: '',
        meetTp: '',
        action: [],
        beginDate: '',
        endDate: '',
        dateType: '',
        projectId: 0,
        supplierId: null,
        pageNo: 1,
        pageSize: 10,
        sortField: 'id',
        sortBy: 'ASC',
        time: []
      }
      this.getList()
    },
    delMaterial() {
      const records = this.$refs.overview.getCheckboxRecords()
      if (records.length < 1) {
        this.$message.error(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.$modal.confirm(this.$t('ppv.areYouSureYouWantToDeleteIt')).then(() => {
        deleteProjectMaterialRelPpv(records.map(a => a.quoteId).join(',')).then(res => {
          this.$message.success(this.$t('common.delSuccess'))
          this.getList()
        })
      })
    },
    terminateMaterial() {
      const records = this.$refs.overview.getCheckboxRecords()
      if (records.length < 1) {
        this.$message.error(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.$modal.confirm(this.$t('ppv.areYouSureYouWantToTerminate')).then(() => {
        terminateProjectMaterialRelPpv(records.map(a => a.quoteId).join(',')).then(res => {
          this.$message.success(this.$t('rfq.terminatedSuccessfully'))
          this.getList()
        })
      })
    },
    closeProject() {
      this.$modal.confirm(this.$t('ppv.areYouSureYouWantToTerminate')).then(() => {
        terminateProject(this.projectId).then(res => {
          this.$message.success(this.$t('rfq.terminatedSuccessfully'))
          this.$tab.closeOpenPage('/ppv/ppvindex')
        })
      })
    },
    sumbitSupplier() {
      submitProject(this.projectId).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.$tab.closeOpenPage('/ppv/ppvindex')
      })
    },
    toSupplier() {
      feedbackProject(this.projectId).then(res => {
        this.$message.success(this.$t('order.operationSucceeded'))
        this.$tab.closeOpenPage('/ppv/ppvindex')
      })
    },
    // 取消批量反馈弹出页面
    doCancelFeedback() {
      this.form = {}
      this.open = false
      this.needFeedbackList = []
    },
    // 提交反馈信息
    // 针对选中的多行数据根据 quotaId批量更新 反馈和反馈备注
    doSubmitFeedback() {
      if (this.needFeedbackList?.length <= 0) {
        this.$message.error(this.$t('supplier.pleaseSelectData'))
      }
      batchFeedback({
        quoteIds: this.needFeedbackList.map(i => i.quoteId),
        action: this.form.action,
        comment: this.form.comment
      }).then(res => {
        this.$message.success(this.$t('ppv.feedbackSuccessful'))
        this.doCancelFeedback()
        this.getList()
      })
    },
    /**
     * 【反馈】
     *   【✔】a.选择列表数据进行反馈，并且物料状态是【待反馈】的才能进行反馈，否则提示【只有【待反馈】的物料才能进行反馈！】
     *   【✔】b.验证项目状态是【进行中】才能反馈，否则提示【只有【进行中】状态的单据才能进行反馈！】
     *   c.如果选择一条数据，则弹出【P3编辑页面】进行维护反馈信息。
     *   d.如果选择多条数据，则弹出【批量反馈弹出页面】进行维护
     */
    doOpenFeedback() {
      const data = this.$refs.overview.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      let materialErr = false
      let projectErr = false
      data.forEach(i => {
        if (i.status !== 'to_feedback') {
          materialErr = true
        }
        if (i.projectStatus !== 'ongoing') {
          projectErr = true
        }
      })

      if (materialErr) {
        this.$message.error(this.$t('ppv.onlyMaterialsThatAreAwaitingFeedbackCanBeGivenFeedback'))
        return
      }

      if (projectErr) {
        this.$message.error(this.$t('ppv.onlyDocumentsInTheinProgressStatusCanReceiveFeedback'))
        return
      }

      if (data.length > 1) {
        // d.如果选择多条数据，则弹出【批量反馈弹出页面】进行维护
        this.needFeedbackList = data
        this.open = true
      } else {
        this.showMaterial(data[0].materialRelId, data[0].quoteId)
      }
    },
    // 导出ppv上传模板
    exportPpvTemplate() {
      exportPpvTemplate().then(response => {
        this.$download.excel(response, this.$t('ppv.ppvTemplateXlsx'))
      })
    },
    // 导入ppv上传数据
    importPPvTemplateData() {
      this.$refs.ppvUpload.submit()
      this.ppvUpload.loading = true
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.url) {
        window.open(file.url)
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.ppvUpload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.ppvUpload.loading = false
      this.ppvUpload.open = false
      this.ppvUpload.isUploading = false
      this.$refs.ppvUpload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.successNo) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.successNo.length
      }
      if (data.failureNo) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + Object.keys(data.failureNo).length
        for (const index in data.failureNo) {
          text += '<br />&nbsp;&nbsp;&nbsp;&nbsp; ' + this.$t('rfq.lineNumber') + index + '：' + data.failureNo[index]
        }
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },

    // 导出报价数据模板
    exportQuoteTemplate() {
      exportQuoteTemplate(this.projectId).then(response => {
        this.$download.excel(response, this.$t('ppv.quotationDataTemplateXlsx'))
      })
    },
    // 导入报价上传数据
    importQuoteTemplateData() {
      this.$refs.quoteUpload.submit()
      this.quoteUpload.loading = true
    },

    // 报价文件上传中处理
    handleQuoteFileUploadProgress(event, file, fileList) {
      this.quoteUpload.isUploading = true
    },
    // 报价文件上传成功处理
    handleQuoteFileSuccess(response, file, fileList) {
      this.quoteUpload.loading = false
      this.quoteUpload.open = false
      this.quoteUpload.isUploading = false
      this.$refs.quoteUpload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.successNo) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.successNo.length
      }
      if (data.failureNo) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + Object.keys(data.failureNo).length
        for (const index in data.failureNo) {
          text += '<br />&nbsp;&nbsp;&nbsp;&nbsp; ' + this.$t('rfq.lineNumber') + index + '：' + data.failureNo[index]
        }
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },

    // 导出反馈数据模板
    exportFeedbackTemplate() {
      exportFeedbackTemplate(this.projectId).then(response => {
        this.$download.excel(response, this.$t('ppv.feedbackDataTemplateXlsx'))
      })
    },
    // 导入反馈上传数据
    importFeedbackTemplateData() {
      this.$refs.feedbackUpload.submit()
      this.feedbackUpload.loading = true
    },

    // 反馈文件上传中处理
    handleFeedbackFileUploadProgress(event, file, fileList) {
      this.feedbackUpload.isUploading = true
    },
    // 反馈文件上传成功处理
    handleFeedbackFileSuccess(response, file, fileList) {
      this.feedbackUpload.loading = false
      this.feedbackUpload.open = false
      this.feedbackUpload.isUploading = false
      this.$refs.feedbackUpload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.successNo) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.successNo.length
      }
      if (data.failureNo) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + Object.keys(data.failureNo).length
        for (const index in data.failureNo) {
          text += '<br />&nbsp;&nbsp;&nbsp;&nbsp; ' + this.$t('rfq.lineNumber') + index + '：' + data.failureNo[index]
        }
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 导出列表
    doExport() {
      this.loading = true
      if (this.queryParams.time?.length) {
        this.queryParams.beginDate = this.queryParams.time[0] ? this.queryParams.time[0] + 'T00:00:00' : undefined
        this.queryParams.endDate = this.queryParams.time[1] ? this.queryParams.time[1] + 'T23:59:59' : undefined
      }
      this.queryParams.projectId = this.projectId
      exportOverview(this.queryParams).then(res => {
        this.$download.excel(res, this.$t('ppv.ppvChecklistxlsx'))
        this.loading = false
      }).catch(_ => {
        this.loading = false
      })
    },
    showMaterial(materialRelId, quoteId) {
      this.materialVisible = true
      this.$nextTick(() => {
        this.$refs.material.showMaterialDetail(materialRelId, quoteId)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

::v-deep .gray-row {
  background: #f1f2f6;;
}

.searchTimeItem {
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 82px);
  }
}

.searchValue {
  width: 95%;
}

</style>
