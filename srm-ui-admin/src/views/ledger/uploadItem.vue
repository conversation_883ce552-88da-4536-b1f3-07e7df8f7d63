<template>
  <div>
    <div style="display: flex;" @click="onclick">
      <el-upload
        ref="upload"
        class="upload-demo"
        style="margin-right: 20px"
        :disabled="key===-1"
        :action="uploadUrl"
        :headers="getBaseHeader()"
        :before-upload="beforeUpload"
        :on-success="onSuccess"
        multiple
        :show-file-list="false"
        :file-list="uploadFileList"
      >
        <el-button
          class="uploadBtn"
          size="small"
          plain
          icon="el-icon-plus"
          type="primary"
        />
      </el-upload>
      <div>
        查看附件
        <el-button
          class="uploadBtn"
          size="small"
          style="padding: 5px 9px"
          :disabled="fileList.length===0"
          plain
          :type="fileList.length?'primary':''"
          @click="showFile=true"
        >
          {{ fileList.length }}
        </el-button>

        <el-dialog
          v-if="showFile"
          :visible.sync="showFile"
          title="查看附件"
          width="400px"
        >
          <div  class="upload-show">
            <div class="el-upload el-upload--text">
              <input type="file" name="file" multiple="multiple" class="el-upload__input">
            </div>
            <ul class="el-upload-list el-upload-list--text">
              <li v-for="(file, index) in fileList" :key="index" class="el-upload-list__item is-success">
                <el-link :href="`${file.url}`" :underline="false" target="_blank">
                  <span class="el-icon-document" :title="file.name"> {{ file.name }} </span>
                </el-link>
                <label class="el-upload-list__item-status-label">
                  <i class="el-icon-upload-success el-icon-circle-check"></i>
                </label>
                <i class="el-icon-close" @click="handleDelete(index)"></i>
                <i class="el-icon-close-tip">按 delete 键可删除</i>
              </li>
            </ul>
          </div>
          <div slot="footer">
            <el-button type="primary" @click="showFile=false">确定</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
    <!-- 上传提示 -->
    <div v-if="isShowTip" class="el-upload__tip">
      请上传
      <template> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b></template>
      <br/>
      <template> 格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b>的文件</template>
    </div>
  </div>
</template>
<script>
import { getBaseHeader } from '@/utils/request'
import { getConfig } from '@/utils/config'

export default {
  name: 'FileUpload',
  props: {
    // 值
    value: {
      type: Array,
      default: []
    },
    key: {
      type: Number,
      default: -1
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'pdf']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: false
    },
    // 数量限制
    limit: {
      type: Number,
      default: 5
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      getBaseHeader,
      showFile: false,
      fileList: [],
      uploadFileList: [],
      uploadUrl: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload'
    }
  },
  created() {
  },
  mounted() {
    this.$refs.upload.clearFiles()
  },
  watch: {
    value: {
      handler(val) {
        console.log('val',val)
        if(val) {
          this.fileList = [...val]
        }
      },
      deep: true
    }
  },
  methods: {
    onclick() {
      if (this.key === -1) {
        this.$message.error(this.$t(`请先保存单据，再进行上传`))
        return false
      }
    },
    beforeUpload(file) {
      console.log('beforeUpload file=====> ', file)
      if (file.size > this.fileSize * 1024 * 1024) {
        this.$message.error(this.$t(`文件大小不得超出${this.fileSize}MB，文件上传失败`))
        return false
      }
      if (this.fileType && this.fileType.length > 0 && !this.fileType.includes(file.name.slice(file.name.lastIndexOf('.') + 1))) {
        this.$message.error(this.$t('order.onlySupported') + this.fileType + this.$t('order.format'))
        return false
      }
    },
    onSuccess(response) {
      console.log('response =====> ', response)
      if (this.limit <= this.fileList.length) {
        this.$message.error(this.$t(`最多可上传${this.limit}个文件，文件上传失败`))
        return
      }
      this.fileList.push(response.data)
      this.$message.success(this.$t('common.uploadSucceeded'))
      console.log('fileList =====> ', this.fileList)
      this.$emit('input', this.fileList)
      this.$emit('bind', response.data, this.fileList)
    },
    handleDelete(index) {
      console.log('handleDelete =====> ', index)
      console.log('handleDelete =====> ', this.uploadFileList)
      this.fileList.splice(index, 1)
      this.$message.success(this.$t('common.delSuccess'))
      console.log('fileList =====> ', this.fileList)
    }
  }
}
</script>
<style scoped lang="scss">
.upload-file-list {
  .el-upload-list__item {
    // border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
  }

  .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }

  .el-link--inner span {
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 320px;
  }
}
</style>
