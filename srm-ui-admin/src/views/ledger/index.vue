<template>
  <div class="app-container">
    <div class="rfqHome-search">

      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.keyword"
          :placeholder="$t('请输入物料描述、供应商名称、批号、订单号')"
          clearable
          style="flex: 0 1 40%"
          @keyup.enter.native="queryParams.pageNo = 1;getList();"
        />
        <el-button type="primary" plain @click="queryParams.pageNo = 1;getList();">{{ $t('搜索') }}</el-button>
        <el-button plain style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('高级搜索') }}

          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
          />
        </div>

      </div>
      <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="120px" size="small">
        <el-form-item :label="$t('公司')" class="searchItem">
          <el-select
            v-model="queryParams.companyIds"
            :placeholder="$t('请选择公司')"
            class="searchValue"
            clearable
            filterable
            multiple
          >
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.COMMON_COMPANY, 0)"
              :key="item.value"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('检测人')" class="searchItem">
          <el-select v-model="queryParams.inspector" class="searchValue" clearable filterable>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('检验时间')" class="searchItem" prop="deliveryNoteNo">
          <el-date-picker
            v-model="queryParams.inspectionDateRange"
            :end-placeholder="$t('开始日期')"
            :range-separator="$t('至')"
            :start-placeholder="$t('结束日期')"
            class="searchValue"
            type="daterange"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item :label="$t('工厂')" class="searchItem">
          <el-select
            v-model="queryParams.factoryIds"
            :placeholder="$t('请选择工厂')"
            class="searchValue"
            clearable
            filterable
            multiple
          >
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.COMMON_FACTORY, 0)"
              :key="item.value"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('物料品类')" class="searchItem">
          <cascading-category
            class="searchValue"
            :placeholder="$t('请选择物料品类')"
            :multiple="false"
            :original-value.sync="queryParams.categoryId"
          />
        </el-form-item>
        <el-form-item :label="$t('验收结果')" class="searchItem">
          <el-select
            v-model="queryParams.acceptanceResult"
            :placeholder="$t('请选择验收结果')"
            class="searchValue"
            clearable
            filterable
          >
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.LEDGER_ACCEPTANCE_RESULT, 0)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('状态')" class="searchItem">
          <el-select
            v-model="queryParams.statusList"
            :placeholder="$t('请选择状态')"
            class="searchValue"
            clearable
            multiple
            filterable
          >
            <el-option
              v-for="item in getDictDatas(DICT_TYPE.LEDGER_STATUS, 0)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('供应商名称')" class="searchItem">
          <el-input
            v-model="queryParams.supplierName"
            :placeholder="$t('请输入供应商名称')"
            clearable
            class="searchValue"
          />
        </el-form-item>

      </el-form>

    </div>

    <vxe-grid
      ref="ledgerIndex"
      :data="dataList"
      :loading="loading"
      v-bind="girdOption"
      @sort-change="sortMethod"
    >
      <template #company="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="row.companyId"/>
      </template>
      <template #unit="{row}">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.unit"/>
      </template>
      <template #vehicleCondition="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_VEHICLE_CONDITION" :value="row.vehicleCondition"/>
      </template>
      <template #acceptanceResult="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_ACCEPTANCE_RESULT" :value="row.acceptanceResult"/>
      </template>
      <template #nonConformityHandling="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_NON_CONFORMITY_HANDLING" :value="row.nonConformityHandling"/>
      </template>
      <template #inspector="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.inspector"/>
      </template>
      <template #scarCodeList="{row}">
        <span v-if="row.scarCodeList">
           <span v-for="(item, index) in row.scarCodeList.split(',')" :key="item">
              <el-button
                type="text"
                size="mini"
                style="text-decoration: underline;"
                @click="$router.push(`/scar/iqc/${item}?id=${row.scarIdList.split(',')[index]}`)"
              >
              {{ item }}
              </el-button>
             <span v-if="index!==row.scarCodeList.split(',').length-1">
                ,
             </span>
        </span>
        </span>
      </template>
      <template #specification="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_ACCEPTANCE_RESULT" :value="row.specification"/>
      </template>
      <template #appearance="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.appearance"/>
      </template>
      <template #functionality="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_ACCEPTANCE_RESULT" :value="row.functionality"/>
      </template>
      <template #textPattern="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.textPattern"/>
      </template>
      <template #printing="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.printing"/>
      </template>
      <template #sealing="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.sealing"/>
      </template>
      <template #dropDown="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.dropDown"/>
      </template>
      <template #adhesiveness="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.adhesiveness"/>
      </template>
      <template #antiFog="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.antiFog"/>
      </template>
      <template #colorAndFreshness="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.colorAndFreshness"/>
      </template>
      <template #smell="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.smell"/>
      </template>
      <template #textureShape="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.textureShape"/>
      </template>
      <template #impurityDetectionMethod="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.impurityDetectionMethod"/>
      </template>
      <template #visualInspection="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.visualInspection"/>
      </template>
      <template #granularityCompliance="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.granularityCompliance"/>
      </template>
      <template #outerPackaging="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.outerPackaging"/>
      </template>
      <template #packagingSpecification="{row}">
        <dict-tag :type="DICT_TYPE.LEDGER_APPEARANCE" :value="row.packagingSpecification"/>
      </template>

      <template #operate="{row}">
        <OperateDropDown

          :menu-item="[
            {
              name: row.status === 'draft' ? $t('编辑') : $t('修改'),

              show: row.status !== 'invalidate'&&$store.getters.permissions.includes('ledger:record-info:update'),
              action: (row) => $router.push(`/ledger/ledgerinfo/${row.batchNumber}?id=${row.id}&reportDetailType=${row.reportDetailType}`),
              para: row
            },
            {
              name: $t('作废'),

              show: ['draft', 'normal'].includes(row.status) &&$store.getters.permissions.includes('ledger:record-info:invalidate'),
              action: (row) => invalidate(row),
              para: row
            },
            {
              name: $t('操作记录'),
              show: ['draft', 'normal'].includes(row.status) &&$store.getters.permissions.includes('ledger:record-info:invalidate'),
              action: (row) => {log.open = true;log.businessId=row.id},
              para: row
            }
          ]"
        />
      </template>

      <template #toolbar_buttons>
        <el-row :gutter="10" class="mb8" style="width: 100%">
          <el-col :span="22">
            <el-row :gutter="10" class="mb8">
              <div style="display: flex;justify-content: space-between;flex-wrap: wrap;">
                <el-scrollbar>
                  <el-col :span="1.5">
                    <el-button
                      v-has-permi="['ledger:record-info:create']"
                      icon="el-icon-plus"
                      size="mini"
                      type="primary"
                      @click="$router.push(`/ledger/ledgerinfo/${null}`)"
                    >{{ $t('新增验货记录') }}
                    </el-button>
                  </el-col>
                  <el-col :span="1.5">
                    <el-button
                      v-has-permi="['ledger:record-info:export']"
                      icon="el-icon-download"
                      size="mini"
                      type="primary"
                      plain
                      :loading="exportLoading"
                      @click="handleExport"
                    >{{ $t('下载') }}
                    </el-button>
                  </el-col>
                  <el-col :span="1.5">
                    <el-dropdown trigger="click" @command="handleImport">
                      <el-button
                        v-has-permi="['ledger:record-info:import']"
                        type="primary"
                        plain
                        size="mini"
                      >{{ $t('批量新增') }}
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item :command="item.value" v-for="(item,index) in customScheme">
                          {{ item.label }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </el-col>
                  <el-col :span="1.5">
                    <el-button
                      v-has-permi="['ledger:record-info:print']"
                      size="mini"
                      plain
                      type="primary"
                      @click="printPdf"
                    >
                      {{ $t('打印报告') }}
                    </el-button>
                  </el-col>
                </el-scrollbar>
                <el-scrollbar>
                  <el-col v-for="(item,index) in customScheme" :span="1.5" style="margin-left: 5px">
                    <el-button
                      :plain="queryParams.reportDetailType !== item.value"
                      size="mini"
                      type="primary"
                      @click="selectScheme(item)"
                    >{{ item.label }}
                    </el-button>
                  </el-col>
                </el-scrollbar>
              </div>
            </el-row>
          </el-col>
          <el-col :span="2">
            <right-toolbar
              :custom-columns.sync="fixColumn"
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              @queryTable="getList"
            />
          </el-col>
        </el-row>
      </template>
    </vxe-grid>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 上传 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" append-to-body width="500px">
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="uploadAction"
          :auto-upload="false"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload"/>
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
    <!-- 预览 -->
    <el-dialog
      v-if="showPreview.open"
      :close-on-click-modal="false"
      :visible.sync="showPreview.open"
      :title="$t('order.preview')"
      width="1200px"
    >
      <report-preview
        :width="1150"
        :report-id="showPreview.reportId"
        :params="showPreview.params"
      />
    </el-dialog>
    <!-- 操作记录 -->
    <el-dialog
      v-if="log.open"
      :close-on-click-modal="false"
      :title="$t('common.operationRecord')"
      :visible.sync="log.open"
      width="900px"
    >
      <operation-record
        :business-id="log.businessId"
        :columns="log.columns"
        :log-visible.sync="log.open"
      />
    </el-dialog>
  </div>
</template>

<script>
import operationRecord from '@/components/OperationRecord/operationRecord'
import { exportExcel, invalidate, pageList, importTemplate } from '@/api/ledger'
import { DICT_TYPE } from '@/utils/dict'
import { listFactoryFromCache } from '@/api/system/factory'
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import { getBaseHeader } from '@/utils/request'
import { getConfig } from '@/utils/config'
import Cookies from 'js-cookie'
import reportPreview from '@/components/reportPreview/reportPreview'

export default {
  name: 'Ledgerindex',
  components: { OperateDropDown, reportPreview, operationRecord },
  data() {
    return {
      importFlag: false,
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: '',
        type: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/ledger/record-info/import'
      },
      log: {
        // 是否显示弹出层
        open: false,
        businessId: null,
        columns: [
          {
            tableName: 'ledger_record_info',
            keyColumns: [
              'companyId', 'inspectionDate', 'orderItemNumber', 'materialName',
              'materialDescription', 'batchNumber', 'supplierName', 'totalQuantity',
              'unit', 'licensePlateNumber', 'vehicleCondition', 'acceptanceResult',
              'nonConformityHandling', 'inspector',
              'warehouseNumber', 'reportDetailType',
              'status', 'remark',
              'categoryId', 'qualityComplaintType'
            ]
          },
          {
            tableName: 'ledger_general_report_details',
            keyColumns: [
              'specification', 'appearance', 'functionality', 'extendField1'
            ]
          },
          {
            tableName: 'ledger_packaging_material_report_detail',
            keyColumns: [
              'specification', 'appearance', 'textPattern', 'printing',
              'lengthMm', 'widthMm', 'heightFoldedMm', 'thicknessMm',
              'diameter', 'weightG', 'sealing', 'compressionKgf',
              'dropDown', 'adhesiveness', 'antiFog', 'compressionStrengthKgf1',
              'compressionStrengthKgf2', 'compressionStrengthKgf3', 'compressionStrengthKgf4', 'compressionStrengthKgf5',
              'compressionStrengthKgfAvg', 'manufacturerCompressionStrengthKgf', 'retestCompressionStrengthKgf1', 'retestCompressionStrengthKgf2',
              'retestCompressionStrengthKgf3', 'retestCompressionStrengthKgf4', 'retestCompressionStrengthKgf5', 'retestCompressionStrengthKgfAvg',
              'extendField1', 'extendField2', 'extendField3', 'extendField4', 'extendField5'
            ]
          },
          {
            tableName: 'ledger_raw_material_report_details',
            keyColumns: [
              'colorAndFreshness', 'smell',
              'textureShape', 'impurityPercentage',
              'impurityDetectionMethod', 'visualInspection',
              'granularityMm02', 'granularityMm26',
              'granularityMm68', 'granularityMmAbove8',
              'granularityOther', 'granularityCompliance',
              'ph', 'moisturePercentage',
              'waterAbsorptionPercentage', 'settlingVolume',
              'otherPhysical1', 'otherPhysical2',
              'outerPackaging', 'packagingSpecification',
              'extendField1', 'extendField2',
              'extendField3', 'extendField4',
              'extendField5'
            ]
          },
          {
            tableName: 'ledger_record_info_factory_scar',
            keyColumns: [
              'factoryId', 'scarCode'
            ]
          }
        ]
      },
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 字典表格数据
      dataList: [],
      factoryList: [],
      customScheme: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        keyword: '',
        inspector: '',
        categoryId: '',
        acceptanceResult: '',
        supplierName: '',
        status: '',
        statusList: ['draft', 'normal'],
        reportDetailType: 'raw_material_report_details',
        inspectionDateRange: [],
        companyIds: [],
        factoryIds: []
      },
      showPreview: {
        // 是否显示弹出层（物料导入）
        open: false,
        reportId: undefined,
        params: null
      },
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'ledgerIndexTable',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isCurrent: true,
          isHover: true
        },
        columns: [],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      fixColumn: [
        { type: 'checkbox', width: 30, visible: true,fixed:'left' },
        {
          title: this.$t('公司'),
          sortable: true,
          slots: { default: 'company' },
          field: 'companyId',
          visible: true,
          width: 100
        },
        { title: this.$t('检验日期'), sortable: true, field: 'inspectionDate', visible: true, width: 100 },
        {
          title: this.$t('订单号码-行项目'),
          field: 'orderItemNumber',
          visible: true, width: 100
        },
        {
          title: this.$t('物料名称'),
          field: 'materialDescription', sortable: true,
          visible: true, width: 100
        },
        { title: this.$t('检验批次号'), sortable: true, field: 'batchNumber', visible: true, width: 150 },
        {
          title: this.$t('供应商'), sortable: true, field: 'supplierName',
          visible: true, width: 100
        },
        {
          title: this.$t('总数量'),
          field: 'totalQuantity',
          visible: true, width: 100
        },
        {
          title: this.$t('单位'),
          field: 'unit',
          slots: { default: 'unit' },
          visible: true, width: 100
        },
        { title: this.$t('车牌号'), sortable: true, field: 'licensePlateNumber', visible: true, width: 100 },
        {
          title: this.$t('车辆状况'),
          field: 'vehicleCondition', slots: { default: 'vehicleCondition' }, visible: true, width: 100
        },
        {
          title: this.$t('验收结果'), sortable: true,
          slots: { default: 'acceptanceResult' },
          field: 'acceptanceResult', visible: true, width: 100
        },
        {
          title: this.$t('不合格处理'),
          field: 'nonConformityHandling',
          visible: true,
          slots: { default: 'nonConformityHandling' },
          width: 100
        },
        { title: this.$t('检测人'), field: 'inspector', visible: true, slots: { default: 'inspector' }, width: 100 },
        { title: this.$t('使用工厂'), field: 'factoryNameList', visible: true, width: 100 },
        { title: this.$t('库房号'), field: 'warehouseNumber', visible: true, width: 100 },
        { title: this.$t('判定结果备注'), field: 'remark', visible: true, width: 100 },
        {
          title: this.$t('质量改善单'),
          field: 'scarCodeList',
          visible: true,
          slots: { default: 'scarCodeList' },
          width: 150
        }
      ],
      generalColumn: [
        {
          title: this.$t('规格'),
          slots: { default: 'specification' },
          field: 'specification', visible: true, width: 100
        },
        {
          title: this.$t('外观'),
          slots: { default: 'appearance' },
          field: 'appearance', visible: true, width: 100
        },
        {
          title: this.$t('功能'),
          slots: { default: 'functionality' },
          field: 'functionality', visible: true, width: 100
        },
        { title: this.$t('其他检测项备注'), field: 'extendField1', visible: true, width: 100 }
      ],
      packageColumn: [
        { title: this.$t('规格'), field: 'specification', visible: true, width: 100 },
        {
          title: this.$t('外观'),
          slots: { default: 'appearance' },
          field: 'appearance', visible: true, width: 100
        },
        {
          title: this.$t('文字图案'),
          slots: { default: 'textPattern' },
          field: 'textPattern', visible: true, width: 100
        },
        {
          title: this.$t('印刷'),
          slots: { default: 'printing' },
          field: 'printing', visible: true, width: 100
        },
        {
          title: this.$t('其他检测项备注'),
          field: 'extendField5', visible: true, width: 100
        },
        {
          title: this.$t('检测项目'), align: 'center',
          children: [
            {
              title: '尺寸', align: 'center',
              children: [
                { field: 'lengthMm', title: '长度(mm)', width: 100 },
                { field: 'widthMm', title: '宽度(mm)', width: 100 },
                { field: 'heightFoldedMm', title: '高度/折边(mm)', width: 100 },
                { field: 'thicknessMm', title: '厚度(mm)', width: 100 },
                { field: 'diameter', title: '筒径', width: 100 },
                { field: 'extendField1', title: '其他尺寸备注', width: 100 }
              ]
            },
            { field: 'weightG', title: '重量(g)', width: 100 },
            { field: 'extendField3', title: '其他重量备注', width: 100 },
            {
              title: '性能', align: 'center',
              children: [
                { field: 'sealing', title: '密封性', slots: { default: 'sealing' }, width: 100 },
                { field: 'compressionKgf', title: '抗压（kgf）', width: 100 },
                { field: 'dropDown', title: '跌落', slots: { default: 'dropDown' }, width: 100 },
                { field: 'adhesiveness', title: '粘性', slots: { default: 'adhesiveness' }, width: 100 },
                { field: 'antiFog', title: '防雾', slots: { default: 'antiFog' }, width: 100 },
                { field: 'extendField6', title: '耐破值', width: 100 },
                { field: 'waterContent', title: '含水量', width: 100 },
                { field: 'extendField2', title: '其他性能备注', width: 100 }
              ]
            }
          ]
        },
        {
          title: this.$t('抗压强度kgf'), align: 'center',
          children: [
            { field: 'compressionStrengthKgf1', title: '1', width: 100 },
            { field: 'compressionStrengthKgf2', title: '2', width: 100 },
            { field: 'compressionStrengthKgf3', title: '3', width: 100 },
            { field: 'compressionStrengthKgf4', title: '4', width: 100 },
            { field: 'compressionStrengthKgf5', title: '5', width: 100 },
            { field: 'compressionStrengthKgfAvg', title: '平均值', width: 100 }

          ]
        },
        {
          title: this.$t('厂家抗压强度kgf'),
          field: 'manufacturerCompressionStrengthKgf', visible: true, width: 100
        },
        {
          title: this.$t('其他抗压备注'),
          field: 'extendField4', visible: true, width: 100
        },
        {
          title: this.$t('复测抗压强度kgf'), align: 'center',
          children: [
            { field: 'retestCompressionStrengthKgf1', title: '1', width: 100 },
            { field: 'retestCompressionStrengthKgf2', title: '2', width: 100 },
            { field: 'retestCompressionStrengthKgf3', title: '3', width: 100 },
            { field: 'retestCompressionStrengthKgf4', title: '4', width: 100 },
            { field: 'retestCompressionStrengthKgf5', title: '5', width: 100 },
            { field: 'retestCompressionStrengthKgfAvg', title: '平均值', width: 100 }
          ]
        }
      ],
      rawColumn: [
        {
          title: this.$t('外观和感官'), align: 'center',
          children: [
            {
              field: 'colorAndFreshness',
              title: '色泽（含色泽描述和新鲜情况等）',
              slots: { default: 'colorAndFreshness' },
              width: 100
            },
            { field: 'smell', title: '气味', slots: { default: 'smell' }, width: 100 },
            { field: 'textureShape', title: '组织状态（形状）', slots: { default: 'textureShape' }, width: 100 },
            { field: 'impurityPercentage', title: '杂质%', width: 100 },
            {
              field: 'impurityDetectionMethod',
              title: '杂质检测法',
              slots: { default: 'impurityDetectionMethod' },
              width: 100
            },
            { field: 'visualInspection', title: '镜检情况', slots: { default: 'visualInspection' }, width: 100 },
            { field: 'extendField5', title: '密度比', width: 100 },
            { field: 'extendField4', title: '其他检测项备注', width: 100 },
            {
              title: '颗粒度', align: 'center', children: [
                { field: 'granularityMm02', title: '标准一', width: 100 },
                { field: 'granularityMm26', title: '标准二', width: 100 },
                { field: 'granularityMm68', title: '标准三', width: 100 },
                { field: 'granularityMmAbove8', title: '标准四', width: 100 },
                { field: 'granularityMm5', title: '标准五', width: 100 },
                { field: 'granularityOther', title: '其他标准', width: 100 },
                {
                  field: 'granularityCompliance',
                  title: '是否符合',
                  slots: { default: 'granularityCompliance' },
                  width: 100
                },
                { field: 'extendField1', title: '其他颗粒度备注', width: 100 }
              ]
            }
          ]
        },
        {
          title: this.$t('理化项目'), align: 'center',
          children: [
            { field: 'ph', title: 'PH', width: 100 },
            { field: 'moisturePercentage', title: '水分%', width: 100 },
            { field: 'waterAbsorptionPercentage', title: '吸水性%', width: 100 },
            { field: 'settlingVolume', title: '沉降体积', width: 100 },
            { field: 'otherPhysical1', title: '其他理化1', width: 100 },
            { field: 'otherPhysical2', title: '其他理化2', width: 100 },
            { field: 'extendField2', title: '其他性能备注', width: 100 }
          ]
        },
        {
          title: this.$t('包装质量'), align: 'center',
          children: [
            { field: 'outerPackaging', title: '外包装', slots: { default: 'outerPackaging' }, width: 100 },
            {
              field: 'packagingSpecification',
              title: '包装规格',
              slots: { default: 'packagingSpecification' },
              width: 100
            },
            { field: 'extendField3', title: '其他包装备注', width: 100 }
          ]
        }
      ],
      operateColumn: [
        { title: this.$t('操作'), visible: true, slots: { default: 'operate' }, fixed: 'right', width: 35 }
      ]
    }
  },
  watch: {
    'fixColumn': {
      handler(newVal, oldVal) {
        if (!newVal) {
          return
        }
        switch (this.queryParams.reportDetailType) {
          case 'raw_material_report_details':
            this.girdOption.columns = [...newVal, ...this.rawColumn, ...this.operateColumn]
            break
          case 'packaging_material_report_detail':
            this.girdOption.columns = [...newVal, ...this.packageColumn, ...this.operateColumn]
            break
          default:
            this.girdOption.columns = [...newVal, ...this.generalColumn, ...this.operateColumn]
            break
        }
      }
    }
  },
  computed: {
    uploadAction() {
      return this.upload.url + '?type=' + this.upload.type
    }
  },
  created() {
    this.customScheme = this.getDictDatas(DICT_TYPE.LEDGER_REPORT_DETAIL_TYPE, 0)
    this.switchReportDetailType('raw_material_report_details')
    this.getList()
  },
  activated() {
    this.getList()
  },
  methods: {
    selectScheme(item) {
      this.queryParams.reportDetailType = item.value
      this.switchReportDetailType(item.value)
      this.getList()
    },
    switchReportDetailType(item) {
      switch (item) {
        case 'raw_material_report_details':
          this.girdOption.columns = [...this.fixColumn, ...this.rawColumn, ...this.operateColumn]
          break
        case 'packaging_material_report_detail':
          this.girdOption.columns = [...this.fixColumn, ...this.packageColumn, ...this.operateColumn]
          break
        default:
          this.girdOption.columns = [...this.fixColumn, ...this.generalColumn, ...this.operateColumn]
          break
      }
    },
    async getFactory() {
      const res = await listFactoryFromCache({ status: 0 })
      this.factoryList = res.data
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.getList()
    },
    /** 查询字典类型列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.queryParams.inspectionDateRange, 'InspectionDate', true)
      // 执行查询
      pageList(params).then(response => {
        const dataList = []
        if (this.queryParams.reportDetailType === 'raw_material_report_details') {
          response.data.list.forEach(v => {
            const id = v.id
            return dataList.push({ ...v, ...v.rawMaterialReportDetailsRespVO, id })
          })
        }
        if (this.queryParams.reportDetailType === 'packaging_material_report_detail') {
          response.data.list.forEach(v => {
            const id = v.id
            return dataList.push({ ...v, ...v.packagingMaterialReportDetailRespVO, id })
          })
        }
        if (this.queryParams.reportDetailType === 'general_report_details') {
          response.data.list.forEach(v => {
            const id = v.id
            return dataList.push({ ...v, ...v.generalReportDetailsRespVO, id })
          })
        }
        this.dataList = [...dataList]
        this.total = response.data.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        reportDetailType: this.queryParams.reportDetailType
      }
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 删除按钮操作 */
    invalidate(row) {
      const ids = row.id || this.ids
      this.$modal.confirm(this.$t('是否确定作废该数据？')).then(function() {
        return invalidate({ id: row.id })
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('作废成功'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.queryParams.inspectionDateRange, 'InspectionDate', true)
      // 执行导出
      this.$modal.confirm(this.$t('是否确认导出所有数据项?')).then(() => {
        this.exportLoading = true
        return exportExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('台账记录.xlsx'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    /** 导入按钮操作 */
    handleImport(type) {
      this.upload.title = this.$t('common.batchCreation') + '-' + this.customScheme.find(v => v.value === type).label
      this.upload.open = true
      this.upload.type = type
    },
    // 下载pdf
    printPdf() {
      const selected = this.$refs.ledgerIndex.getCheckboxRecords()
      if (selected.length === 0) {
        this.$message.warning(this.$t('请至少选择一条数据'))
        return
      }
      this.showPreview.open = true
      this.showPreview.reportId = 54
      let mapObject = new Map()
      mapObject.set('ids', selected.map(v => v.id).join(','))
      mapObject.set('userId', this.$store.getters.userId)
      mapObject.set('locale', Cookies.get('pro_language') || 'zh')
      // _t 的value 决定了预览顶部展示的小工具类型
      mapObject.set('_t', '2,4')
      this.showPreview.params = mapObject
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate({ type: this.upload.type }).then(response => {
        this.$download.excel(response, '台账导入模板.xlsx')
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.okList) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.okList.length
      }
      if (data.failureList) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureList).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    }
  }
}
</script>

<style lang="scss" scoped>
.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.searchValue {
  width: 80%;
}

.searchTimeItem {
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 82px);
  }
}
</style>
