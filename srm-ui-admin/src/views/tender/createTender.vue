<template>
  <div class="tender-create">
    <common-card
      :title="$t('supplier.essentialInformation')"
    >
      <el-form
        ref="projectInfo"
        :model="projectInfo"
        :rules="projectRule"
        inline
        label-width="135px"
      >
        <el-form-item
          :label="$t('rfq.eventName')"
          class="commonItem"
          prop="projectName"
        >
          <show-or-edit
            :disabled="disabled"
            :value="projectInfo.projectName"
          >
            <el-input
              v-model="projectInfo.projectName"
              maxlength="50"
              show-word-limit
            />

          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('supplier.purchasingOrganization')"
          class="commonItem"
          prop="purchaseOrg"
        >
          <show-or-edit
            :dict="DICT_TYPE.COMMON_PURCHASEORG"
            :disabled="disabled"
            :value="projectInfo.purchaseOrg"
          >
            <el-select
              v-model="projectInfo.purchaseOrg"
              class="content"
              clearable
              filterable
              @change="getFactory"
            >
              <el-option
                v-for="item in getDictDatas(DICT_TYPE.COMMON_PURCHASEORG)"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </show-or-edit>

        </el-form-item>
        <el-form-item
          :label="$t('tender.projectCategory')"
          class="commonItem"
          prop="projectClass"
        >
          <show-or-edit
            :dict="DICT_TYPE.TENDER_PROJECT_CLASS"
            :disabled="disabled"
            :value="projectInfo.projectClass"
          >
            <el-select
              v-model="projectInfo.projectClass"
              class="content"
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.TENDER_PROJECT_CLASS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </show-or-edit>

        </el-form-item>
        <el-form-item
          :label="$t('material.factory')"
          class="commonItem"
          prop="factoryId"
        >
          <show-or-edit
            :custom-list="factoryList"
            :disabled="disabled"
            :value="projectInfo.factoryId"
          >
            <el-select
              v-model="projectInfo.factoryId"
              class="content"
              clearable
              filterable
            >
              <el-option
                v-for="item in factoryList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>

          </show-or-edit>
        </el-form-item>

        <el-form-item
          :label="$t('sl.applicant')"
          class="commonItem"
        >
          <show-or-edit
            :disabled="disabled"
            :value="projectInfo.applicantName"
          >
            <el-input v-model="projectInfo.applicantName" />
            <!--            <el-select-->
            <!--              v-model="projectInfo.applicantId"-->
            <!--              class="content"-->
            <!--              clearable-->
            <!--              filterable-->
            <!--              @change="getDeptId"-->
            <!--            >-->
            <!--              <el-option-->
            <!--                v-for="item in getDictDatas(DICT_TYPE.COMMON_USERS)"-->
            <!--                :key="item.id"-->
            <!--                :label="item.name"-->
            <!--                :value="item.id"-->
            <!--              />-->
            <!--            </el-select>-->

          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('tender.estimatedAmount')"
          class="commonItem"
        >
          <show-or-edit
            :disabled="disabled"
            :value="projectInfo.amount"
          >
            <vxe-input
              v-model="projectInfo.amount"
              style="width: 60%;"
              type="float"
            />
          </show-or-edit>

          <show-or-edit
            :dict="DICT_TYPE.COMMON_CURRENCY"
            :disabled="disabled"
            :value="projectInfo.currencyId"
          >
            <el-select
              v-model="projectInfo.currencyId"
              :placeholder="$t('rfq.pleaseSelectTheTargetCurrency')"
              clearable
              style="width: 40%;"
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </show-or-edit>

        </el-form-item>

        <el-form-item
          :label="$t('tender.applicationDepartment')"
          class="commonItem"
        >
          <show-or-edit
            :disabled="disabled"
            :value="projectInfo.departmentName"
          >
            <el-input v-model="projectInfo.departmentName" />
          </show-or-edit>

          <!--          <dict-tag-->
          <!--            v-model="projectInfo.departmentId"-->
          <!--            :type="DICT_TYPE.COMMON_DEPT"-->
          <!--          />-->
        </el-form-item>
        <el-form-item
          :label="$t('tender.biddingOrganizer')"
          class="commonItem"
        >
          <el-select
            v-model="projectInfo.organizerId"
            class="content my-select"
            clearable
            disabled
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS,false)"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>

        </el-form-item>

        <!--        <el-form-item class="commonItem"
        :label="$t('tender.publicBidding')">-->
        <!--          <show-or-edit-->
        <!--            type="Boolean"-->
        <!--            :disabled="disabled"-->
        <!--            :value="projectInfo.openTender"-->
        <!--          >-->
        <!--            <el-switch v-model="projectInfo.openTender" />-->

        <!--          </show-or-edit>-->
        <!--        </el-form-item>-->
        <el-form-item
          :label="$t('rfq.projectRemarks')"
          class="commonItem"
        >
          <show-or-edit
            :disabled="disabled"
            :value="projectInfo.remark"
          >
            <el-input v-model="projectInfo.remark" :rows="2" autosize maxlength="1000" show-word-limit type="textarea" />

          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('common.creationDate')"
          class="commonItem"
        >
          <el-date-picker
            v-model="projectInfo.createTime"
            class="content"
            placement="bottom-start"
            readonly
            type="date"
            value-format="yyyy-MM-dd"
          />
          <!--          {{ dayjs(projectInfo.createTime).format('YYYY-MM-DD') }}-->
        </el-form-item>

        <!--        <el-form-item v-if="projectInfo.openTender" class="commonItem"
        :label="$t('tender.publicRegistrationDeadline')" prop="enrollClosingDate">-->
        <!--          <show-or-edit-->
        <!--            :type="'Date'"-->
        <!--            :disabled="disabled"-->
        <!--            :value="projectInfo.enrollClosingDate"-->
        <!--          >-->
        <!--            <el-date-picker-->
        <!--              v-model="projectInfo.enrollClosingDate"-->
        <!--              class="content"-->
        <!--              :placeholder="$t('order.selectDate')"-->
        <!--              type="date"-->
        <!--              placement="bottom-start"-->
        <!--              value-format="yyyy-MM-dd"-->
        <!--            />-->
        <!--          </show-or-edit>-->
        <!--        </el-form-item>-->
        <el-form-item
          :label="$t('tender.projectAttachments')"
          class="commonItem"
        >
          <tenderFile
            :business-type="'basic_information'"
            :disabled="disabled"
          />

        </el-form-item>
        <el-form-item
          :label="$t('tender.projectCode')"
          class="commonItem"
        >
          <el-input v-model="projectInfo.projectNo" readonly />

        </el-form-item>

      </el-form>

    </common-card>
    <common-card
      :title="$t('tender.supplierInformation')"
    >

      <el-button v-if="!disabled" class="required" type="primary" @click="addVisible = true">{{ $t('tender.addSupplier') }}</el-button>
      <!--      <el-button v-if="!disabled" type="text">{{$t('tender.viewRegisteredSuppliers')}}</el-button>-->

      <el-table :data="projectInfo.projectSupplierList">
        <el-table-column :label="$t('supplier.supplierName')" min-width="200" prop="supplierName">
          <template #header>
            <span class="required">{{ $t('supplier.supplierName') }}</span>
          </template>
          <!--          <template #default="scope">-->
          <!--            <el-input v-model="scope.row.supplierName" />-->
          <!--          </template>-->

        </el-table-column>
        <el-table-column :label="$t('supplier.supplierCode')" prop="supplierCode" width="90" />
        <el-table-column :label="$t('supplier.registeredCapital')" prop="registeredCapital" width="130">

          <template #default="scope">
            <show-or-edit
              :disabled="disabled"
              :value="scope.row.registeredCapital"
            >
              <vxe-input v-model="scope.row.registeredCapital" style="width: 100%" type="float" />
            </show-or-edit>
          </template>
        </el-table-column>
        <el-table-column :label="$t('system.currency')" prop="currencyId" width="115">
          <template #default="scope">
            <show-or-edit
              :dict="DICT_TYPE.COMMON_CURRENCY"
              :disabled="disabled"
              :value="scope.row.currencyId"
            >
              <el-select
                v-model="scope.row.currencyId"
                :placeholder="$t('rfq.pleaseSelectTheTargetCurrency')"
                clearable
                style="width: 85px"
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY,false)"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>
            </show-or-edit>
          </template>
        </el-table-column>
        <el-table-column :label="$t('supplier.establishedTime')" prop="foundedDate" width="140">
          <template #default="scope">
            <show-or-edit
              :disabled="disabled"
              :value="scope.row.foundedDate"
              type="Date"
            >
              <el-date-picker
                v-model="scope.row.foundedDate"
                :picker-options="pickerOptionsDayBefore"
                :placeholder="$t('order.selectDate')"
                class="content"
                placement="bottom-start"
                style="width: 130px;"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </show-or-edit>
          </template></el-table-column>
        <el-table-column :label="$t('tender.relatedQualificationsAndProjectCases')" min-width="200" prop="businessCases">
          <template #default="scope">
            <show-or-edit
              :disabled="disabled"
              :value="scope.row.businessCases"
            >
              <el-input v-model="scope.row.businessCases" :rows="1" autosize maxlength="1000" show-word-limit type="textarea" />
            </show-or-edit>
          </template></el-table-column>
        <el-table-column v-if="projectInfo.projectStatus !== 'toBeReleased'" :label="$t('tender.biddingStatus')" prop="bidStatus" width="80">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.TENDER_BID_STATUS" :value="scope.row.bidStatus" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('supplier.contacts')" prop="contactName" width="150">
          <template #default="scope">
            <show-or-edit
              :disabled="disabled"
              :value="scope.row.contactName"
            >
              <el-input v-model="scope.row.contactName" />
            </show-or-edit>
          </template></el-table-column>
        <el-table-column :label="$t('supplier.contactNumber')" prop="phone" width="150">
          <template #default="scope">
            <show-or-edit
              :disabled="disabled"
              :value="scope.row.phone"
            >
              <el-input v-model="scope.row.phone" />
            </show-or-edit>
          </template></el-table-column>
        <el-table-column :label="$t('tender.contactEmail')" prop="email" width="150">
          <template #header>
            <span class="required">{{ $t('tender.contactEmail') }}</span>
          </template>
          <template #default="scope">
            <show-or-edit
              :disabled="disabled"
              :value="scope.row.email"
            >
              <el-input v-model="scope.row.email" />
            </show-or-edit>
          </template></el-table-column>
        <el-table-column v-if="!disabled" :label="$t('common.operate')" width="60px">
          <template #default="scope">
            <el-button
              type="text"
              @click="projectInfo.projectSupplierList.splice(scope.$index,1)"
            >{{ $t('common.del') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </common-card>
    <common-card
      :title="$t('tender.biddingMethod')"
    >
      <el-form
        ref="tenderMethod"
        :model="projectInfo"
        :rules="projectRule"
        inline
        label-width="130px"
      >
        <el-form-item
          :label="$t('tender.bidOpeningMethod')"
          class="commonItem"
          prop="bidOpeningType"
        >
          <show-or-edit
            :dict="DICT_TYPE.TENDER_BID_OPENING_TYPE"
            :disabled="disabled"
            :value="projectInfo.bidOpeningType"
          >
            <el-select
              v-model="projectInfo.bidOpeningType"
              clearable
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.TENDER_BID_OPENING_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="Number(dict.value)"
              />
            </el-select>
          </show-or-edit>
        </el-form-item>
        <el-form-item
          :label="$t('tender.doYouNeedADeposit')"
          class="commonItem"
        >
          <show-or-edit
            :disabled="disabled"
            :value="projectInfo.bondRequire"
            type="Boolean"
          >
            <el-switch v-model="projectInfo.bondRequire" />
          </show-or-edit>
        </el-form-item>
        <el-form-item
          v-if="projectInfo.bondRequire"
          :label="$t('tender.guaranteeAmount')"
          class="commonItem"
          prop="bondAmount"
        >
          <show-or-edit
            :disabled="disabled"
            :value="projectInfo.bondAmount"
          >
            <vxe-input v-model="projectInfo.bondAmount" min="0" type="number" />
          </show-or-edit>
          <show-or-edit
            :dict="DICT_TYPE.COMMON_CURRENCY"
            :disabled="disabled"
            :value="projectInfo.bondCurrencyId"
          >
            <el-select
              v-model="projectInfo.bondCurrencyId"
              :placeholder="$t('rfq.pleaseSelectTheTargetCurrency')"
              clearable
              style="width: 85px"
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY,false)"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select></show-or-edit>
        </el-form-item>
      </el-form>
    </common-card>
    <common-card
      :title="$t('tender.invitation')"
    >
      <el-descriptions>
        <el-descriptions-item :label="$t('tender.invitationLetterUpload')">
          <tenderFile
            :business-type="'invitation_letter'"
            :disabled="disabled"
          />
        </el-descriptions-item>
      </el-descriptions>

    </common-card>
    <common-card
      :title="$t('tender.requirementDescription')"
    >
      <el-card
        v-for="(item,index) in projectInfo.projectDocumentList"
        style="margin-bottom: 15px"
      >
        <el-form
          :ref="`projectDocumentList${index}`"
          :model="item"
          :rules="{
            standardUser: [{
              required:true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }],

            documentType: [{
              required:true,
              message: $t('common.pleaseSelect'),
              trigger: 'change'
            }],
          }"
          inline
        >
          <el-form-item :label="$t('tender.bidType')" prop="documentType">
            <show-or-edit
              :dict="DICT_TYPE.TENDER_DOCUMENT_TYPE"
              :disabled="disabled"
              :value="item.documentType"
            >

              <el-select
                v-model="item.documentType"
                clearable
                @change="(val)=>changeDocumentType(val,item)"
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.TENDER_DOCUMENT_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
              <i
                class="el-icon-circle-plus"
                style="margin-left:10px;font-size: 18px;cursor: pointer"
                @click="projectInfo.projectDocumentList.push( {
                  projectDocumentContentList: [
                    {
                      content: '',
                      contentRequirements: '',
                      answerType: null
                    }
                  ],
                  tenderFileRelList: [
                    {
                      fileId: null,
                      businessType: '',
                      remark: '',
                      createTime: ''
                    }
                  ],
                  documentType: '',
                  standardUser: null,
                })"
              />
              <i
                class="el-icon-remove"
                style="margin-left:5px;font-size: 18px;cursor: pointer"
                @click="delProjectDocument(index)"
              />

            </show-or-edit>

          </el-form-item>
          <el-form-item
            :label="$t('tender.ratingCriteriaCreator')"
            prop="standardUser"
            style="float: right"
          >
            <show-or-edit
              :dict="DICT_TYPE.COMMON_USERS"
              :disabled="disabled"
              :value="item.standardUser"
            >
              <el-select
                v-model="item.standardUser"
                clearable
                filterable
              >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS)"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>

            </show-or-edit>
          </el-form-item>
        </el-form>
        {{ $t('tender.bidRequirementsDocument') }}
        <el-table
          :data="item.tenderFileRelList"
          style="margin: 15px 0"
        >
          <el-table-column :label="$t('tender.bidType')">
            <template #default>
              <dict-tag :type="DICT_TYPE.TENDER_DOCUMENT_TYPE" :value="item.documentType" />
            </template>
          </el-table-column>
          <el-table-column :label="$t('tender.bidAttachmentPreview')">
            <template #default="scope">
              <el-button
                type="text"
                @click="showFile(scope.row)"
              >{{ scope.row.fileName }}</el-button>
            </template>
          </el-table-column>
          <el-table-column :label="$t('auth.uploadAttachments')">
            <template #header>
              <span class="required">{{ $t('auth.uploadAttachments') }}</span>
            </template>
            <el-upload
              ref="upload"
              :action="url"
              :disabled="disabled"
              :headers="headers"
              :on-success="(response, file, fileList)=>handleFileSuccess(response, file, fileList,item)"
              :show-file-list="false"
            >
              <el-button
                :disabled="disabled"
                type="text"
              >{{ $t('common.upload') }}</el-button>
            </el-upload>
          </el-table-column>
          <el-table-column :label="$t('order.uploadDate')" prop="createTime">
            <template #default="scope">
              {{ scope.row.createTime? dayjs(scope.row.createTime).format('YYYY-MM-DD') :'' }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('tender.remarksDescription')">
            <template #default="scope">
              <show-or-edit
                :disabled="disabled"
                :value="scope.row.remark"
              >
                <el-input
                  v-model="scope.row.remark"
                  :rows="1"
                  autosize
                  maxlength="100"
                  show-word-limit
                  type="textarea"
                />
              </show-or-edit>

            </template>
          </el-table-column>
        </el-table>
        <div>
          {{ $t('tender.customizedBiddingDocuments') }}
          <el-button
            v-if="!disabled"
            style="margin-left: 10px"
            type="text"
            @click="addDocument(item)"
          >{{ $t('rfq.addTo') }}</el-button>
        </div>

        <el-table
          :data="item.projectDocumentContentList"
          style="margin: 15px 0"
        >

          <el-table-column :label="$t('rfq.serialNo')" type="index" />
          <el-table-column :label="$t('tender.bidType')">
            <template #default>
              <dict-tag :type="DICT_TYPE.TENDER_DOCUMENT_TYPE" :value="item.documentType" />
            </template>
          </el-table-column>
          <el-table-column :label="$t('tender.bidContent')">
            <template #header>
              <span class="required">{{ $t('tender.bidContent') }}</span>
            </template>
            <template #default="scope">
              <show-or-edit
                :disabled="disabled"
                :value="scope.row.content"
              >
                <el-autocomplete
                  v-model="scope.row.content"
                  :fetch-suggestions="querySearch"
                  :placeholder="$t('rfq.pleaseEnterTheContent')"
                  value-key="content"
                  @select="(val)=>selectContent(val,scope.row)"
                />

              </show-or-edit>

            </template>

          </el-table-column>
          <el-table-column :label="$t('tender.requirement')">
            <template #header>
              <span class="required">{{ $t('tender.requirement') }}</span>
            </template>
            <template #default="scope">
              <show-or-edit
                :disabled="disabled"
                :value="scope.row.contentRequirements"
              >
                <el-input v-model="scope.row.contentRequirements" autosize maxlength="500" show-word-limit type="textarea" />
              </show-or-edit>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tender.answerAttribute')">
            <template #header>
              <span class="required">{{ $t('tender.answerAttribute') }}</span>
            </template>
            <template #default="scope">
              <show-or-edit
                :dict="DICT_TYPE.TENDER_ANSWER_TYPE"
                :disabled="disabled"
                :value="scope.row.answerType"
              >
                <el-select
                  v-model="scope.row.answerType"
                >
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.TENDER_ANSWER_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="Number(dict.value)"
                  />
                </el-select>
              </show-or-edit>
            </template>

          </el-table-column>
          <el-table-column v-if="!disabled" :label="$t('tender.functionalZone')">
            <template #default="scope">
              <el-button type="text" @click="addDocument(item)">{{ $t('rfq.addTo') }}</el-button>
              <el-button
                type="text"
                @click="item.projectDocumentContentList.splice(scope.$index,1)"
              >{{ $t('common.del') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

    </common-card>
    <common-card
      :title="$t('tender.biddingRules')"
    >
      <el-form ref="tenderRule" :model="projectInfo" :rules="projectRule" inline label-width="130px">
        <!--        <el-form-item v-if="!disabled" class="commonItem"
        :label="$t('tender.doYouWantToAnswerAnyQuestions')">-->
        <!--          <show-or-edit-->
        <!--            type="Boolean"-->
        <!--            :disabled="disabled"-->
        <!--            :value="projectInfo.openAnswer"-->
        <!--          >-->
        <!--            <el-switch v-model="projectInfo.openAnswer" />-->

        <!--          </show-or-edit>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item v-if="!disabled" class="commonItem"
        :label="$t('tender.deadlineForQa')">-->
        <!--          <show-or-edit-->
        <!--            type="Date"-->
        <!--            :disabled="disabled"-->
        <!--            :value="projectInfo.answerClosingDate"-->
        <!--          >-->
        <!--            <el-date-picker-->
        <!--              v-model="projectInfo.answerClosingDate"-->
        <!--              class="content"-->
        <!--              :placeholder="$t('order.selectDate')"-->
        <!--              type="date"-->
        <!--              placement="bottom-start"-->
        <!--              :picker-options="pickerOptionsDayAfter"-->
        <!--              value-format="yyyy-MM-dd"-->
        <!--            />-->

        <!--          </show-or-edit>-->
        <!--        </el-form-item>-->
        <el-form-item
          :label="$t('tender.bidDeadline')"
          class="commonItem"
          prop="bidClosingDate"
        >
          <show-or-edit
            :disabled="disabled"
            :value="projectInfo.bidClosingDate"
            type="DateTime"
          >
            <el-date-picker
              v-model="projectInfo.bidClosingDate	"
              :picker-options="pickerOptionsDayAfter"
              :placeholder="$t('order.selectDate')"
              class="content"
              placement="bottom-start"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
            />

          </show-or-edit>

        </el-form-item>
      </el-form>

    </common-card>
    <div v-if="!disabled" class="fixedBottom">
      <!--      <el-button type="primary">{{$t('tender.announcement')}}</el-button>-->

      <el-button v-has-permi="['tender:project:submit']" type="primary" @click="submitProject">{{ $t('common.submit') }}</el-button>
      <el-button v-has-permi="['tender:project:save']" @click="saveProject">{{ $t('common.save') }}</el-button>
    </div>
    <el-dialog
      v-if="addVisible"
      :title="$t('tender.addSupplier')"
      :visible.sync="addVisible"
      width="1200px"
      @close="closeAdd"
    >
      <div style="font-weight: bold;margin-bottom: 10px">
        {{ $t('tender.addExistingSupplier') }}

      </div>
      <span>
        {{ $t('supplier.supplierName') }}
      </span>
      <el-select
        v-model="addInfo.supplierName"
        :placeholder="$t('common.pleaseSelect')"
        :remote-method="getSupplierDetail"
        class="content"
        clearable
        filterable
        remote
        style="margin: 10px 0"
        @change="selectSupplier"
      >
        <el-option
          v-for="dict in supplierList"
          :key="dict.id"
          :disabled="addInfo.list.some(a=>a.supplierId === dict.id) ||projectInfo.projectSupplierList.some(a=>a.supplierId === dict.id)"
          :label="dict.name"
          :value="dict.name"
        />
      </el-select>
      <div
        style="margin: 10px 0;font-weight: bold"
      >
        <!--        选择报名供应商：-->
      </div>
      <el-table :data="addInfo.list" style="margin-top: 15px">
        <el-table-column :label="$t('supplier.supplierName')" prop="supplierName" />
        <!--        <el-table-column :label="$t('tender.registrationTime')" prop="" />-->
        <el-table-column :label="$t('supplier.contacts')" prop="contactName" />
        <el-table-column :label="$t('supplier.contactNumber')" prop="phone" />
        <el-table-column :label="$t('supplier.mailingAddress')" prop="email" />
      </el-table>
      <div>
        <div
          style="margin: 10px 0;font-weight: bold"
        >
          {{ $t('tender.addExistingSupplier') }}
        </div>
        <el-row
          v-for="(item,index) in addInfo.customList"
          :gutter="20"
          justify="space-between"
          style="margin: 5px 0"
          type="flex"
        >
          <el-col :span="6">
            <span class="required">
              {{ $t('supplier.supplierName') }}
            </span>
            <el-input v-model="item.supplierName" class="addItem" style="width: 140px" />
          </el-col>
          <el-col :span="6">
            {{ $t('supplier.contacts') }}
            <el-input v-model="item.contactName" class="addItem" />

          </el-col>
          <el-col :span="6">
            {{ $t('supplier.contactNumber') }}
            <el-input v-model="item.phone" class="addItem" />

          </el-col>
          <el-col :span="6">
            <span class="required">
              {{ $t('supplier.mailingAddress') }}
            </span>
            <el-input v-model="item.email" class="addItem" />

          </el-col>
          <el-col :span="2">
            <el-button type="text" @click="addCustom">{{ $t('rfq.addTo') }}</el-button>
            <el-button
              v-if="addInfo.customList.length>1"
              type="text"
              @click="addInfo.customList.splice(index,1)"
            >{{ $t('common.del') }}</el-button>
          </el-col>
        </el-row>
      </div>
      <div slot="footer">
        <el-button @click="closeAdd">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitAdd">{{ $t('common.save') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="$t('tender.supplierRegistrationViewing')"
    >
      <el-table>
        <el-table-column :label="$t('tender.registrationTime')">123</el-table-column>
        <el-table-column :label="$t('supplier.supplierName')">123</el-table-column>
        <el-table-column :label="$t('supplier.contacts')">123</el-table-column>
        <el-table-column :label="$t('supplier.contactNumber')">123</el-table-column>
        <el-table-column :label="$t('supplier.mailingAddress')">123</el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary">{{ $t('order.close') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSupplierDetail } from '@/api/scar'
import { getSupplierContact } from '@/api/supplier/info'
import { deepClone } from '@/utils'
import { listPurchaseOrgFactoryFromCache } from '@/api/system/factory'
import { getTenderDocList, getTenderProject, saveTenderProject, submitTenderProject } from '@/api/tender/project'
import { getAccessToken } from '@/utils/auth'
import dayjs from 'dayjs'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import {getConfig} from "@/utils/config";

export default {
  name: 'Createtender',
  components: {
    ShowOrEdit,
    tenderFile: () => import('./componet/upload.vue')
  },
  provide() {
    return {
      tenderInfo: () => this.projectInfo
    }
  },
  props: ['projectId', 'disabled'],
  data() {
    const blankSupplier = {
      supplierId: null,
      supplierName: '',
      supplierCode: '',
      contactName: '',
      phone: '',
      email: '',
      currencyId: null,
      registeredCapital: null,
      foundedDate: '',
      businessCases: '',
      enrollDate: '',
      supplierFrom: true

    }
    const required = (rule, value, callback) => {
      if (!value && value !== 0) {
        callback(new Error(this.$t('common.pleaseEnter')))
      } else {
        callback()
      }
    }
    return {
      dayjs,
      pickerOptionsDayAfter: {
        disabledDate(time) {
          return dayjs().isAfter(dayjs(time), 'day')
        }
      },
      pickerOptionsDayBefore: {
        disabledDate(time) {
          return dayjs().isBefore(dayjs(time).add(1, 'day'), 'day')
        }
      },
      projectRule: {
        purchaseOrg: [{
          validator: required,
          trigger: 'blur', required: true
        }],
        bidClosingDate: [{
          validator: required,
          trigger: 'blur', required: true
        }],
        factoryId: [{
          validator: required,
          trigger: 'blur', required: true
        }],
        projectClass: [{
          validator: required,
          trigger: 'blur', required: true
        }],
        projectName: [{
          validator: required,
          trigger: 'blur', required: true
        }],
        bondAmount: [{
          validator: required,
          trigger: 'blur', required: true
        }],
        bidOpeningType: [{
          validator: required,
          trigger: 'change', required: true
        }]

      },
      url: getConfig('VUE_APP_BASE_API', getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API)) + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      data: {},
      blankSupplier,
      addVisible: false,
      projectInfo: {
        projectStatus: 'toBeReleased',
        bondCurrencyId: null,
        applicantName: '',
        departmentName: '',
        projectSupplierList: [

        ],
        projectDocumentList: [
          {
            projectDocumentContentList: [
              {
                content: '',
                contentRequirements: '',
                answerType: 1
              }
            ],
            documentType: '',
            standardUser: null,
            tenderFileRelList: [
              {
                fileId: null,
                businessType: '',
                remark: '',
                createTime: ''

              }
            ]
          }
        ],
        tenderFileRelList: [

        ],
        isSubmit: true,
        projectNo: '',
        projectName: '',
        purchaseOrg: null,
        factoryId: null,
        organizerId: null,
        applicantId: null,
        departmentId: null,
        currencyId: null,
        amount: null,
        remark: '',
        openTender: false,
        openAnswer: false,
        enrollClosingDate: '',
        answerClosingDate: '',
        bidClosingDate: '',
        bidOpeningType: null,
        bondAmount: null,
        bondRequire: false,
        projectClass: '',
        supplierQty: null
      },
      supplierList: [],
      addInfo: {
        supplierName: '',
        list: [],
        customList: [{
          supplierId: null,
          supplierName: '',
          supplierCode: '',
          contactName: '',
          phone: '',
          email: '',
          currencyId: null,
          registeredCapital: null,
          foundedDate: '',
          businessCases: '',
          enrollDate: '',
          supplierFrom: false
        }]
      },
      factoryList: [],
      tenderList: []
    }
  },
  mounted() {
    this.getTenderList()
    if (this.projectId) {
      this.init()
    }
  },
  methods: {
    init() {
      getTenderProject({
        id: this.projectId
      }).then(res => {
        res.data.createTime = new Date(res.data.createTime)
        res.data.projectDocumentList.forEach(a => {
          if (!a.tenderFileRelList.length) {
            a.tenderFileRelList.push({
              fileId: null,
              businessType: a.documentType,
              remark: '',
              createTime: ''

            })
          }
        })
        this.projectInfo = res.data
        if (this.projectInfo.purchaseOrg) {
          this.getFactory(this.projectInfo.purchaseOrg)
        }
        this.$nextTick(() => {
          for (let i = 0; i < this.projectInfo.projectDocumentList.length; i++) {
            this.$refs[`projectDocumentList${i}`][0].clearValidate()
          }
        })
      })
    },
    querySearch(queryString, cb) {
      const results = queryString ? this.tenderList.filter(a => a.content.indexOf(queryString) !== -1) : this.tenderList
      cb(results)
    },
    getTenderList() {
      getTenderDocList().then(res => {
        this.tenderList = res.data
      })
    },
    saveProject() {
      saveTenderProject(this.projectInfo).then(res => {
        this.projectInfo.id = res.data
        this.projectId = res.data
        this.init()
        this.$message.success(this.$t('common.savedSuccessfully'))
      })
    },
    submitProject() {
      let pass = true
      this.$refs.projectInfo.validate(valid => {
        if (!valid) {
          pass = false
        }
      })
      this.$refs.tenderMethod.validate(valid => {
        if (!valid) {
          pass = false
        }
      })
      this.$refs.tenderRule.validate(valid => {
        if (!valid) {
          pass = false
        }
      })
      for (let i = 0; i < this.projectInfo.projectDocumentList.length; i++) {
        this.$refs[`projectDocumentList${i}`][0].validate(valid => {
          if (!valid) {
            pass = false
          }
        })
      }
      if (!pass) {
        this.$message.error(this.$t('auth.pleaseCompleteTheInformation'))
        return
      }
      submitTenderProject(this.projectInfo).then(res => {
        this.$message.success(this.$t('supplier.submittedSuccessfully'))
        this.$tab.closeOpenPage('/tender/tenderIndex')
      })
    },
    getFactory(orgIds) {
      listPurchaseOrgFactoryFromCache({
        orgIds,
        status: 0
      }).then(res => {
        this.factoryList = res.data || []
      })
    },
    getSupplierDetail(query) {
      if (query) {
        getSupplierDetail({
          fuzzySupplierName: query
        }).then(res => {
          this.supplierList = res.data
        })
      } else {
        this.supplierList = []
      }
    },
    selectSupplier(item) {
      if (item) {
        const temp = this.supplierList.find(a => a.name === item)
        getSupplierContact({ supplierId: temp.id, contactDivision: 'the_main_contact' }).then(res => {
          this.addInfo.list.push({
            ...this.blankSupplier,
            ...res.data,
            supplierName: item,
            currencyId: temp.registeredCapitalCurrency,
            registeredCapital: temp.registeredCapital,
            contactName: res.data.name,
            supplierCode: temp.code,
            foundedDate: temp.establishedTime ? dayjs(temp.establishedTime).format('YYYY-MM-DD') : ''
          })
          this.addInfo.supplierName = ''
        })
      }
    },
    addCustom() {
      this.addInfo.customList.push({ ...this.blankSupplier, supplierFrom: false })
    },
    closeAdd() {
      this.addInfo = {
        supplierName: '',
        list: [],
        customList: [deepClone(this.blankSupplier)]
      }
      this.supplierList = []
      this.addVisible = false
    },
    submitAdd() {
      this.projectInfo.projectSupplierList.push(...this.addInfo.list, ...this.addInfo.customList.filter(a => a.supplierName))
      this.closeAdd()
    },
    getDeptId(id) {
      this.projectInfo.departmentId = getDictDatas(DICT_TYPE.COMMON_USERS).find(a => a.id === id)?.deptId
    },
    addDocument(item) {
      if (item.projectDocumentContentList === null) {
        item.projectDocumentContentList = []
      }
      item.projectDocumentContentList.push({
        documentId: item.id || null,
        content: null,
        contentRequirements: '',
        answerType: null
      })
    },
    handleFileSuccess(response, file, fileList, item, businessType) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
        return
      }
      this.$set(item.tenderFileRelList[0], 'fileId', response.data.id)
      this.$set(item.tenderFileRelList[0], 'fileName', file.name)
      this.$set(item.tenderFileRelList[0], 'filePath', response.data.url)
      this.$set(item.tenderFileRelList[0], 'createTime', response.data.createTime)
    },
    selectContent(val, item) {
      item.contentRequirements = val.contentRequirements
      item.answerType = val.answerType
    },
    changeDocumentType(val, item) {
      item.tenderFileRelList[0].businessType = 'require'
    },
    showFile(file) {
      window.open(file.filePath)
    },
    delProjectDocument(index) {
      this.$modal.confirm(this.$t('tender.pleaseConfirmToDeleteTheCurrentBidInformation')).then(() => {
        this.projectInfo.projectDocumentList.length === 1
          ? this.projectInfo.projectDocumentList = [
            {
              projectDocumentContentList: [
                {
                  content: '',
                  contentRequirements: '',
                  answerType: null
                }
              ],
              documentType: '',
              standardUser: null,
              tenderFileRelList: [
                {
                  fileId: null,
                  businessType: '',
                  remark: '',
                  createTime: ''

                }
              ]
            }

          ]
          : this.projectInfo.projectDocumentList.splice(index, 1)
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>

.required::before{
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}
.commonItem {
  width: 50%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

  ::v-deep .el-form-item__content {
    width: calc(100% - 140px);
  }
}
::v-deep .my-select.el-select .el-input.is-disabled .el-input__inner {
  background-color: #fff;
  color: #606266;
  cursor: not-allowed;
}
.addItem {
  width: 150px;
  margin-left: 10px;
}
</style>
