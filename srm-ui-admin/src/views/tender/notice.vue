<template>
  <div class="notice">
    <common-card
      :title="$t('supplier.essentialInformation')"
    >
      <basic-info :project-info="projectInfo" />
    </common-card>
    <common-card
      :title="$t('tender.notice')"
    >
      <el-table
        ref="tenderSupplier"
        :data="projectInfo.tenderSupplierList"
      >
        <el-table-column
          type="selection"
          width="30"
        />
        <el-table-column :label="$t('supplier.supplierName')" prop="supplierName" />
        <el-table-column :label="$t('supplier.contacts')" prop="contactName">
          <template #default="scope">
            <el-input v-model="scope.row.contactName" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('supplier.contactNumber')" prop="phone">
          <template #default="scope">
            <el-input v-model="scope.row.phone" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('supplier.mailingAddress')" prop="email">
          <template #default="scope">
            <el-input v-model="scope.row.email" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('tender.totalScore')" prop="score" />
        <el-table-column :label="$t('tender.bidWinningSituation')" prop="bidWin">
          <template #default="scope">
            <i v-if="scope.row.bidWin" class="el-icon-check" style="font-size: 18px" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('tender.noticeAttachment')">
          <template #default="scope">
            <el-button type="text" @click="showFile(scope.row.fileRelBaseVO)">
              {{ scope.row.fileRelBaseVO?.fileName }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="$t('tender.notificationSendingStatus')" prop="bidStatus">
          <template #default="scope">
            {{ scope.row.bidNotice ?$t('tender.hasBeenSent'):$t('tender.notSent') }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('tender.functionalZone')">
          <template #default="scope">
            <el-button
              :disabled="scope.row.bidNotice"
              type="text"
              @click="showPdf(scope.row)"
            >{{ $t('tender.generateNotification') }}</el-button>
            <el-upload
              ref="upload"
              :disabled="scope.row.bidNotice"
              style="display: inline-block;margin-left: 3px"
              :headers="headers"
              :show-file-list="false"
              :action="url"
              :on-success="(response, file, fileList)=>handleFileSuccess(response, file, fileList,scope.row)"
            >
              <el-button
                :disabled="scope.row.bidNotice"

                type="text"
              >{{ $t('common.upload') }}</el-button>
            </el-upload>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 15px;text-align: right">
        <el-button type="primary" @click="sendNotice">{{ $t('tender.sendNotifications') }}</el-button>
      </div>
    </common-card>
    <el-dialog
      v-if="generateVisible"
      width="1200px"
      :title="$t('tender.generateNotification')"
      :visible.sync="generateVisible"
    >
      <iframe style="width: 100%" height="600" :src="pdfUrl" />
      <div slot="footer">
        <el-button type="primary" @click="generateFile">{{ $t('tender.generateNotificationAttachment') }}</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>

import { getAccessToken } from '@/utils/auth'
import BasicInfo from '@/views/tender/componet/BasicInfo.vue'
import { createTenderFileRel, generateNoticeFile, sendTenderEmailToSupplier, tenderDetail } from '@/api/tender/project'
import { getPreviewReport } from '@/api/visualization/report'
import {getConfig} from "@/utils/config";

export default {
  name: 'Notice',
  components: { BasicInfo },
  data() {
    return {
      url: getConfig('VUE_APP_BASE_API', getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API)) + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      data: {},
      fileList: [],
      projectInfo: {},
      generateVisible: false,
      pdfUrl: '',
      tempItem: {}
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      tenderDetail({
        projectId: Number(this.$route.query.projectId)
      }).then(res => {
        this.projectInfo = res.data
      })
    },
    handleFileSuccess(response, file, fileList, item) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
        return
      }
      item.fileRelBaseVO = {}
      item.fileRelBaseVO.fileName = file.name
      item.fileRelBaseVO.filePath = response.data.url
      createTenderFileRel([
        {
          businessId: item.id,
          businessType: 'tender_notice_attachment',
          fileId: response.data.id,
          projectId: Number(this.$route.query.projectId)
        }
      ])
    },
    showFile(file) {
      if (file.filePath) {
        window.open(file.filePath)
      }
    },
    showPdf(row) {
      getPreviewReport({
        reportId: row.bidWin ? this.projectInfo.reportId : this.projectInfo.notWinReportId,
        id: row.id,
        locale: 'zh'
      }).then(res => {
        this.pdfUrl = res.data
        this.tempItem = row
        this.generateVisible = true
      })
    },
    generateFile() {
      generateNoticeFile({
        tenderProjectSupplierId: this.tempItem.id
      }).then(res => {
        this.generateVisible = false
        this.init()
      })
    },
    sendNotice() {
      const data = this.$refs.tenderSupplier.selection

      if (data.length) {
        sendTenderEmailToSupplier({
          noticeReqVOS: data.map(a => {
            return {
              businessId: a.id,
              contactName: a.contactName,
              email: a.email,
              phone: a.phone
            }
          }),
          projectId: Number(this.$route.query.projectId)
        }).then(res => {
          this.init()
          this.$message.success(this.$t('common.successfullySent'))
        })
      } else {
        this.$message.error(this.$t('supplier.pleaseSelectData'))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-upload--text{
  text-align: left;
}
.notice{
  padding: 15px 25px;
}
</style>
