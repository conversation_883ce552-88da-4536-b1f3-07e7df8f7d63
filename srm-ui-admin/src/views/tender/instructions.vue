<template>
  <div class="notice">
    <common-card
      :title="$t('tender.invitationToBid')"
    >
      <div class="notice-body">
        <p> {{ $t('tender.dear') }}{{ noticeInfo.supplierName }}</p>
        <p>
          {{ $t('tender.sincerelyInviteYourCompanyTitle') }}
        </p>
        <el-descriptions
          :label-style="{
            width:'200px',
            color: 'black'

          }"
          :column="1"
          :colon="false"
        >

          <el-descriptions-item :label="$t('rfq.eventName')">{{ noticeInfo.projectName }}</el-descriptions-item>
          <el-descriptions-item :label="$t('rfq.rfqItemNo')">{{ noticeInfo.projectNo }}</el-descriptions-item>
          <el-descriptions-item :label="$t('tender.deadlineForQa')">{{ noticeInfo.answerClosingDate }}</el-descriptions-item>
          <el-descriptions-item :label="$t('tender.bidDeadline')">{{ noticeInfo.bidClosingDate }}</el-descriptions-item>
          <el-descriptions-item :label="$t('tender.biddingOrganizer')">
            <dict-tag :value="noticeInfo.organizerId" :type="DICT_TYPE.COMMON_USERS" />
          </el-descriptions-item>
          <el-descriptions-item :label="$t('tender.depositAmount')">{{ noticeInfo.bondAmount }}

            <dict-tag :value="noticeInfo.bondCurrencyId" :type="DICT_TYPE.COMMON_CURRENCY" />
          </el-descriptions-item>
        </el-descriptions>
      </div>

    </common-card>
    <common-card
      :title="$t('tender.noticeToBidders')"
    >
      <div class="notice-body">
        <p v-html="noticeInfo.instruction" />
        <div style="margin-left:40%">
          <div>
            <el-checkbox v-model="noticeInfo.bidRead">{{ $t('tender.iHaveReadAndAmAwareOfTheAboveRequirements') }}</el-checkbox>
          </div>
          <div v-if="noticeInfo.bondRequire" style="margin-top: 14px;display: flex">
            <div>
              <el-checkbox v-model="hasUpload" disabled>{{ $t('tender.theDepositHasBeenPaidAndTheVoucherHasBeenUploaded') }}</el-checkbox>
            </div>
            <el-upload
              ref="upload"
              v-has-permi="['tender:project:save-instruction-file']"
              :headers="headers"
              class="upload-container"
              :action="url"
              :on-success="handleFileSuccess"
              :file-list="noticeInfo.tenderFileRelList"
              :on-remove="handleRemove"
            >
              <el-button type="primary">{{ $t('auth.uploadAttachments') }}</el-button>

            </el-upload>
          </div>
        </div>

      </div>

    </common-card>
    <div class="fixedBottom">
      <!--      <el-button style="width: 110px" @click="closeWindow">{{ $t('common.cancel') }}</el-button>-->
      <el-button
        v-if="noticeInfo.bidRead&&(!noticeInfo.bondRequire||hasUpload)"
        style="width: 50%"
        type="primary"
        @click="readFile"
      >{{ $t('tender.openRequirementFile') }}</el-button>
    </div>
  </div>

</template>

<script>
import { delTenderFileRel, getInstruction, openRequireFile, saveInstructionFile } from '@/api/tender/project'
import { getAccessToken } from '@/utils/auth'
import {getConfig} from "@/utils/config";

export default {
  name: 'Instructions',
  props: ['supplierId', 'projectId'],

  data() {
    return {
      noticeInfo: {
        amount: 0,
        answerClosingDate: '',
        applicantId: 0,
        bidClosingDate: '',
        bidOpeningType: 0,
        bidRead: false,
        bondAmount: 0,
        bondRequire: true,
        currencyId: 0,
        departmentId: 0,
        enrollClosingDate: '',
        factoryId: 0,
        instruction: '',
        openAnswer: true,
        openTender: true,
        organizerId: 0,
        projectClass: '',
        projectName: '',
        projectNo: '',
        projectStatus: '',
        purchaseOrg: 0,
        remark: '',
        remark_win: '',
        supplierName: '',
        supplierQty: 0,
        tenderFileRelList: [

        ]
      },
      url: getConfig('VUE_APP_BASE_API', getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API)) + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      data: {},
      fileList: [],
      hasRead: false
    }
  },
  computed: {
    hasUpload() {
      return this.noticeInfo.tenderFileRelList.length !== 0
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      getInstruction({
        supplierId: this.supplierId,
        projectId: this.projectId
      }).then(res => {
        res.data.tenderFileRelList.forEach(a => {
          a.name = a.fileName
          a.url = a.filePath
        })
        if (this.noticeInfo.bidRead) {
          res.data.bidRead = this.noticeInfo.bidRead
        }
        this.noticeInfo = res.data
        this.$emit('getReadStatus', this.noticeInfo.bidRead)
      })
    },

    handleFileSuccess(response, file, fileList) {
      saveInstructionFile({
        supplierId: this.supplierId,
        projectId: this.projectId,

        tenderFileRelList: [{
          supplierId: this.supplierId,
          projectId: this.projectId,
          businessType: 'security_fund',
          fileId: response.data.id
        }]
      }).then(a => {
        this.init()
      })
    },
    handleRemove(file, fileList) {
      delTenderFileRel({ id: file.id || file.response.data.id }).then(res => {
        this.$message.success(this.$t('common.delSuccess'))
        this.init()
      })
    },
    closeWindow() {
      window.close()
    },
    readFile() {
      openRequireFile({
        supplierId: this.supplierId,
        projectId: this.projectId,
        bidRead: true
      }).then(a => {
        this.$emit('getReadStatus', true)
        this.$emit('showRequire')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-upload--text{
  text-align: left;
}
.fixedBottom{
  z-index: 99;
  text-align: center;
  position: fixed;
  width: 100%;
  bottom: 20px
}
.notice{
  &-body{
    padding: 0 70px;
  }
}
</style>
