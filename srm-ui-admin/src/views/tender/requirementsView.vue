<template>
  <div class="requirement">
    <common-card
      :title="$t('tender.requirementDescription')"
    >
      <el-card
        v-for="(item,index) in projectDocumentList"
        style="margin-bottom: 15px"
      >
        <div style="font-weight: bold;margin-bottom: 5px">
          {{ $t('tender.reply') }}<dict-tag :value="item.documentType" :type="DICT_TYPE.TENDER_DOCUMENT_TYPE" />
        </div>
        <div>
          {{ $t('tender.uploadBidRequirementsDocument') }}
        </div>
        <el-table
          :data="[{}]"
          style="margin: 15px 0"
        >
          <el-table-column :label="$t('tender.bidType')">
            <template #default>
              <dict-tag :value="item.documentType" :type="DICT_TYPE.TENDER_DOCUMENT_TYPE" />
            </template>
          </el-table-column>
          <el-table-column :label="$t('tender.bidAttachmentPreview')">
            <template #default="scope">
              <el-button
                type="text"
                @click="showFile(item.filePath)"
              >{{ item.fileName }}</el-button>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tender.replyContent')">
            <template #header>
              <span class="required">{{ $t('tender.replyContent') }}</span>
            </template>
            <el-button
              type="text"
              @click="showFile(item.supplierFilePath)"
            >{{ item.supplierFileName }}</el-button>
          </el-table-column>
          <el-table-column :label="$t('tender.functionalZone')" prop="createTime">
            <el-upload
              ref="upload"
              :disabled="!editMode"
              :headers="headers"
              :show-file-list="false"
              :action="url"
              :on-success="(response, file, fileList)=>handleFileSuccess(response, file, fileList,item)"
            >
              <el-button
                :disabled="!editMode"
                type="text"
              >{{ $t('common.upload') }}</el-button>
            </el-upload>
          </el-table-column>

        </el-table>
        {{ $t('tender.replyToCustomBiddingDocuments') }}
        <el-table
          style="margin: 15px 0"
          :data="item.projectDocumentSupplierContentList"
        >

          <el-table-column :label="$t('rfq.serialNo')" type="index" />
          <el-table-column :label="$t('tender.bidContent')">
            <template #default="scope">

              {{ scope.row.content }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('tender.issuesAndRequirements')">
            <template #default="scope">
              {{ scope.row.contentRequirements }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('tender.answerAttribute')">
            <template #default="scope">
              <dict-tag :value="scope.row.answerType" :type="DICT_TYPE.TENDER_ANSWER_TYPE" />
            </template>

          </el-table-column>
          <el-table-column :label="$t('tender.replyContent')">
            <template #header>
              <span class="required">{{ $t('tender.replyContent') }}</span>
            </template>
            <template #default="scope">

              <el-button
                v-if="scope.row.answerType ===1"
                :disabled="!editMode"
                type="text"
                @click="showFile(scope.row.supplierContentFilePath)"
              >{{ scope.row.supplierContentFileName }}</el-button>
              <template
                v-else
              >
                <el-input
                  v-model="scope.row.supplierAnswer "
                  :disabled="!editMode"
                />

              </template>
            </template>
          </el-table-column>
          <el-table-column :label="$t('tender.functionalZone')">
            <template #default="scope">
              <el-upload
                v-if="scope.row.answerType === 1"
                ref="upload"
                :disabled="!editMode"
                :headers="headers"
                :show-file-list="false"
                :action="url"
                :on-success="(response, file, fileList)=>handleFileContentSuccess(response, file, fileList,scope.row,item.documentType)"
              >
                <el-button
                  :disabled="!editMode"
                  type="text"
                >{{ $t('common.upload') }}</el-button>
              </el-upload>
            </template>

          </el-table-column>
        </el-table>
      </el-card>

    </common-card>
    <div class="fixedBottom">
      <el-button
        v-if="editMode"
        v-has-permi="['tender:project:submit-supplier-bid']"
        :disabled="!notEndFlag"
        style="width: 300px"
        type="primary"
        @click="submitSupplierBid"
      >{{ $t('tender.bidSubmission') }}</el-button>
      <el-button
        v-if="editMode"
        v-has-permi="['tender:project:submit-supplier-bid']"
        type="primary"
        style="width: 100px"
        plain
        @click="saveSupplierBid"
      >{{ $t('common.save') }}</el-button>
      <el-button
        v-if="bidStatus === 2 && !editMode"
        type="primary"
        style="width: 100px"
        @click="editMode =!editMode"
      >{{ $t('common.modify') }}</el-button>

    </div>
  </div>
</template>

<script>
import { getSupplierBid, saveSupplierBid, submitSupplierBid } from '@/api/tender/project'
import { getAccessToken } from '@/utils/auth'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import {getConfig} from "@/utils/config";

export default {
  name: 'Requirementsview',
  props: ['supplierId', 'projectId'],
  data() {
    dayjs.extend(duration)
    return {
      dayjs,
      bidClosingDate: '',
      projectDocumentList: [],
      url: getConfig('VUE_APP_BASE_API', getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API)) + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      timer: null,
      countdown: null,
      editMode: true,
      notEndFlag: true,
      bidStatus: null
    }
  },
  mounted() {
    this.init()
    this.timer = setInterval(() => {
      this.notEndFlag = dayjs(this.bidClosingDate).isAfter(dayjs())
      const durationObj = dayjs.duration(dayjs().diff(dayjs(this.bidClosingDate)))
      this.countdown = `${Math.abs(durationObj.days())}${this.$t('order.day')}
      ${Math.abs(durationObj.hours())}${this.$t('tender.hour')}
      ${Math.abs(durationObj.minutes())}${this.$t('tender.minute')}
      ${Math.abs(durationObj.seconds())}${this.$t('tender.second')}`
    }, 1000)
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    init() {
      getSupplierBid({
        projectId: this.projectId,
        supplierId: this.supplierId
      }).then(res => {
        res.data.projectDocumentSupplierList.forEach(a => {
          a.documentId = a.id
          a.id = a.documentSupplierId || null
          a.tenderFileRel = {
            businessId: 0,
            businessType: 'supplier_reply',
            fileId: a.supplierFileId,
            supplierId: this.supplierId
          }
          a.projectDocumentSupplierContentList?.forEach(b => {
            b.id = b.contentSupplierId || null
            b.tenderFileRel = {
              businessId: 0,
              businessType: 'supplier_reply',
              fileId: b.supplierContentFileId,
              supplierId: this.supplierId
            }
          })
        })
        this.bidClosingDate = res.data.bidClosingDate
        this.projectDocumentList = res.data.projectDocumentSupplierList
        this.bidStatus = res.data.bidStatus
        if ([2, -1].includes(res.data.bidStatus)) {
          this.editMode = false
        }
      })
    },
    saveSupplierBid() {
      saveSupplierBid({
        projectId: this.projectId,
        supplierId: this.supplierId,
        projectDocumentSupplierList: this.projectDocumentList
      }).then(res => {
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.init()
      })
    },
    submitSupplierBid() {
      let pass = true
      this.projectDocumentList.forEach(a => {
        if (!a.tenderFileRel.fileId) {
          pass = false
        }
        a.projectDocumentSupplierContentList.forEach(b => {
          if (b.answerType === 1 && !b.tenderFileRel.fileId) {
            pass = false
          }
          if (b.answerType !== 1 && !b.supplierAnswer) {
            pass = false
          }
        })
      })
      if (!pass) {
        this.$message.error(this.$t('tender.pleaseFillInTheCompleteReplyContent'))
        return
      }
      this.$confirm(this.$t('tender.pleaseConfirmIfYouAreBiddingNow'), this.$t('supplier.tips'), {
        confirmButtonText: this.$t('order.determine'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        submitSupplierBid({
          projectId: this.projectId,
          supplierId: this.supplierId,
          projectDocumentSupplierList: this.projectDocumentList
        }).then(res => {
          this.$message.success(this.$t('supplier.submittedSuccessfully'))
          this.init()
        })
      })
    },
    handleFileSuccess(response, file, fileList, item, businessType) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
        return
      }
      item.supplierFileId = response.data.id
      item.supplierFileName = file.name
      item.supplierFilePath = response.data.url
      item.tenderFileRel.fileId = response.data.id
      item.tenderFileRel.businessType = 'supplier_reply'
    },
    handleFileContentSuccess(response, file, fileList, item, businessType) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
        return
      }
      item.supplierContentFileId = response.data.id
      item.supplierContentFileName = file.name
      item.supplierContentFilePath = response.data.url
      item.tenderFileRel.fileId = response.data.id
      item.tenderFileRel.businessType = 'supplier_reply'
    },
    showFile(path) {
      if (path) {
        window.open(path)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.required::before{
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}
</style>
