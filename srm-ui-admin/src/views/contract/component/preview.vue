
<template>
  <div>
    <embed
      v-if="contractId"
      :src="`${baseUrl}/contract/content-record/preview-contract-report?contractId=${contractId}&a#toolbar=0`"
      type="application/pdf"
      style="width: 100%;height: calc(100vh - 260px);"
    >
    <el-empty v-else />

  </div>
</template>
<script>

export default {
  name: 'Preview',
  props: ['contractId'],
  data() {
    return {
      baseUrl: `${getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API)}/admin-api`
    }
  },

  mounted() {
    // this.init()
  },
  methods: {

  }
}
</script>

<style scoped lang="scss">

</style>
