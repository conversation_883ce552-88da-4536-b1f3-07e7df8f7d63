
<template>
  <div>
    <el-tabs v-model="active" tab-position="left" type="border-card">

      <el-tab-pane label="内容拟定" name="preparation">
        <div style="padding: 15px">
          <div class="commonHead">
            <span class="required">基本信息</span>
          </div>
          <el-form
            ref="baseInfo"
            label-width="130px"
            :model="contractInfo"
            inline
            hide-required-asterisk
            :rules="contractInfoRule"
          >
            <el-form-item class="commonForm" label="合同名称" prop="name">
              <show-or-edit :value="contractInfo.name" :disabled="disabled">
                <el-input v-model="contractInfo.name" class="searchValue" placeholder="请输入" />
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="name" />
              </show-or-edit>
            </el-form-item>
            <el-form-item class="commonForm" label="合同性质" prop="contractType">
              <show-or-edit :value="contractInfo.contractType" :disabled="disabled">
                <el-radio-group v-model="contractInfo.contractType">
                  <el-radio
                    v-for="dict in getDictDatas(DICT_TYPE.CONTRACT_TYPE)"
                    :key="dict.value"
                    :label="dict.value"
                  >{{ dict.label }}
                  </el-radio>
                </el-radio-group>
              </show-or-edit>
            </el-form-item>
            <el-form-item class="commonForm" label="甲方名称" prop="firstPartyId">
              <show-or-edit :value="contractInfo.firstPartyId" :dict="DICT_TYPE.COMMON_COMPANY" :disabled="disabled">
                <el-select
                  v-model="contractInfo.firstPartyId"
                  filterable
                  :placeholder="$t('请选择')"
                  class="searchValue"
                >
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.COMMON_COMPANY)"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="firstPartyName" />
              </show-or-edit>
            </el-form-item>
            <el-form-item class="commonForm" label="乙方名称" prop="secondPartyName">
              <show-or-edit :value="contractInfo.secondPartyName" :disabled="disabled">
                <RemoteSupplier
                  ref="supplier"
                  :supplier-id.sync="contractInfo.secondPartyId"
                  :supplier-name.sync="contractInfo.secondPartyName"
                  class="searchValue"
                />
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="secondPartyName" />
              </show-or-edit>
            </el-form-item>
            <el-form-item class="commonForm" label="甲方法人" prop="firstPartyLegalPerson">
              <show-or-edit :value="contractInfo.firstPartyLegalPerson" :disabled="disabled">
                <el-input v-model="contractInfo.firstPartyLegalPerson" class="searchValue" placeholder="请输入" />
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="firstPartyLegalPerson" />
              </show-or-edit>
            </el-form-item>
            <el-form-item class="commonForm" label="乙方法人" prop="secondPartyLegalPerson">
              <show-or-edit :value="contractInfo.secondPartyLegalPerson" :disabled="disabled">
                <el-input v-model="contractInfo.secondPartyLegalPerson" class="searchValue" placeholder="请输入" />
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="secondPartyLegalPerson" />
              </show-or-edit>
            </el-form-item>
            <el-form-item class="commonForm" label="甲方地址" prop="firstPartyAddress">
              <show-or-edit :value="contractInfo.firstPartyAddress" :disabled="disabled">
                <el-input v-model="contractInfo.firstPartyAddress" class="searchValue" placeholder="请输入" />
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="firstPartyAddress" />
              </show-or-edit>
            </el-form-item>
            <el-form-item class="commonForm" label="乙方地址" prop="secondPartyAddress">
              <show-or-edit :value="contractInfo.secondPartyAddress" :disabled="disabled">
                <el-input v-model="contractInfo.secondPartyAddress" class="searchValue" placeholder="请输入" />
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="secondPartyAddress" />
              </show-or-edit>
            </el-form-item>
            <el-form-item class="commonForm" label="甲方联系电话" prop="firstPartyPhone">
              <show-or-edit :value="contractInfo.firstPartyPhone" :disabled="disabled">
                <el-input v-model="contractInfo.firstPartyPhone" class="searchValue" placeholder="请输入" />
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="firstPartyPhone" />
              </show-or-edit>
            </el-form-item>
            <el-form-item class="commonForm" label="乙方联系电话" prop="secondPartyPhone">
              <show-or-edit :value="contractInfo.secondPartyPhone" :disabled="disabled">
                <el-input v-model="contractInfo.secondPartyPhone" class="searchValue" placeholder="请输入" />
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="secondPartyPhone" />
              </show-or-edit>
            </el-form-item>

          </el-form>

          <div class="commonHead">
            <span class="required">合同概述</span>

          </div>
          <div style="width: 72%;padding: 10px 0 28px 28px">
            <el-form ref="contractAbstract" :model="contractInfo" :rules="contractInfoRule" hide-required-asterisk>
              <el-form-item label="" prop="contractAbstract">
                <show-or-edit :value="contractInfo.contractAbstract" :disabled="disabled">
                  <el-input v-model="contractInfo.contractAbstract" class="searchValue" type="textarea" placeholder="请输入" />
                </show-or-edit>
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="contractAbstract" />
              </el-form-item>
            </el-form>

          </div>
          <div class="commonHead">
            <span>标的物</span>
          </div>
          <div style="display: flex;justify-content: space-between;margin: 10px 0 ">
            <div style="display: flex;align-items: center">
              <BatchUpload
                :btn-name="$t('批量导入')"
                :template-download="async ()=>{ return getImportTemplate()}"
                template-name="标的物"
                :url="`contract/subject-matter-rel/import?contractId=${contractInfo.id}`"
                :after-upload="getSaleData"
                @click.native="saveBeforeUpload"
              />
              <span
                v-show="$refs.saleData?.selection.length"
                style="color: red;cursor:pointer;margin-left: 10px"
                @click="delSaleData"
              >
                批量删除</span>
            </div>

            <el-input v-model="queryParams.searchText" placeholder="输入物料编码/描述/规格" clearable style="width: 300px;" @keyup.enter.native="getSaleData">
              <i slot="suffix" style="cursor:pointer;" class="el-input__icon el-icon-search" @click="getSaleData" />
            </el-input>
          </div>
          <el-table ref="saleData" :data="saleData">
            <el-table-column type="selection" width="30" />
            <el-table-column prop="materialCode" label="物料编码" width="180">
              <template #default="scope">
                <show-or-edit :value="scope.row.materialCode" :disabled="disabled">
                  <el-autocomplete
                    v-model="scope.row.materialCode"
                    :fetch-suggestions="querySearchAsync"
                    :placeholder="$t('rfq.pleaseEnterTheContent')"
                    popper-class="el-autocomplete-suggestion"
                    :maxlength="100"
                    class="overList"
                    @select="(item)=>handleMaterialCodeSelect(item,scope.row)"
                  />
                </show-or-edit>
              </template>
            </el-table-column>
            <el-table-column prop="materialDescription" label="物料描述/服务描述" width="280">
              <template #default="scope">
                <show-or-edit :value="scope.row.materialDescription" :disabled="disabled">
                  <el-input v-model="scope.row.materialDescription" placeholder="请输入" />
                </show-or-edit>
              </template>
            </el-table-column>
            <el-table-column prop="specification" label="规格/型号" width="180">
              <template #default="scope">
                <show-or-edit :value="scope.row.specifications" :disabled="disabled">
                  <el-input v-model="scope.row.specifications" placeholder="请输入" />
                </show-or-edit>
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" width="108">
              <template #default="scope">
                <show-or-edit :value="scope.row.qty" :disabled="disabled">
                  <vxe-input
                    v-model="scope.row.qty"
                    type="integer"
                    class="content"
                    placeholder="请输入"
                    @blur="calculateTotal(scope.row)"
                    @prev-number="calculateTotal(scope.row)"
                    @next-number="calculateTotal(scope.row)"
                  />
                </show-or-edit>
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="单位" width="90">
              <template #default="scope">
                <show-or-edit :value="scope.row.unit" :dict="DICT_TYPE.MATERIAL_UOM" :disabled="disabled">
                  <el-select
                    v-model="scope.row.unit"
                    :disabled="disabled"
                    filterable
                  >
                    <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_UOM)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </template>
            </el-table-column>
            <el-table-column prop="priceBeforeTax" label="单价（未税）" width="135">
              <template #default="scope">
                <show-or-edit :value="scope.row.unitPriceBeforeTax" :disabled="disabled">
                  <vxe-input
                    v-model="scope.row.unitPriceBeforeTax"
                    :min="0.01"
                    type="number"
                    class="content"
                    placeholder="请输入"
                    @blur="calculateTotal(scope.row, 0)"
                    @prev-number="calculateTotal(scope.row)"
                    @next-number="calculateTotal(scope.row)"
                  />
                </show-or-edit>
              </template>
            </el-table-column>
            <el-table-column prop="taxRate" label="税率" width="180">
              <template #default="scope">
                <show-or-edit :value="scope.row.taxRate" :dict="DICT_TYPE.SUPPLIER_RATE" :disabled="disabled">
                  <el-select
                    v-model="scope.row.taxRate"
                    :disabled="disabled"
                    filterable
                  >
                    <el-option
                      v-for="dict in getDictDatas(DICT_TYPE.SUPPLIER_RATE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </show-or-edit>
              </template>
            </el-table-column>
            <el-table-column prop="priceAfterTax" label="单价（含税）" width="100">
              <template #default="scope">
                <show-or-edit :value="scope.row.unitPriceAfterTax" :disabled="disabled">
                  <vxe-input
                    v-model="scope.row.unitPriceAfterTax"
                    :min="0.01"
                    type="number"
                    class="content"
                    placeholder="请输入"
                    @blur="calculateTotal(scope.row)"
                    @prev-number="calculateTotal(scope.row)"
                    @next-number="calculateTotal(scope.row)"
                  />
                </show-or-edit>
              </template>
            </el-table-column>
            <el-table-column prop="amountPriceBeforeTax" label="小计（未税）" width="150" />
            <el-table-column prop="amountPriceAfterTax" label="小计（含税）" width="150" />
            <el-table-column prop="drawingNo" label="图纸号" width="132">
              <template #default="scope">
                <show-or-edit :value="scope.row.drawNo" :disabled="disabled">
                  <el-input v-model="scope.row.drawNo" placeholder="请输入" />
                </show-or-edit>
              </template>
            </el-table-column>
            <el-table-column prop="remarks" label="备注" width="190">
              <template #default="scope">
                <show-or-edit :value="scope.row.remark" :disabled="disabled">
                  <el-input v-model="scope.row.remark" placeholder="请输入" />
                </show-or-edit>
              </template>
            </el-table-column>
            <el-table-column prop="attachment" label="附件" width="300">
              <template #default="scope">
                <div v-for="(item,index) in scope.row.subjectMatterFileList" :key="index">
                  <el-button type="text" @click="downloadUrl(item.url)">{{ item.name }}</el-button>
                  <el-button type="text" @click="scope.row.subjectMatterFileList.splice(index,1)">删除</el-button>
                </div>
              </template>
            </el-table-column>

            <el-table-column align="center" fixed="right" label="操作" width="80">
              <template #default="scope">
                <div style="display: flex">

                  <el-upload
                    :action="uploadUrl"
                    :class="'upload-demo'"
                    :disabled="disabled"
                    :file-list="scope.row.subjectMatterFileList"
                    :headers="getBaseHeader()"
                    :on-success="(response, file, fileList)=>onSuccess(response, file, fileList,scope.row)"
                    multiple
                    :show-file-list="false"
                  >
                    <el-button type="text" :disabled="disabled">{{ $t('上传') }}</el-button>
                  </el-upload>

                  <el-popover
                    v-if="!calculateNone(scope.row)"
                    :ref="`popover-${scope.$index}`"
                    placement="left"
                    width="160"
                  >
                    <p>是否确认删除？</p>
                    <div style="text-align: right; margin: 0">
                      <el-button size="mini" @click="cancel">取消</el-button>
                      <el-button type="danger" plain size="mini" @click="cancel();delRow(scope)">确定</el-button>
                    </div>
                    <i slot="reference" style="margin-left: 5px;cursor: pointer" class="el-icon-delete" />
                  </el-popover>
                  <i
                    v-else
                    style="margin-left: 5px;cursor: pointer;margin-top: 5px"
                    class="el-icon-delete"
                    @click="delRow(scope)"
                  />

                </div>

              </template>

            </el-table-column>

          </el-table>
          <div style="display: flex;justify-content: center;margin: 5px 0">
            <el-button style="width: 200px" icon="el-icon-plus" @click="addLine">添加行</el-button>
          </div>
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNo"
            :total="total"
            @pagination="handlePageChange"
          />

          <el-form ref="bid" label-width="130px" :model="contractInfo" :rules="contractInfoRule">
            <el-form-item class="commonForm" label="币种">
              <show-or-edit :value="contractInfo.currency" :disabled="disabled" :dict="DICT_TYPE.COMMON_CURRENCY">
                <el-select v-model="contractInfo.currency" class="searchValue" filterable>
                  <el-option
                    v-for="dict in getDictDatas(DICT_TYPE.COMMON_CURRENCY)"
                    :key="dict.id"
                    :label="dict.name"
                    :value="dict.id"
                  />
                </el-select>
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="currency" />
              </show-or-edit>
            </el-form-item>
            <el-form-item class="commonForm" label="总计含税">
              <span v-loading="submitLoading">{{ calculateSum }}</span>
            </el-form-item>
            <el-form-item class="commonForm" label="总金额（未税）" prop="sumQuotedPriceBeforeTax">
              <show-or-edit :value="contractInfo.sumQuotedPriceBeforeTax" :disabled="disabled">
                <el-input-number v-model.number="contractInfo.sumQuotedPriceBeforeTax" type="number" :controls="false" class="searchValue" />
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="sumQuotedPriceBeforeTax" />
              </show-or-edit>
            </el-form-item>
            <el-form-item class="commonForm" label="总金额（含税）" style="width: 72%" prop="sumQuotedPriceAfterTax">
              <show-or-edit :value="contractInfo.sumQuotedPriceAfterTax" :disabled="disabled">
                <el-input-number v-model.number="contractInfo.sumQuotedPriceAfterTax" :controls="false" type="number" style="width: calc(50% - 95px)" class="searchValue" />
                <span v-show="contractInfo.currency === 3">
                  <span style="margin-left: 5px">大写:人民币</span>
                  <span style="margin: 0 5px">|</span>
                  <span>{{ rmb(contractInfo.sumQuotedPriceAfterTax) }}</span>
                </span>
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="sumQuotedPriceAfterTax" />
              </show-or-edit>
            </el-form-item>
            <el-form-item class="commonForm" label="标的物备注" style="width: 72%">
              <show-or-edit :value="contractInfo.subjectMatterRemarks" :disabled="disabled">
                <el-input v-model="contractInfo.subjectMatterRemarks" class="searchValue" />
                <change-log v-if="contractInfo.id" :id="contractInfo.id" field="subjectMatterRemarks" />
              </show-or-edit>
            </el-form-item>
          </el-form>
          <div class="commonHead">
            <span class="required">验收及付款</span>
          </div>
          <div style="width: 72%;padding: 10px 0 28px 28px">
            <el-form ref="acceptanceAndPaymentRemarks" :model="contractInfo" :rules="contractInfoRule" hide-required-asterisk>
              <el-form-item label="" prop="acceptanceAndPaymentRemarks">
                <show-or-edit :value="contractInfo.acceptanceAndPaymentRemarks" :disabled="disabled">
                  <el-input v-model="contractInfo.acceptanceAndPaymentRemarks" class="searchValue" type="textarea" />
                  <change-log v-if="contractInfo.id" :id="contractInfo.id" field="acceptanceAndPaymentRemarks" />
                </show-or-edit>
              </el-form-item>
            </el-form>
          </div>

          <div class="commonHead">
            <span>合同附件</span>
          </div>
          <div style="padding-bottom: 35px">
            <el-descriptions>
              <el-descriptions-item label-style="width: 120px;padding-right:8px;justify-content:flex-end" label="上传">
                <el-upload
                  :action="uploadUrl"
                  :class="'upload-demo'"
                  :disabled="disabled"
                  :file-list="contractInfo.contractFileList"
                  :headers="getBaseHeader()"
                  :on-success="(response, file, fileList)=>onSuccess0(response, file, fileList)"
                  :on-remove="onRemove"
                  multiple
                >
                  <el-button
                    :loading="submitLoading"
                    plain
                    type="primary"
                    :disabled="disabled"
                  >{{ $t('点击上传') }}</el-button>
                </el-upload>
              </el-descriptions-item>
            </el-descriptions>

          </div>
        </div>

      </el-tab-pane>
      <el-tab-pane v-if="contractInfo.id" label="条款明细" name="termInfo">
        <term-condition
          v-if="contractInfo.id&&active === 'termInfo'"
          ref="termInfo"
          @submitTermInfo="submitTermInfo"
        />
      </el-tab-pane>
    </el-tabs>
    <div v-if="active === 'preparation'&&contractInfo.status === 'toBeDeveloped'" class="fixedBottom">
      <el-button :loading="submitLoading" @click="saveContract('preparation')">保存草稿</el-button>
      <el-button :loading="submitLoading" type="primary" @click="next">下一步</el-button>
    </div>

  </div>
</template>
<script>
import rmb from '@/utils/rmb'
import { getBaseHeader } from '@/utils/request'
import { getConfig } from '@/utils/config'
import {
  deleteSubjectMatterRel,
  getImportTemplate,
  getSubjectMatterRelPage,
  saveContentRecord, saveSubjectMatterRel, submitContentRecord
} from '@/api/contract'
import { getMaterialInfo } from '@/api/material/main'
import { getMasterMaterialByMaterialId } from '@/api/rfq/home'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import BatchUpload from '@/components/BatchUpload/batchUpload.vue'
import RemoteSupplier from '@/components/RemoteSupplier/index.vue'
import ChangeLog from '@/views/contract/component/changeLog.vue'
import TermCondition from '@/views/contract/component/termCondition.vue'
import { DICT_TYPE, getDictData } from '../../../utils/dict'

export default {
  name: 'Elaboration',
  components: { TermCondition, ChangeLog, RemoteSupplier, BatchUpload, ShowOrEdit },
  inject: ['contract', 'termInfoPd'],
  data() {
    return {
      submitLoading: false,
      disabled: false,
      saleData: [],
      queryParams: {
        contractId: '',
        searchText: '',
        pageNo: 1,
        pageSize: 10
      },
      total: 0,
      uploadUrl: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload',
      contractInfoRule: {
        name: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
        contractType: [{ required: true, message: '请选择合同性质', trigger: 'change' }],
        firstPartyId: [{ required: true, message: '请输入甲方名称', trigger: 'change' }],
        secondPartyName: [{ required: true, message: '请输入乙方名称', trigger: 'change' }],
        firstPartyLegalPerson: [{ required: true, message: '请输入甲方法人', trigger: 'blur' }],
        secondPartyLegalPerson: [{ required: true, message: '请输入乙方法人', trigger: 'blur' }],
        firstPartyAddress: [{ required: true, message: '请输入甲方地址', trigger: 'blur' }],
        secondPartyAddress: [{ required: true, message: '请输入乙方地址', trigger: 'blur' }],
        firstPartyPhone: [{ required: true, message: '请输入甲方联系电话', trigger: 'blur' }],
        secondPartyPhone: [{ required: true, message: '请输入乙方联系电话', trigger: 'blur' }],
        contractAbstract: [{ required: true, message: '请输入合同概述', trigger: 'blur' }],
        sumQuotedPriceBeforeTax: [{ required: true, message: '请输入总金额（未税）', trigger: 'blur' }],
        sumQuotedPriceAfterTax: [{ required: true, message: '请输入总金额（含税）', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (this.calculateSum && value > this.calculateSum) {
                callback(new Error(this.$t('总金额（含税）不可大于总计含税')))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        acceptanceAndPaymentRemarks: [{ required: true, message: '请输入验收及付款', trigger: 'blur' }]

      },
      active: 'preparation',
      storeAmountPriceAfterTax: 0
    }
  },
  computed: {
    contractInfo() {
      return this.contract()
    },
    termInfo() {
      return this.termInfoPd()
    },
    calculateSum() {
      const total = this.saleData.reduce((total, item) => total + item.amountPriceAfterTax || 0, 0)
      if (total === this.storeAmountPriceAfterTax) {
        return this.contractInfo.sumAfterTax
      } else {
        return this.contractInfo.sumAfterTax + total - this.storeAmountPriceAfterTax
      }
    }
  },
  mounted() {
    this.emitter.on('saveBase', (data) => {
      this.saveContract('preparation')
    })
  },
  beforeDestroy() {
    this.emitter.off('saveBase')
  },
  methods: {
    rmb,
    getBaseHeader,
    getImportTemplate,
    init() {
      this.$nextTick(() => {
        this.active = this.$route.query.active || 'preparation'
      })
      // 初始化供应商列表
      this.$refs.supplier.doListNewAuthSupplier(this.contractInfo.secondPartyName)
      this.getSaleData()
    },
    async saveContract(active, fresh = 1) {
      this.submitLoading = true
      // 赋值 firstPartyName
      this.contractInfo.firstPartyName = getDictData(DICT_TYPE.COMMON_COMPANY, this.contractInfo.firstPartyId)?.name
      // 金额的中文处理
      this.contractInfo.sumQuotedPriceAfterTaxCn = rmb(this.contractInfo.sumQuotedPriceAfterTax)
      this.contractInfo.sumQuotedPriceBeforeTaxCn = rmb(this.contractInfo.sumQuotedPriceBeforeTax)
      const res = await saveContentRecord({
        contractId: this.contractInfo.id,
        headVO: {
          ...this.contractInfo,
          sumAfterTax: this.calculateSum || 0
        },
        subjectMatterList: this.saleData,
        contractFileList: this.contractInfo.contractFileList
      }).finally(() => {
        this.submitLoading = false
      })
      this.submitLoading = false
      if (this.$route.params.id === '0' && fresh) {
        this.$tab.closeOpenPage(`/contract/detail/${res.data.id}?name=${res.data.name}&active=${active}`)
        return
      }
      this.contractInfo.taskId = res.data.taskId
      this.contractInfo.processInstanceExtId = res.data.processInstanceExtId
      this.contractInfo.processInstanceId = res.data.processInstanceId
      this.contractInfo.sumAfterTax = res.data.sumAfterTax
      this.contractInfo.sumQuotedPriceBeforeTax = res.data.sumQuotedPriceBeforeTax
      this.contractInfo.sumQuotedPriceAfterTax = res.data.sumQuotedPriceAfterTax
      this.contractInfo.id = res.data.id
      this.getSaleData()
      this.$message.success('保存成功')
    },
    getSaleData() {
      getSubjectMatterRelPage(
        { ...this.queryParams,
          contractId: this.contractInfo.id
        }).then(res => {
        // 记录当前列表总计价格以验证用户填写的总计含税
        this.storeAmountPriceAfterTax = res.data.respVO.list.reduce((total, item) => total + item.amountPriceAfterTax, 0) || 0
        this.saleData = res.data.respVO.list
        this.total = res.data.respVO.total
        this.emitter.emit('getSaleData', this.saleData)
      })
    },
    addLine() {
      this.saleData.push({
        subjectMatterFileList: [
        ],
        contractId: null,
        materialCode: '',
        materialDescription: '',
        specifications: '',
        qty: null,
        unit: '',
        unitPriceBeforeTax: null,
        taxRate: '',
        unitPriceAfterTax: null,
        amountPriceBeforeTax: null,
        amountPriceAfterTax: null,
        drawNo: '',
        remark: ''
      })
    },
    downloadUrl(url) {
      window.open(url)
    },
    onSuccess(response, file, fileList, list) {
      list.subjectMatterFileList.push({
        id: response.data.id,
        url: response.data.url,
        name: response.data.name
      })
    },
    cancel() {
      document.body.click()
    },
    onSuccess0(response, file, fileList) {
      this.contractInfo.contractFileList.push({
        id: response.data.id,
        url: response.data.url,
        name: response.data.name
      })
    },
    onRemove(file) {
      this.contractInfo.contractFileList.splice(this.contractInfo.contractFileList.find(a => a.id === file.id), 1)
    },
    delSaleData() {
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.saleData = this.saleData.filter(item => !this.$refs.saleData.selection.includes(item))
        deleteSubjectMatterRel(this.contractInfo.id, this.$refs.saleData.selection.map(item => item.id).filter(a => a).join(',')).then(() => {
          this.$message.success('删除成功')
        })
      })
    },
    calculateNone(row) {
      // 判断对象属性是否都为空
      return Object.values(row).every(item => item === null || item === '' || item === 0 || item?.length === 0)
    },
    querySearchAsync(queryString, cb) {
      if (queryString.length < 2) {
        return
      }
      getMaterialInfo({
        materialCode: queryString
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item.materialCode + '-' + item.materialDescription,
            ...item
          }
        }))
      })
    },
    handleMaterialCodeSelect(item, row) {
      // 根据id去物料主数据的完整信息
      getMasterMaterialByMaterialId({
        materialCodeId: item.id
      }).then(res => {
        // 代入物料库数据，合并数据
        row.materialCode = res.data.material.materialCode
        row.materialDescription = res.data.material.materialDescription
        row.specifications = res.data.material.specifications
        row.unit = res.data.material.basicUnit
      })
    },
    saveBeforeUpload() {
      if (!this.contractInfo.id) {
        this.saveContract('preparation', 0)
      }
    },
    async saveSubject() {
      await saveSubjectMatterRel({
        contractId: this.contractInfo.id || null,
        subjectMatterList: this.saleData
      })
    },
    calculateTotal(row, type) {
      row.amountPriceBeforeTax = row.qty * row.unitPriceBeforeTax
      row.amountPriceAfterTax = row.qty * row.unitPriceAfterTax
    },
    delRow(scope) {
      this.saleData.splice(scope.$index, 1)
      if (scope.row.id) {
        deleteSubjectMatterRel(this.contractInfo.id, scope.row.id).then(() => {
          this.$message.success('删除成功')
        })
      }
    },
    async handlePageChange() {
      // 翻页需保存当前页标的物
      await this.saveSubject()
      this.getSaleData()
    },
    submitTermInfo() {
      this.$refs.termInfo.submitLoading = true
      submitContentRecord({
        processInstanceId: this.contractInfo.processInstanceId,
        taskId: this.contractInfo.taskId,
        contractId: this.contractInfo.id,
        headVO: this.contractInfo,
        clauseVO: { ...this.termInfo,
          contractId: this.contractInfo.id
        },
        subjectMatterList: this.saleData,
        contractFileList: this.contractInfo.contractFileList
      }).then(res => {
        this.$message.success('提交成功')
        this.$tab.closeOpenPage('/contract/contract')
      }).finally(() => {
        this.$refs.termInfo.submitLoading = false
      })
    },
    async next() {
      let pass = true
      const validateForm = async(ref) => {
        await this.$refs[ref].validate(valid => { if (!valid) pass = false })
      }
      await validateForm('baseInfo')
      await validateForm('contractAbstract')
      await validateForm('bid')
      await validateForm('acceptanceAndPaymentRemarks')
      if (pass) {
        await this.saveContract('termInfo')
        this.active = 'termInfo'
      }
    }
  }
}
</script>

<style scoped lang="scss">
.app-container{
  padding-bottom: 50px;
}
.required::after{
  content: "*";
  color: #ff4949;
  margin-left: 4px;
}
.commonHead{
  font-size: 16px;
  font-weight: bold;
  margin: 5px 0;
}

.commonForm{
  width: 36%;
  margin-right: 0;
  margin-bottom: 14px;
  ::v-deep .el-form-item__content {
    width: calc(100% - 130px);
  }
}

::v-deep .el-tabs--border-card > .el-tabs__header{
  background: none;
  height: 80vh;
}
::v-deep .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active{
  font-weight: bold;
  color: #1e1e1e;
  background: #DAE9F0;
}
::v-deep .el-tabs__item{
  text-align: left;
}
.searchValue{
  width: calc(100% - 30px);
}
.content{
  width: 100%;
}
::v-deep .el-autocomplete-suggestion{
  width: auto !important;
  min-width: 180px;
  max-width: 600px !important;
}
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
</style>
<style lang="scss">
.el-autocomplete-suggestion{
  width: auto !important;
  min-width: 180px;
  max-width: 600px !important;
}

</style>

