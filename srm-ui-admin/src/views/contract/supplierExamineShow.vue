<!--供应商待签订查看-->

<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="header-container">
        <span class="header-title">{{ contractInfo.name }}</span>
        <dict-tag :value="contractInfo.status" :type="DICT_TYPE.CONTRACT_STATUS" />
      </div>
      <div class="header-title" style="margin: 15px 0">已上传文件</div>

      <div style="border: 1px solid #EAEAEA;border-radius: 4px;padding: 20px 28px;font-size: 14px">
        <div v-for="item in fileList" style="font-size: 14px;margin: 3px ">
          <span> {{ item.name }}</span>
          <el-button
            type="text"
            style="margin-left: 5px;text-decoration: underline"
            @click="downloadUrl(item.url)"
          >下载</el-button>
        </div>
      </div>
      <div class="header-title" style="margin: 15px 0">合同文件</div>
      <file-show ref="fileShow" style="margin:10px 0" :pdf-info="pdfInfo" />
    </el-card>


  </div>
</template>
<script>
import dayjs from 'dayjs'
import rmb from '@/utils/rmb'
import { getBaseHeader } from '@/utils/request'
import { getConfig } from '@/utils/config'
import { createFileRel, getContentRecord, sendEmailToSupplier, submitContentRecord } from '@/api/contract'
import FileShow from '@/views/contract/component/fileShow.vue'
import ReturnBack from '@/views/contract/component/returnBack.vue';

export default {
  name: 'Supplierexamineshow',
  components: {ReturnBack, FileShow },
  provide() {
    return {
      contract: () => this.contractInfo,
      termInfoPd: () => this.termInfo
    }
  },
  data() {
    return {
      pdfInfo: {},
      uploadUrl: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload',
      contractInfo: {
        taskId: null,
        processInstanceId: '',
        processInstanceExtId: null,
        companyId: null,
        contractType: '',
        status: '',
        name: '',
        no: '',
        firstPartyId: null,
        firstPartyName: '',
        firstPartyLegalPerson: '',
        firstPartyAddress: '',
        firstPartyPhone: '',
        secondPartyId: null,
        secondPartyName: '',
        secondPartyLegalPerson: '',
        secondPartyAddress: '',
        secondPartyPhone: '',
        contractAbstract: '',
        currency: 3,
        sumQuotedPriceBeforeTax: null,
        sumAfterTax: null,
        sumQuotedPriceAfterTax: null,
        subjectMatterRemarks: '',
        acceptanceAndPaymentRemarks: '',
        effectiveTime: '',
        publicAgent: null,
        protocolAgent: null,
        contractFileList: []
      },
      termInfo: {
        templateId: 0,
        contractType: '',
        remarks: '',
        version: '',
        templateName: '',
        clauseDetail: '',
        type: [],
        useOriginTemplate: false,
        toBeDevelopedRemarks: ''
      },
      activeTab: 'preview',
      showSubmit: false,
      submitInfo: {
        supplierName: '',
        email: ''
      },
      fileList: []
    }
  },
  mounted() {
    if (this.$route.params.id !== '0') {
      this.init()
    }
  },
  methods: {
    dayjs,
    rmb,
    getBaseHeader,
    init() {
      getContentRecord(this.$route.params.id).then(res => {
        res.data.headVO.contractFileList = res.data.contractFileList || []
        res.data.headVO.taskId = res.data.taskId
        res.data.headVO.fileCount = res.data.fileCount
        this.contractInfo = res.data.headVO
        this.pdfInfo = res.data.contractPdfFileList.at(0)
        this.termInfo = res.data.clauseVO
        this.fileList = res.data.supplierAssignFileList
        this.$refs.fileShow.fileCount = res.data.fileCount
      })
    },
    async pass() {
      await submitContentRecord({
        processInstanceId: this.contractInfo.processInstanceId,
        taskId: this.contractInfo.taskId,
        contractId: this.contractInfo.id,
        headVO: this.contractInfo,
        clauseVO: {
          ...this.termInfo,
          contractId: this.contractInfo.id
        },
        contractFileList: this.contractInfo.contractFileList
      })
    },
    submitFile() {
      if (this.fileList.length === 0) {
        this.$message.error('请上传文件')
        return
      }
      createFileRel(this.fileList.map(a => {
        return {
          contractId: this.contractInfo.id,
          businessValue: 'supplier_assign',
          businessId: this.contractInfo.id,
          fileId: a.id || a.fileId || a.response.data.id,
          fileName: a.name || a.fileName || a.response.data.name
        }
      })).then(async res => {
        await this.pass()
        this.$tab.closeOpenPage('/contract/contract')
      })
    },
    onSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    downloadUrl(url) {
      window.open(url)
    },
  }
}
</script>

<style scoped lang="scss">
.header-title {
  font-size: 18px;
  font-weight: bold;
}
::v-deep .el-upload{
  display: flex;
}
::v-deep .el-upload-dragger{
  width: 100%;
  height: 108px;
  align-items: center;
  justify-content: center;
  border: 2px dashed #d9d9d9;
}
</style>
