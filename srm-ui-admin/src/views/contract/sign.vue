<!--待签订-->

<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="header-container">
        <span class="header-title">合同签订</span>
        <span style="margin: 0 15px">|</span>
        <span class="header-title">{{ contractInfo.name }}</span>
      </div>

      <div>
        <div class="header-title" style="margin: 35px 0 15px 0">上传已签订文件</div>
        <div :style="{ fontSize: '14px', color: fileError ? '#F56C6C' : '#929292', marginBottom: '10px' }">请下载合同文件，签字盖章并扫描后上传，完成签订</div>

        <el-upload
          :action="uploadUrl"
          :headers="getBaseHeader()"
          :on-success="onSuccess"
          class="upload-demo"
          drag
          multiple
          :file-list="fileList"
          :show-file-list="false"
        >
          <div
            style="height: 108px;
          align-items: center;font-size: 14px;
          display: flex;justify-content: center"
          >
            <i class="el-icon-plus" />
            <span style="margin-left: 5px">点击上传</span>
          </div>

        </el-upload>
      </div>
      <div>
        <div v-if="fileList.length" class="header-title" style="margin: 15px 0">已上传（{{ fileList.length }}）</div>
        <div v-for="item in fileList" style="font-size: 14px;margin: 3px 0">
          <span> {{ item.name }}</span>
          <el-button
            type="text"
            style="margin-left: 5px;text-decoration: underline"
            @click="fileList.splice(fileList.indexOf(item), 1)"
          >删除</el-button>
        </div>
      </div>
    </el-card>
    <el-card>
      <span class="header-title">合同文件</span>
      <file-show ref="fileShow" style="margin:10px 0" :pdf-info="pdfInfo" />

    </el-card>
    <div style="margin-top:15px;display: flex;justify-content: center">
      <return-back />
      <el-button type="primary" :loading="loading" @click="submitFile">提交</el-button>
    </div>

  </div>
</template>
<script>
import dayjs from 'dayjs'
import rmb from '@/utils/rmb'
import { getBaseHeader } from '@/utils/request'
import { getConfig } from '@/utils/config'
import { createFileRel, getContentRecord, submitContentRecord } from '@/api/contract'
import FileShow from '@/views/contract/component/fileShow.vue'
import ReturnBack from '@/views/contract/component/returnBack.vue'
import store from '@/store'
import {removeToken} from "@/utils/auth";

export default {
  name: 'Sign',
  components: { ReturnBack, FileShow },
  provide() {
    return {
      contract: () => this.contractInfo,
      termInfoPd: () => this.termInfo
    }
  },
  data() {
    return {
      loading: false,
      pdfInfo: {},
      uploadUrl: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload',
      contractInfo: {
        taskId: null,
        processInstanceId: '',
        processInstanceExtId: null,
        companyId: null,
        contractType: '',
        status: '',
        name: '',
        no: '',
        firstPartyId: null,
        firstPartyName: '',
        firstPartyLegalPerson: '',
        firstPartyAddress: '',
        firstPartyPhone: '',
        secondPartyId: null,
        secondPartyName: '',
        secondPartyLegalPerson: '',
        secondPartyAddress: '',
        secondPartyPhone: '',
        contractAbstract: '',
        currency: 3,
        sumQuotedPriceBeforeTax: null,
        sumAfterTax: null,
        sumQuotedPriceAfterTax: null,
        subjectMatterRemarks: '',
        acceptanceAndPaymentRemarks: '',
        effectiveTime: '',
        publicAgent: null,
        protocolAgent: null,
        contractFileList: []
      },
      termInfo: {
        templateId: 0,
        contractType: '',
        remarks: '',
        version: '',
        templateName: '',
        clauseDetail: '',
        type: [],
        useOriginTemplate: false,
        toBeDevelopedRemarks: ''
      },
      activeTab: 'preview',
      showSubmit: false,
      submitInfo: {
        supplierName: '',
        email: ''
      },
      fileList: [],
      fileError: false
    }
  },
  mounted() {
    if (this.$route.params.id !== '0') {
      this.init()
    }
  },
  methods: {
    dayjs,
    rmb,
    getBaseHeader,
    init() {
      getContentRecord(this.$route.params.id).then(res => {
        res.data.headVO.contractFileList = res.data.contractFileList || []
        res.data.headVO.taskId = res.data.taskId
        res.data.headVO.fileCount = res.data.fileCount
        this.contractInfo = res.data.headVO
        this.pdfInfo = res.data.contractPdfFileList.at(0)
        this.termInfo = res.data.clauseVO
        this.$refs.fileShow.fileCount = res.data.fileCount
        this.$refs.ela.init()
      })
    },
    async pass() {
      await submitContentRecord({
        processInstanceId: this.contractInfo.processInstanceId,
        taskId: this.contractInfo.taskId,
        contractId: this.contractInfo.id,
        headVO: this.contractInfo,
        clauseVO: {
          ...this.termInfo,
          contractId: this.contractInfo.id
        },
        contractFileList: this.contractInfo.contractFileList
      })
    },
    submitFile() {
      if (this.fileList.length === 0) {
        this.fileError = true
        return
      }
      this.loading = true
      this.fileError = false
      createFileRel(this.fileList.map(a => {
        return {
          contractId: this.contractInfo.id,
          businessValue: 'supplier_assign',
          businessId: this.contractInfo.id,
          fileId: a.id || a.fileId || a.response.data.id,
          fileName: a.name || a.fileName || a.response.data.name
        }
      })).then(async res => {
        await this.pass()
        if (store.getters.userId === -1) {
          this.$message.success('提交成功，即将返回登录页')
          setTimeout(() => {
            removeToken()
            this.$router.push('/login')
          }, 3500)
        } else {
          this.$tab.closeOpenPage('/contract/contract')
        }
      }).finally(() => {
          this.loading = false
        })
    },
    onSuccess(response, file, fileList) {
      this.fileList = fileList
      this.fileError = false
    }
  }
}
</script>

<style scoped lang="scss">
.header-title {
  font-size: 18px;
  font-weight: bold;
}
::v-deep .el-upload{
  display: flex;
}
::v-deep .el-upload-dragger{
  width: 100%;
  height: 108px;
  align-items: center;
  justify-content: center;
  border: 2px dashed #d9d9d9;
}
</style>
