<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('common.categoryCode')" prop="code">
        <el-input v-model="queryParams.code" clearable :placeholder="$t('common.pleaseEnterCategoryCode')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.name')" prop="name">
        <el-input v-model="queryParams.name" clearable :placeholder="$t('common.pleaseEnterAName')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.brandOrNot')" prop="brand">
        <el-select v-model="queryParams.brand" clearable :placeholder="$t('common.brandOrNot')">
          <el-option
            v-for="dict in statusDictDatas"
            :key="parseInt(dict.value)"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" clearable :placeholder="$t('common.status')">
          <el-option
            v-for="dict in statusDictDatas"
            :key="parseInt(dict.value)"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:categories:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd()"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:categories:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >{{ $t('common.export') }}
        </el-button>
      </el-col>
      <!--        批量创建-->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:categories:import']"
          :loading="exportLoading"
          icon="el-icon-upload2"
          size="mini"
          type="primary"
          @click="handleImport"
        >{{ $t('common.batchCreation') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:position-category:assign-position-categories']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAddPosition()"
        >{{ $t('system.categoryPositioningConfiguration') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button plain type="primary" @click="toggleExpandAll()">{{
          $t('common.unfoldcollapse')
        }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table
      v-if="refreshTable"
      ref="multipleTable"
      v-loading="loading"
      :data="list"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      row-key="id"
      @select="handleSelect"
      @selection-change="handleSelectionChange"
      @select-all="handleSelectAll"
    >
      <el-table-column
        type="selection"
        width="30"
      />
      <el-table-column :label="$t('common.categoryCode')" align="left" prop="code" />
      <el-table-column :label="$t('common.name')" align="center" prop="name" />
      <el-table-column :label="$t('system.grade')" align="center" prop="level" />
      <el-table-column :label="$t('common.brandOrNot')" align="center" prop="brand">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.brand" />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('sl.categoryPositioning')"
        align="center"
        prop="position"
        width="180"
      >
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY" :value="scope.row.position" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.status')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:categories:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:categories:create']"
            icon="el-icon-plus"
            size="mini"
            type="text"
            @click="handleAdd(scope.row)"
          >{{ $t('common.add') }}
          </el-button>
          <el-button
            v-hasPermi="['system:categories:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="480px">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('system.parentCategory')">
              <treeselect
                v-model="form.parentId"
                :normalizer="normalizer"
                :options="categoryOptions"
                :show-count="true"
                :placeholder="$t('system.pleaseSelectTheParentCategory')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('common.categoryCode')" prop="code">
              <el-input v-model="form.code" :placeholder="$t('common.pleaseEnterCategoryCode')" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col v-for="(item,i) in form.translations" :key="i" :span="24">
            <el-form-item
              :label="$t('auth.categoryName')"
              :prop="`translations[${i}].translation`"
              :rules="{required: true, message: $t('system.pleaseEnterCategoryName'), trigger: 'blur'}"
            ><el-input v-model="item.translation" maxlength="200">
              <svg-icon slot="suffix" :icon-class="item.locale" />
            </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('sl.categoryPositioning')" prop="position">
              <el-select v-model="form.position" style="width: 100%" clearable>
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('common.brandOrNot')" prop="brand">
              <el-radio-group v-model="form.brand">
                <el-radio v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="parseInt(dict.value)">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('common.status')" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in statusDictDatas" :key="parseInt(dict.value)" :label="parseInt(dict.value)">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          :action="upload.url"
          :auto-upload="false"
          :disabled="upload.isUploading"
          :headers="upload.headers"
          :limit="1"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          accept=".xlsx, .xls"
          class="small-padding"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

    <!-- 品类定位 -->
    <el-dialog :title="$t('system.categoryPositioningConfiguration')" :visible.sync="openPosition" append-to-body width="500px">
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item :label="$t('sl.categoryPositioning')" prop="position">
          <el-select v-model="form.position" class="searchValue" clearable>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitPosition">{{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  categoriesImportTemplate,
  createCategories,
  deleteCategories,
  exportCategoriesExcel,
  getCategories,
  getCategoriesList,
  getCategoryLevel,
  updateCategories, updatePosition
} from '@/api/system/categories'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { addTranslation } from '@/utils/i18n'

import { getBaseHeader } from '@/utils/request'
import {getConfig} from "@/utils/config";
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Categories',
  components: {
    Treeselect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 品类树选项
      categoryOptions: [],
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 品类列表
      list: [],
      // 弹出层标题
      title: '',
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        code: null,
        name: null,
        status: null,
        brand: null
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 人员类型:Sourcing
        type: '',
        // 上传的地址
        url: getConfig('VUE_APP_BASE_API', getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API)) + '/admin-api/system/categories/import'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        code: [{ required: true, message: this.$t('system.codeCannotBeEmpty'), trigger: 'blur' }],
        nameLocalId: [{ required: true, message: this.$t('system.translationCannotBeEmpty'), trigger: 'blur' }],
        position: [{ required: true, message: this.$t('品类定位不能为空'), trigger: 'blur' }],
        status: [{ required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }]
      },
      // 数据字典
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS),
      // 等级
      categoryLevel: getCategoryLevel(),
      openPosition: false,
      multipleSelection: []
    }
  },
  created() {
    this.getList()
  },
  methods: {

    /** 批量创建 */
    handleImport() {
      this.upload.title = this.$t('common.batchCreation')
      this.upload.open = true
      this.upload.type = 'SQE'
    }, /** 下载模板操作 */
    importTemplate() {
      categoriesImportTemplate().then(response => {
        this.$download.excel(response, '品类导入模板.xlsx')
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 拼接提示语
      const data = response.data
      let text = ''
      if (data.createCategories) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createCategories.length
      }
      if (data.failureCategories) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureCategories).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }
      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getCategoriesList(params).then(response => {
        this.list = this.handleTree(response.data, 'id')
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.openPosition = false
      this.reset()
    },
    /** 转换品类数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      getCategoriesList().then(response => {
        this.categoryOptions = []
        const category = { id: 0, name: this.$t('supplier.mainCategory'), children: [] }
        category.children = this.handleTree(response.data, 'id')
        this.categoryOptions.push(category)
      })
    },
    /** 表单重置 */
    reset() {
      this.form = {
        code: undefined,
        parentId: undefined,
        nameLocalId: undefined,
        name: undefined,
        level: undefined,
        position: undefined
      }
      this.resetForm('form')
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false
      this.isExpandAll = !this.isExpandAll
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      this.open = true
      this.title = this.$t('system.addCategory')
      // 新增时初始化中文翻译框
      addTranslation(this.form)
      this.getTreeselect()
      if (row != null && row.id) {
        this.form.parentId = row.id
      } else {
        this.form.parentId = 0
      }
      // 默认品牌关闭
      this.form.brand = 1
      // 默认状态开启
      this.form.status = 0
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      this.getTreeselect()
      getCategories(id).then(response => {
        this.form = response.data
        if (this.form.translations?.length === 0) {
          // 新增时初始化中文翻译框
          addTranslation(this.form)
        }
        this.open = true
        this.title = this.$t('system.modifyCategory')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.form.name = this.form.translations?.find(item => item.locale === 'zh').translation
        // 修改的提交
        if (this.form.id != null) {
          // this.form.id=null;
          updateCategories(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createCategories(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除品类编号为"' + id + '"的数据项?').then(function() {
        return deleteCategories(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      this.exportLoading = true
      return exportCategoriesExcel(params).then(response => {
        this.$download.excel(response, this.$t('system.categoryXls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    handleSelectAll() {
      const isAllSelected = this.$refs.multipleTable.store.states.isAllSelected
      const _handleSelectAll = data => {
        data.forEach(item => {
          item.select
          this.$refs.multipleTable.toggleRowSelection(item, isAllSelected)
          _handleSelectAll(item.children || [])
        })
      }
      _handleSelectAll(this.list)
    },
    handleSelect(selection, current) {
      // 判断selection中是否存在current,若是存在那么就代表是被勾选上了,若是不存在代表是取消勾选了
      const isChecked = !!selection.find(item => item.id === current.id)
      // 如果当前项被取消勾选
      if (!isChecked) {
        // 那么其所有的祖先也应该被取消勾选
        this.uncheckedParents(selection, current)
        // 那么其所有的后代也应该被取消勾选
        this.toggleCheckedChildrens(selection, current, false)
      } else {
        // 如果当前项被勾选
        // 那么若同一组的元素都被勾选了,那么父元素将也被勾选,依次往上类推
        this.checkedParents(selection)
        // 那么其所有的后代都要被勾选
        this.toggleCheckedChildrens(selection, current, true)
      }
    },
    uncheckedParents(selection, item) {
      const _uncheckedParents = data => {
        return data.find(element => {
          if (element.id === item.id) {
            return true
          } else if (_uncheckedParents(element.children || [])) {
            this.$refs.multipleTable.toggleRowSelection(element, false)
            for (let i = selection.length - 1; i >= 0; i--) {
              if (selection[i].id === element.id) {
                selection.splice(i, 1)
                break
              }
            }
            return true
          } else {
            return false
          }
        })
      }
      _uncheckedParents(this.list)
    },
    toggleCheckedChildrens(selection, item, isChecked) {
      const _toggleCheckedChildrens = data => {
        data.find(element => {
          this.$refs.multipleTable.toggleRowSelection(element, isChecked)
          if (isChecked && !selection.find(item => item.id === element.id)) {
            selection.push(element)
          } else if (!isChecked && selection.find(item => item.id === element.id)) {
            for (let i = selection.length - 1; i >= 0; i--) {
              if (selection[i].id === element.id) {
                selection.splice(i, 1)
                break
              }
            }
          }
          _toggleCheckedChildrens(element.children || [])
        })
      }
      _toggleCheckedChildrens(item.children || [])
    },
    checkedParents(selection) {
      const _checkedParents = element => {
        const children = element.children
        if (children && children.length) {
          const allChildrenChecked = children.every(child => {
            return _checkedParents(child)
          })
          if (allChildrenChecked) {
            this.$refs.multipleTable.toggleRowSelection(element, true)
            if (!selection.find(item => item.id === element.id)) {
              selection.push(element)
            }
          }
        }
        return selection.find(item => item.id === element.id)
      }
      this.list.forEach(element => {
        _checkedParents(element)
      })
    },
    /**
     * 选择框
     * @param val
     */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleAddPosition() {
      if (this.multipleSelection == null || this.multipleSelection.length === 0) {
        this.$modal.msgError(this.$t('supplier.pleaseSelectData'))
        return
      }
      this.reset()
      this.openPosition = true
    },
    submitPosition() {
      updatePosition(
        {
          ids: this.multipleSelection.map(a => a.id),
          position: this.form.position
        }
      ).then(res => {
        this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
        this.openPosition = false
        this.getList()
      })
    }
  }
}
</script>
