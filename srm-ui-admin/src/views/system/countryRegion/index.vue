<template>
  <div class="app-container">

    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item :label="$t('common.name')" prop="name">
        <el-input
          v-model="queryParams.name"
          :placeholder="$t('common.pleaseEnterAName')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('supplier.pleaseSelectStatus')" clearable size="small">
          <el-option
            v-for="dict in statusDicts"
            :key="parseInt(dict.value)"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.createTime')">
        <el-date-picker
          v-model="dateRangeCreateTime"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          style="width: 240px"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button plain icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:country-region:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:country-region:export']"
          :loading="exportLoading"
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >{{ $t('common.export') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:country-region:import']"
          plain
          size="mini"
          @click="upload.open = true"
        >{{ $t('导入') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="list"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      row-key="id"
    >
      <el-table-column align="left" :label="$t('system.code')" prop="code" />
      <el-table-column align="center" :label="$t('common.name')" prop="name" width="250" />
      <el-table-column align="center" :label="$t('system.type')" prop="type">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_COUNTRY_REGION_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('common.status')" prop="status">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('common.createTime')" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" :label="$t('common.operate')">
        <template slot-scope="scope">

          <el-button
            v-hasPermi="['system:country-region:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:country-region:create']"
            icon="el-icon-plus"
            size="mini"
            type="text"
            @click="handleAdd(scope.row)"
          >{{ $t('common.add') }}
          </el-button>
          <el-button
            v-hasPermi="['system:country-region:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('system.superior')">
          <treeselect
            v-model="form.parentId"
            :normalizer="normalizer"
            :options="treeOptions"
            :placeholder="$t('system.selectTheParentCountryregion')"
            :show-count="true"
          />
        </el-form-item>
        <el-form-item :label="$t('system.code')" prop="code">
          <el-input v-model="form.code" :placeholder="$t('system.pleaseEnterTheCountryregionCode')" />
        </el-form-item>
        <div v-for="(item, index) in form.translations" :key="index">
          <el-form-item
            :label="$t('common.name')"
            :prop="`translations[${index}].translation`"
            :rules="{required: true, message: $t('common.pleaseEnterAName'), trigger: 'blur'}"
          >
            <el-input v-model="item.translation">
              <svg-icon slot="suffix" :icon-class="item.locale" />
            </el-input>
          </el-form-item>
        </div>
        <el-form-item :label="$t('system.type')" prop="type">
          <el-select v-model="form.type">
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SYSTEM_COUNTRY_REGION_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('supplier.displayOrder')" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :placeholder="$t('system.pleaseEnterTheDisplayOrder')" />
        </el-form-item>
        <el-form-item :label="$t('common.status')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
    <el-dialog :title="upload.title" :visible.sync="upload.open" append-to-body width="400px">
      <div class="text-center">
        <el-upload
            ref="upload"
            :action="upload.url"
            :auto-upload="false"
            :disabled="upload.isUploading"
            :headers="upload.headers"
            :limit="1"
            :on-preview="onPreview"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            accept=".xlsx, .xls"
            class="small-padding"
            drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button plain type="primary" @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  createCountryRegion,
  deleteCountryRegion,
  exportCountryRegionExcel,
  getCountryRegion,
  getCountryRegionPage,
  listCountryRegion,
  updateCountryRegion,
  importTemplate
} from '@/api/system/countryRegion'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import IconSelect from '@/components/IconSelect'

import { CommonStatusEnum } from '@/utils/constants'
import { addTranslation } from '@/utils/i18n'
import { getBaseHeader } from '@/utils/request'
import {getConfig} from "@/utils/config";
export default {
  name: 'Countryregion',
  // eslint-disable-next-line vue/no-unused-components
  components: { Treeselect, IconSelect },
  data() {
    return {
      upload: {
        // 是否显示弹出层（物料导入）
        open: false,
        // 弹出层标题（物料导入）
        title: this.$t('导入'),
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: getConfig('VUE_APP_BASE_API', getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API)) + '/admin-api/system/country-region/importData'
      },
      // 国家地区tree选项
      treeOptions: [],
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 国家地区列表(tree)
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nameLocalId: null,
        name: null,
        type: null,
        parentId: null,
        level: null,
        code: null,
        relCode: null,
        sort: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: this.$t('system.nameCannotBeEmpty'), trigger: 'blur' }],
        type: [{ required: true, message: this.$t('system.typeCannotBeEmpty'), trigger: 'change' }],
        parentId: [{ required: false, message: this.$t('system.pleaseSelectASuperior'), trigger: 'blur' }],
        code: [{ required: true, message: this.$t('system.codeCannotBeEmpty'), trigger: 'blur' }],
        sort: [{ required: true, message: this.$t('system.displayOrderCannotBeEmpty'), trigger: 'blur' }]
      },
      statusDicts: getDictDatas(DICT_TYPE.COMMON_STATUS)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true
      // 处理查询参数
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行查询
      getCountryRegionPage(params).then(response => {
        this.list = this.handleTree(response.data, 'id')
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        nameLocalId: undefined,
        name: undefined,
        type: undefined,
        parentId: undefined,
        level: undefined,
        code: undefined,
        relCode: undefined,
        sort: undefined,
        status: CommonStatusEnum.ENABLE
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      // 新增时初始化中文翻译框
      if (row) {
        // 新增时初始化中文翻译框
        addTranslation(this.form)
      }
      this.getCountryRegionTree()
      if (row != null && row.id) {
        this.form.parentId = row.id
      } else {
        this.form.parentId = 0
      }
      this.open = true
      this.title = this.$t('system.addCountryregion')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getCountryRegionTree()
      getCountryRegion(row.id).then(response => {
        this.form = response.data
        if (this.form.translations?.length === 0) {
          // 新增时初始化中文翻译框
          addTranslation(this.form)
        }
        this.open = true
        this.title = this.$t('system.modifyCountryregion')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (!valid) {
          return
        }
        this.form.name = this.form.translations?.find(item => item.locale === 'zh').translation
        this.form.type = this.form.parentId === 0 ? 'country' : 'region'
        // 修改的提交
        if (this.form.id != null) {
          updateCountryRegion(this.form).then(response => {
            this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
            this.open = false
            this.getList()
          })
          return
        }
        // 添加的提交
        createCountryRegion(this.form).then(response => {
          this.$modal.msgSuccess(this.$t('common.addSuccess'))
          this.open = false
          this.getList()
        })
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id
      this.$modal.confirm('是否确认删除国家地区编号为"' + id + '"的数据项?').then(function() {
        return deleteCountryRegion(id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 处理查询参数
      const params = { ...this.queryParams }
      params.pageNo = undefined
      params.pageSize = undefined
      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime')
      // 执行导出
      this.$modal.confirm(this.$t('system.areYouSureToExportAllCountryregionDataItems')).then(() => {
        this.exportLoading = true
        return exportCountryRegionExcel(params)
      }).then(response => {
        this.$download.excel(response, this.$t('system.countryxls'))
        this.exportLoading = false
      }).catch(() => {
      })
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      // 做后端数据和treeSelect组件默认的字段映射
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    // 查询国家地区的树结构
    getCountryRegionTree() {
      const obj = {
        ids: []
      }
      listCountryRegion(obj).then(response => {
        this.treeOptions = []
        const countryRegion = { id: 0, name: this.$t('system.toplevel'), children: [] }
        countryRegion.children = this.handleTree(response.data, 'id')
        this.treeOptions.push(countryRegion)
      })
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.$download.excel(response, '国家地区导入模板.xlsx')
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      this.$message.success("导入成功")
      this.getList()
    },
    onPreview(file) {
      this.downloadFile(file)
    },
  }
}
</script>
