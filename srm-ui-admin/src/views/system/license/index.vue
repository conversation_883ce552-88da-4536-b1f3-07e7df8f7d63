<template>
  <div class="license">
    <common-card
      title="安装证书"
    >
      <div class="common-body">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-form-item>
            当前证书有效期：{{ validityDate }}
          </el-form-item>
          <el-form-item>
            <el-button @click="handleAdd()">上传证书</el-button>
          </el-form-item>
        </el-form>
      </div>
    </common-card>

    <!-- 上传文件 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".lic, .keystore"
        :auto-upload="false"
        drag
        :headers="upload.headers"
        :action="upload.url"
        :data="upload.data"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">
          将文件拖到此处，或 <em>点击上传</em>
        </div>
        <div slot="tip" class="el-upload__tip" style="color:red">提示：仅允许导入 lic 格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">取消</el-button>
        <el-button type="primary" @click="submitFileForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getConfig } from '@/utils/config'
import {
  getLicensFileValidityDate,
  installLicense
} from '@/api/license/license'
import { getAccessToken } from '@/utils/auth'
export default {
  name: 'Index',
  data() {
    return {
      form: {},
      rules: {},
      validityDate: '',
      upload: {
        open: false, // 是否显示弹出层
        title: '', // 弹出层标题
        isUploading: false, // 是否禁用上传
        url: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/license/upload-license-file', // 请求地址
        headers: { Authorization: 'Bearer ' + getAccessToken() } // 设置上传的请求头部
      }
    }
  },
  mounted() {
    this.getValidityDate()
  },
  methods: {
    getValidityDate() {
      getLicensFileValidityDate().then(resp => {
        this.validityDate = resp.data.validityDate
      })
    },
    /** 安装证书 */
    installLicenseFile() {
      installLicense().then(resp => {
        if (resp.data.result) {
          this.$message.success(resp.data.message)
          this.getValidityDate()
        } else {
          this.$message.error(this.$t('证书安装失败'))
        }
      })
    },
    /** 上传文件 */
    handleAdd() {
      this.upload.open = true
      this.upload.title = '上传文件'
    },
    /** 处理文件上传中 */
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true // 禁止修改
    },
    /** 文件上传成功处理 */
    handleFileSuccess(response, file, fileList) {
      // 清理
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      this.$modal.confirm('上传成功，是否立即安装证书？').then(() => {
        this.installLicenseFile()
      })
    },
    /** 发起文件上传 */
    submitFileForm() {
      this.$refs.upload.submit()
    }
  }
}
</script>

<style lang="scss" scoped>
.license {
  padding: 15px 20px;
}
.common-body {
  padding: 0 100px;
}
</style>
