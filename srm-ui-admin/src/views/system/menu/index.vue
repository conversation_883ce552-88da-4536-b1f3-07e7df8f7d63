<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" size="small">
      <el-form-item :label="$t('system.menuName')" prop="name">
        <el-input v-model="queryParams.name" clearable :placeholder="$t('system.pleaseEnterTheMenuName')" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.status')" prop="status">
        <el-select v-model="queryParams.status" clearable :placeholder="$t('system.menuStatus')">
          <el-option
            v-for="dict in statusDictDatas"
            :key="parseInt(dict.value)"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('system.onlyOpenToSuppliers')" prop="status">
        <el-select v-model="queryParams.toSupplier" clearable :placeholder="$t('system.onlyOpenToSuppliers')">
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_Y_N)"
            :key="parseInt(dict.value) === 0 ? 1 : 0"
            :label="dict.label"
            :value="parseInt(dict.value) === 0 ? 1 : 0 "
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:menu:create']"
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >{{ $t('common.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="el-icon-sort" plain size="mini" type="info" @click="toggleExpandAll">{{ $t('common.expandCollapse') }}</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="menuList"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      row-key="id"
    >
      <el-table-column :show-overflow-tooltip="true" :label="$t('system.menuName')" prop="name" width="250" />
      <el-table-column
        align="center"
        :label="$t('system.icon')"
        prop="icon"
        width="100"
      >
        <template slot-scope="scope">
          <svg-icon :icon-class="scope.row.icon" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.sort')" prop="sort" width="60" />
      <el-table-column :show-overflow-tooltip="true" :label="$t('system.permissionId')" prop="permission" />
      <el-table-column :show-overflow-tooltip="true" :label="$t('system.componentPath')" prop="component" />
      <el-table-column :label="$t('common.status')" prop="status" width="80">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        class-name="small-padding fixed-width"
        :label="$t('common.operate')"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:menu:update']"
            icon="el-icon-edit"
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.modify') }}
          </el-button>
          <el-button
            v-hasPermi="['system:menu:create']"
            icon="el-icon-plus"
            size="mini"
            type="text"
            @click="handleAdd(scope.row)"
          >{{ $t('common.add') }}
          </el-button>
          <el-button
            v-hasPermi="['system:menu:delete']"
            icon="el-icon-delete"
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
          >{{ $t('common.del') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="680px">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('system.parentMenu')">
              <treeselect
                v-model="form.parentId"
                :normalizer="normalizer"
                :options="menuOptions"
                :show-count="true"
                :placeholder="$t('system.selectTheParentMenu')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('system.menuType')" prop="type">
              <el-radio-group v-model="form.type">
                <el-radio v-for="dict in menuTypeDictDatas" :key="parseInt(dict.value)" :label="parseInt(dict.value)">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item v-if="form.type !== 3" :label="$t('system.menuIcon')">
              <el-popover placement="bottom-start" trigger="click" width="460" @show="$refs['iconSelect'].reset()">
                <IconSelect ref="iconSelect" @selected="selected" />
                <el-input
                  slot="reference"
                  v-model="form.icon"
                  :placeholder="$t('system.clickTheSelectIcon')"
                  readonly
                >
                  <svg-icon
                    v-if="form.icon"
                    slot="prefix"
                    :icon-class="form.icon"
                    class="el-input__icon"
                    style="height: 32px;width: 16px;"
                  />
                  <i v-else slot="prefix" class="el-icon-search el-input__icon" />
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col v-for="(item,i) in form.translations" :span="12">
            <el-form-item
              :label="$t('system.menuName')"
              :prop="`translations[${i}].translation`"
              :rules="{required: true, message: $t('system.pleaseEnterTheMenuName'), trigger: 'blur'}"
            >
              <el-input v-model="item.translation">
                <svg-icon slot="suffix" :icon-class="item.locale" />
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.showSort')" prop="sort">
              <el-input-number v-model="form.sort" :min="0" controls-position="right" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.type !== 3" :label="$t('system.routingAddress')" prop="path">
              <span slot="label">
                <el-tooltip :content="$t('system.anInternetAddressIsRequiredItStartsWithhttps')" placement="top">
                  <i class="el-icon-question" />
                </el-tooltip>
                {{ $t('system.routingAddress') }}
              </span>
              <el-input v-model="form.path" :placeholder="$t('system.pleaseEnterTheRoutingAddress')" />
            </el-form-item>
          </el-col>
          <el-col v-if="form.type === 2" :span="12">
            <el-form-item :label="$t('system.componentPath')" prop="component">
              <el-input v-model="form.component" :placeholder="$t('system.pleaseEnterTheComponentPath')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.type !== 1" :label="$t('system.permissionId')">
              <span slot="label">
                <el-tooltip
                  content="Controller 方法上的权限字符，如：@PreAuthorize(`@ss.hasPermission('system:user:list')`)"
                  placement="top"
                >
                  <i class="el-icon-question" />
                </el-tooltip>
                {{ $t('system.permissionCharacter') }}
              </span>
              <el-input
                v-model="form.permission"
                maxlength="100"
                :placeholder="$t('system.pleaseIdentifyThePermission')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.menuStatus')" prop="status">
              <span slot="label">
                <el-tooltip :content="$t('system.whenDisablingTheRouteWillNotAppearInTheSidebarAndCannotBeAccessed')" placement="top">
                  <i class="el-icon-question" />
                </el-tooltip>
                {{ $t('system.menuStatus') }}
              </span>
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="parseInt(dict.value)"
                >{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.type !== 3" :label="$t('system.showStatus')">
              <span slot="label">
                <el-tooltip :content="$t('system.whenHideIsSelectedRoutesWillNotAppearInTheSidebarButCanStillBeAccessed')" placement="top">
                  <i class="el-icon-question" />
                </el-tooltip>
                {{ $t('system.whetherToDisplay') }}
              </span>
              <el-radio-group v-model="form.visible">
                <el-radio :key="true" :label="true">{{ $t('rfq.display') }}</el-radio>
                <el-radio :key="false" :label="false">{{ $t('rfq.hide') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.type === 2" :label="$t('system.showStatus')">
              <span slot="label">
                <el-tooltip :content="$t('system.routingAddressOfMatchingComponentsNeedToBeConsistent')" placement="top">
                  <i class="el-icon-question" />
                </el-tooltip>
                {{ $t('system.cacheOrNot') }}
              </span>
              <el-radio-group v-model="form.keepAlive">
                <el-radio :key="true" :label="true">{{ $t('system.cache') }}</el-radio>
                <el-radio :key="false" :label="false">{{ $t('system.dontCache') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('system.onlyOpenToSuppliers')" prop="toSupplier">
              <span slot="label">
                <el-tooltip :content="$t('system.onlyTheSupplierAccountCanSeeIt')" placement="top">
                  <i class="el-icon-question" />
                </el-tooltip>
                {{ $t('system.onlyOpenToSuppliers') }}
              </span>
              <el-radio-group v-model="form.toSupplier">
                <!--                <el-radio-->
                <!--                  v-for="dict in getDictDatas(DICT_TYPE.COMMON_Y_N)"-->
                <!--                  :key="dict.value"-->
                <!--                  :label="parseInt(dict.value)"-->
                <!--                  :value="parseInt(dict.value)"-->
                <!--                >{{ dict.label }}-->
                <!--                </el-radio>-->
                <el-radio :label="true">{{ $t('auth.yes') }}</el-radio>
                <el-radio :label="false">{{ $t('auth.no') }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="submitForm">{{ $t('order.determine') }}</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addMenu, delMenu, getMenu, listMenu, updateMenu } from '@/api/system/menu'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import IconSelect from '@/components/IconSelect'

import { CommonStatusEnum, SystemMenuTypeEnum } from '@/utils/constants'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { isExternal } from '@/utils/validate'
import { addTranslation } from '@/utils/i18n'

export default {
  name: 'Menu',
  components: { Treeselect, IconSelect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 菜单表格树数据
      menuList: [],
      // 菜单树选项
      menuOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否展开，默认全部折叠
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        name: undefined,
        visible: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: this.$t('system.menuNameCannotBeEmpty'), trigger: 'blur' }
        ],
        sort: [
          { required: true, message: this.$t('system.menuOrderCannotBeEmpty'), trigger: 'blur' }
        ],
        path: [
          { required: true, message: this.$t('system.routingAddressCannotBeEmpty'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('supplier.statusCannotBeEmpty'), trigger: 'blur' }
        ]
      },

      // 枚举
      MenuTypeEnum: SystemMenuTypeEnum,
      CommonStatusEnum: CommonStatusEnum,
      // 数据字典
      menuTypeDictDatas: getDictDatas(DICT_TYPE.SYSTEM_MENU_TYPE),
      statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS)
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 选择图标
    selected(name) {
      this.form.icon = name
    },
    /** 查询菜单列表 */
    getList() {
      this.loading = true
      listMenu(this.queryParams).then(response => {
        this.menuList = this.handleTree(response.data, 'id')
        this.loading = false
      })
    },
    /** 转换菜单数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      listMenu().then(response => {
        this.menuOptions = []
        const menu = { id: 0, name: this.$t('supplier.mainCategory'), children: [] }
        menu.children = this.handleTree(response.data, 'id')
        this.menuOptions.push(menu)
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        parentId: 0,
        name: undefined,
        icon: undefined,
        type: SystemMenuTypeEnum.DIR,
        sort: undefined,
        status: CommonStatusEnum.ENABLE,
        visible: true,
        keepAlive: true
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false
      this.isExpandAll = !this.isExpandAll
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()

      if (row) {
        // 新增时初始化中文翻译框
        addTranslation(this.form)
      }
      this.getTreeselect()
      if (row != null && row.id) {
        this.form.parentId = row.id
      } else {
        this.form.parentId = 0
      }
      this.open = true
      this.title = this.$t('system.addMenu')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getTreeselect()
      getMenu(row.id).then(response => {
        this.form = response.data
        if (this.form.translations?.length === 0) {
          // 新增时初始化中文翻译框
          addTranslation(this.form)
        }
        this.open = true
        this.title = this.$t('system.modifyMenu')
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.name = this.form.translations?.find(item => item.locale === 'zh').translation
          // 若权限类型为目录或者菜单时，进行 path 的校验，避免后续拼接出来的路由无法跳转
          if (this.form.type === SystemMenuTypeEnum.DIR ||
            this.form.type === SystemMenuTypeEnum.MENU) {
            // 如果是外链，则不进行校验
            const path = this.form.path
            if (!isExternal(path)) {
              // 父权限为根节点，path 必须以 / 开头
              if (this.form.parentId === 0 && path.charAt(0) !== '/') {
                this.$modal.msgSuccess(this.$t('system.frontEndMustStartWith'))
                return
              } else if (this.form.parentId !== 0 && path.charAt(0) === '/') {
                this.$modal.msgSuccess(this.$t('system.frontEndCannotStartWith'))
                return
              }
            }
          }

          // 提交
          if (this.form.id !== undefined) {
            updateMenu(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('common.modifiedSuccessfully'))
              this.open = false
              this.getList()
            })
          } else {
            addMenu(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('common.addSuccess'))
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除名称为"' + row.name + '"的数据项?').then(function() {
        return delMenu(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.$t('common.delSuccess'))
      }).catch(() => {
      })
    }
  }
}
</script>
