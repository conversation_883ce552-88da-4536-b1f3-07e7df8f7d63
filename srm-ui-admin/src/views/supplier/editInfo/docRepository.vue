<template>
  <div style="padding:  15px 20px">
    <div>
      <show-or-edit
        disabled="true"
        :value="supplierNameAndNo"
        style="font-size: 16px; font-weight: 700; color: rgba(73, 150, 184, 0.99); padding-left: 10px"
      >
        <el-input  v-model="supplierNameAndNo" />
      </show-or-edit>
    </div>
    <div id="document" class="form-title">5.1 <span style="margin-left:5px">{{
        $t('supplier.supplierDocumentLibrary')
      }}</span></div>
    <docRepository v-if="docRepositoryRef&&supplierId" ref="docRepositoryRef" :supplier-id-ref="supplierId" @listdata="docRepositoryListData" />

  </div>
</template>

<script>
import docRepository from '@/views/supplier/docRepository'
import { getBaseHeader, handleAuthorized } from '@/utils/request'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { detailBankInfo } from '@/api/supplier/info'
import {getConfig} from "@/utils/config";

export default {
  name: 'DocRepository/:id',
  components: {
    ShowOrEdit,
    docRepository
  },
  data() {
    return {
      supplierNameAndNo: '',
      list: [],
      docRepositoryRef: true,
      docRepositoryList: [],
      uploadVisible: false,
      supplierId: null,
      upload: {
        headers: getBaseHeader(),
        url: '',
        isUploading: false
      },
      showUpload: false,
      uploadUrl: '',
      headers: getBaseHeader(),
      fileType: ''
    }
  },
  created() {
    this.supplierId = this.$route.query.supplierId
    this.init()
  },
  mounted() {
    this.supplierId = this.$route.query.supplierId
  },
  methods: {
    docRepositoryListData(list) {
      this.docRepositoryList = [...list]
    },
    init() {
      this.uploadUrl = getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload'
      this.upload.url = getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + `/admin-api/supplier/base-info/import-equipment?supplierId=${this.supplierId}`
      this.getConfigKey('file.type.common').then(response => {
        this.fileType = response.data
      })
      // 复用下该接口去展示供应商名称
      detailBankInfo({
        supplierId: this.supplierId
      }).then(async res => {
        if (res.data.supplierCode){
          this.supplierNameAndNo = res.data.supplierName + ' - ' + res.data.supplierCode
        }else {
          this.supplierNameAndNo = res.data.supplierName
        }
      })
    },
    // 上传时的钩子方法，不允许点击上传组件
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 上传成功后的钩子方法
    handleFileSuccess(response, file, fileList) {
      // 放在第一行，否则下次点击该上传组件失效
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code === 401) {
        return handleAuthorized()
      } else if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      // 该上传api处理excel导入错误不会返回错误码，是将错误信息放在返回对象中体现，so需要手动判断
      if (response.data.failureEquipments || response.data.checkErrorEquipments) {
        this.$message.error(this.$t('supplier.failedToUploadTheFilePleaseCheckTheFileFormat'))
        this.uploadVisible = false
      } else {
        this.supplierInfo.equipmentCreateReqVO = response.data.equipmentRespVO
        this.$message.success(this.$t('common.uploadSucceeded'))
        this.uploadVisible = false
        this.reloadDocRepository()
      }
    },
    reloadDocRepository() {
      this.docRepositoryRef = false
      this.$nextTick(() => {
        this.docRepositoryRef = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>

.form-title {
  border-left: 6px solid #376092;
  margin: 10px 0;
  padding: 10px 30px;
  font-size: 16px;
  font-weight: bold;
  background-color: #f1f1f1;
}
</style>
