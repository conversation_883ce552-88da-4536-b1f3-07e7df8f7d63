<template>
  <common-card
    :title="$t('sp.specialIndicatorResults')"
  >
    <div class="common-body">

      <span>{{ $t('sp.tipWhenPerformingMonthlyPerformanceCalculations') }}</span>
      <el-descriptions :colon="false" :column="1" label-class-name="special" style="margin-top: 10px">
        <el-descriptions-item v-for="item in specialList" :label="item.name">
          <el-button
            v-has-permi="['sp:pi:export']"
            icon="el-icon-upload2"
            style="margin-right: 50px"
            type="primary"
            @click="handleImport(item)"
          >{{ $t('supplier.batchImport') }}
          </el-button>
          （
          {{ formatCompanyName(item.companyIds) }}
          ）
        </el-descriptions-item>
      </el-descriptions>
      <div>
        <el-popover
          class="warning-popover"
          placement="top-start"
          trigger="hover"
        >
          <div>
            <div>
              <i class="el-icon-warning" style="color:#ff8d1a;font-size: 16px " />
              <span>{{ $t('sp.importantReminder') }}</span>
            </div>
            <div v-if="source==='manually'" style="margin: 10px 0">{{ $t('sp.tipPleaseUploadManuallyRunMonth') }}</div>
            <div v-if="source==='automatic'" style="margin: 10px 0;">
              <div style="float: left">
                {{ $t('supplier.tips') }}：
              </div>
              <div style="float: left">
                <div>{{ $t('sp.PleaseUploadManuallyRunMonthRelatedData') }}</div>
                <div>{{ $t('sp.PleasePayAttentionToSelectingOneOrMoreCorrespondingCompaniesBeforeUploading') }}</div>
              </div>

            </div>

          </div>

          <span slot="reference">
            <i class="el-icon-warning" style="color:#ff8d1a;font-size: 16px " />
            <span style="margin-left: 3px">{{ $t('sp.importantReminder') }}</span>
          </span>
        </el-popover>

        <el-button
          v-has-permi="['sp:upload-file-record:query']"
          style="margin-left: 250px;text-decoration: underline"
          type="text"
          @click="showHistory"
        >{{ $t('sp.historicalUploadRecords') }}
        </el-button>
      </div>
    </div>
    <el-dialog
      v-if="upload.open"
      :title="upload.title"
      :visible.sync="upload.open"
      append-to-body
      width="400px"
    >
      <el-upload
        ref="upload"
        :action="upload.url"
        :disabled="upload.isUploading"
        :headers="upload.headers"
        :limit="1"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        accept=".xlsx, .xls"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        <div slot="tip" class="el-upload__tip text-center">
          <span>{{ $t('common.onlyXlsXlsxFormatFilesAreAllowedToBeImported') }}</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="upload.open = false"
        >{{ $t('common.cancel') }}
        </el-button>
        <el-button
          type="primary"
          plain
          @click="importTemplate"
        > {{ $t('common.downloadTemplate') }}
        </el-button>

        <el-button
          type="primary"
          @click="submitFileForm"
        > {{ $t('common.confirm') }}
        </el-button>

      </div>
    </el-dialog>
    <el-dialog
      v-if="historyVisible"
      :visible.sync="historyVisible"
      :title="$t('sp.originalFileUploadRecord')"
      width="900px"
    >

      <el-table
        :data="uploadRecords"
      >
        <el-table-column :label="$t('rfq.fileName')" prop="fileName">
          <template #default="scope">
            <el-button
              type="text"
              @click="openUrl(scope.row.url)"
            >{{ scope.row.fileName }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="$t('sp.indicatorName')" prop="shortName" width="140px" />
        <el-table-column :label="$t('sp.executionStatus')" width="80px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SP_UPLOAD_PROCESS_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('sp.uploader')" width="80px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="scope.row.creator" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('sp.uploadTime')" prop="createTime" width="150px">
          <template #default="scope">
            {{ dayjs(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('avpl.source')" width="110px">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SP_UPLOAD_FILE_SOURCE" :value="scope.row.source" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.operate')" width="80px">
          <template #default="scope">
            <el-button
              v-if="scope.row.status === 'processed'"
              type="text"
              @click="deleteUploadFileData(scope.row.id)"
            >{{ $t('sp.deleteData') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getRecord"
      />
    </el-dialog>
  </common-card>

</template>

<script>
import { getBaseHeader } from '@/utils/request'
import { getConfig } from '@/utils/config'
import {
  deleteUploadFileDataByRecordId,
  exportPi,
  specialPiListByYear,
  uploadFileRecord,
  uploadFileRecordPage
} from '@/api/performance'
import { DICT_TYPE } from '@/utils/dict'
import dayjs from 'dayjs'

export default {
  name: 'Runhead',
  props: ['source', 'year'],
  data() {
    return {
      dayjs,
      specialList: [],
      upload: {
        open: false,
        title: '',
        isUploading: false,
        headers: getBaseHeader(),
        url: ''
      },
      importData: {},
      historyVisible: false,
      uploadRecords: [],
      queryParams: {
        pageNo: 1,
        pageSize: 10
      },
      companyId: null,
      total: 0,
      file: {}
    }
  },

  mounted() {
    this.getSpecial()
  },
  methods: {
    openUrl(url) {
      window.open(url)
    },
    formatCompanyName(ids) {
      return this.getDictDatas2(DICT_TYPE.COMMON_COMPANY, ids).map(a => a.name).join('，')
    },
    getSpecial() {
      specialPiListByYear({
        year: this.year
      }).then(res => {
        this.specialList = res.data
      })
    },
    handleImport(item) {
      this.upload.title = `${item.name}` + this.$t('supplier.batchImport')
      this.upload.open = true
      this.upload.url = getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload'
      this.importData = item
      this.companyId = item.companyIds[0]
    },

    importTemplate() {
      exportPi({
        piId: this.importData.piId,
        year: this.year,
        companyId: this.companyId
      }).then(response => {
        this.$download.excel(response, `${this.importData.name}.xlsx`)
      })
    },
    showHistory() {
      this.historyVisible = true
      this.getRecord()
    },
    getRecord() {
      uploadFileRecordPage(this.queryParams).then(res => {
        this.uploadRecords = res.data.list
        this.total = res.data.total
      })
    },
    handleFileUploadProgress() {
    },
    handleFileSuccess(file, fileList) {
      this.file = file.data
    },
    submitFileForm() {
      if (!this.file.name) {
        this.$message.error(this.$t('sp.pleaseUploadTheFileFirst'))
        return
      }
      uploadFileRecord({
        companies: this.importData.companyIds.join(','),
        fileId: this.file.id,
        fileName: this.file.name,
        piId: this.importData.piId,
        shortName: this.importData.shortName,
        source: this.source,
        url: this.file.url
      }).then(res => {
        this.$message.success(this.$t('common.uploadSucceeded'))
        this.upload.open = false
      })
    },
    deleteUploadFileData(id) {
      deleteUploadFileDataByRecordId({ id: id }).then(res => {
        this.$message.success(this.$t('sp.dataDeletionSuccessful'))
        this.getRecord()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.common-body {
  padding: 0 100px;
}

::v-deep .special {
  width: 250px;
  color: #4996b8;
  font-weight: bold;
  display: flex;
  align-items: center;

}

::v-deep .el-descriptions-item__container {
  margin: 10px 0;
}
</style>
