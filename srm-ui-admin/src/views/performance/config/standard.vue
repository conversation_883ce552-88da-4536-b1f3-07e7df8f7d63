<template>
  <div class="standard">
    <configHead
      :edit-mode.sync="editMode"
      @getScoreTable="init"
    />
    <common-card
      :title="$t('sp.performanceScoringStandardSetting')"
    >
      <div style="text-align: right">
        <span>{{ $t('sp.theHighestScoringStandard') }}</span>
        <el-select
          v-model="queryParams.scoreStandardLevel"
          :disabled="!editMode"
          style="margin: 0 5px;width: 65px"
        >
          <el-option
            v-for="a in 10"
            :key="a"
            :value="a"
            :label="a"
          />
        </el-select>
        <span>{{ $t('sp.level') }}</span>
      </div>
      <el-table
        border
        :data="list"
        style="margin-top: 15px"
        :header-cell-style="headerStyle"
        :cell-style="numStyle"
      >

        <el-table-column :label="$t('sp.indicatorName')" prop="name" width="100px">
          <template #default="scope">
            <el-button
              v-if="scope.row.uploadGradingCriteria"
              type="text"
              style="text-decoration: underline"
              @click="showPaymentCycle"
            >{{ scope.row.name }}</el-button>
            <span v-else>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('sp.indicatorType')" prop="target" width="100px">
          <template #default="scope">
            <dict-tag :value="scope.row.piCategory" :type="DICT_TYPE.SP_PI_CATEGORY" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('sp.target')" prop="target" width="100px" />
        <el-table-column :label="$t('sp.unit')" prop="unit" width="100px" />
        <el-table-column :label="$t('sp.supplementaryRules')" align="center" width="130px">
          <el-table-column width="130px" :label="$t('sp.scoreForNoEventsOccurred')">
            <template #default="scope">
              <el-select v-if="!(scope.row.piCategory==='quality' && scope.row.unitType==='number')" v-model="scope.row.additionalRules" :disabled="!editMode">
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.SP_NO_EVENT_SCORE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          align="center"
          :label="$t('sp.ruleOne')"
        >
          <el-table-column :label="$t('sp.usingIndicatorValues')" align="center" width="100px">
            <template #default="scope">
              <el-checkbox
                :disabled="scope.row.unitType==='number' || !editMode"
                :value="scope.row.scoreStandardType ==='pi_value'"
                @change="(val)=>scoreStandardType(val,scope.row,'pi_value')"
              />
            </template>
          </el-table-column>

        </el-table-column>
        <el-table-column
          align="center"
          :label="$t('sp.rule')"
        >
          <el-table-column
            align="center"
            :label="$t('sp.deductionPointsForUsageCount')"
            width="110px"
          >
            <template #default="scope">
              <el-checkbox
                v-if="scope.row.unitType==='number'"
                :disabled="!editMode"
                :value="'deduction_points'===scope.row.scoreStandardType"
                @change="(val)=>scoreStandardType(val,scope.row,'deduction_points')"
              />
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            :label="$t('sp.useVetoItems')"
            width="100px"
          >
            <template #default="scope">
              <el-checkbox
                v-if="scope.row.scoreStandardType ==='deduction_points'"
                v-model="scope.row.veto"
                :disabled="!editMode"
                @change="(val)=>changeVeto(scope.row.piId,scope.row.name,val)"
              />
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column
          align="center"
          :label="$t('sp.ruleThree')"
        >
          <template v-for="item in queryParams.scoreStandardLevel">
            <el-table-column
              width="100px"
              align="center"
              :label="`${$t('sp.scoringCriteriaNo')}${item}${$t('sp.level')}`"
              :prop="`opSymbol${item}`"
            >
              <template #default="scope">
                <el-select
                  v-model="scope.row[`opSymbol${item}`]"
                  :disabled="![null,'nothing'].includes(scope.row.scoreStandardType) ||!editMode"
                >
                  <el-option
                    v-for="item in getDictDatas(DICT_TYPE.SP_OPERATOR_SYMBOL)"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              width="110px"
              align="center"
              :label="`${$t('sp.scoringCriteriaNo')}${item}${$t('sp.level')}`"
              :prop="`opValue${item}`"
            >
              <template #default="scope">
                <vxe-input
                  v-model.number="scope.row[`opValue${item}`]"
                  type="number"
                  class="numberClass"
                  max="999"
                  :disabled="![null,'nothing'].includes(scope.row.scoreStandardType)||!editMode"
                />
              </template>
            </el-table-column>
            <el-table-column
              width="110px"
              align="center"
              :label="`${$t('sp.scoringCriteriaNo')}${item}${$t('sp.level')}`"
              :prop="`score${item}`"
            >
              <template #default="scope">
                <vxe-input
                  v-model.number="scope.row[`score${item}`]"
                  type="number"
                  max="999"
                  class="numberClass"
                  :disabled="![null,'nothing'].includes(scope.row.scoreStandardType) ||!editMode"
                />
              </template>
            </el-table-column>

          </template>

        </el-table-column>

      </el-table>

      <div
        v-if="pointsDeductedConfig.length"
        class="card"
      >
        {{ $t('sp.settingOfDeductionStandardsForCountingPoints') }}
      </div>
      <el-form label-width="150px">
        <el-form-item
          v-for="item in pointsDeductedConfig"
          :label="item.name"
        >
          <span>{{ $t('sp.theResultOfThisIndicatorIs') }}</span>

          <vxe-input
            v-model.number="item.piResult"
            type="number"
            :disabled="!editMode"
            min="0"
            class="scoreNum"
          />
          <span>{{ $t('sp.nextTimeTheMaximumScoreIs') }}</span>

          <vxe-input
            v-model.number="item.fullMarks"
            type="number"
            :disabled="!editMode"
            min="0"
            class="scoreNum"
          />
          <span>{{ $t('sp.Appears') }}</span>

          <vxe-input
            v-model.number="item.frequency"
            type="integer"
            min="0"
            :disabled="!editMode"
            class="scoreNum"
          />
          <span>{{ $t('sp.secondaryDeduction') }}</span>

          <vxe-input
            v-model.number="item.deductPoints	"
            type="number"
            :disabled="!editMode"
            min="0"
            class="scoreNum"
          />
          <span>{{ $t('sp.theMaximumDeductionForThisItemIs') }}</span>

          <vxe-input
            v-model.number="item.bottomScore"
            type="number"
            :disabled="!editMode"
            min="0"
            class="scoreNum"
          />
          <span>{{ $t('sp.minute') }}</span>

        </el-form-item>
      </el-form>
      <div
        v-if="voteCreate.length"
        class="card"
      >
        {{ $t('sp.settingOfVetoCriteria') }}
      </div>
      <el-form label-width="150px">
        <el-form-item
          v-for="item in voteCreate"
          :label="item.name"
        >
          <div>
            {{ $t('sp.currentOccurrence') }}
            <vxe-input v-model.number="item.frequency" :disabled="!editMode" min="0" type="number" class="scoreNum" />
            {{ $t('sl.second') }}
            <el-select
              v-model="item.influenceSphere	"
              :disabled="!editMode"
              style="width: 100px"
            >
              <el-option
                v-for="item in getDictDatas(DICT_TYPE.SP_INFLUENCE_SPHERE)"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            {{ $t('sp.directlyDeductToPoints') }}

          </div>

        </el-form-item>
      </el-form>

    </common-card>
    <common-card
      :title="$t('sp.performanceLevelStandardSetting')"
    >
      <el-button
        v-if="editMode"
        type="primary"
        @click="newGrade"
      >{{ $t('sp.increaseLevel') }}</el-button>
      <el-table
        border
        style="margin-top: 15px"
        :header-cell-style="headerStyleGrade"
        :data="gradeList"
      >
        <el-table-column :label="$t('sp.performanceLevel')" prop="gradeName" width="200">
          <template #default="scope">
            <el-select
              v-model="scope.row.gradeName"
              :disabled="!editMode"
            >
              <el-option
                v-for="item in getDictDatas(DICT_TYPE.SP_GRADE)"
                :key="item.id"
                :label=" item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column :label="$t('sp.settingStandards')" align="center" prop="opSymbol" width="200">
          <template #default="scope">
            <el-select
              v-model="scope.row.opSymbol"
              :disabled="!editMode"
            >
              <el-option
                v-for="item in getDictDatas2(DICT_TYPE.SP_OPERATOR_SYMBOL,'greater_equal')"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column :label="$t('sp.settingStandards')" align="center" prop="opValue" width="120">
          <template #default="scope">
            <vxe-input
              v-model.number="scope.row.opValue"
              type="number"
              class="numberClass"
              :disabled="!editMode"
            />

          </template>
        </el-table-column>
        <el-table-column :label="$t('material.colour')" prop="color" width="100">
          <template #default="scope">
            <el-color-picker
              v-model="scope.row.color"
              :disabled="!editMode"
              size="mini"
            />

          </template>
        </el-table-column>
        <el-table-column :label="$t('order.describe')" prop="gradeDescribe">
          <template #default="scope">
            <el-input
              v-model="scope.row.gradeDescribe"
              :disabled="!editMode"
            />
          </template>
        </el-table-column>>
        <el-table-column :label="$t('common.operate')" prop="gradeDescribe" width="65">
          <template #default="scope">
            <el-button
              v-if="editMode"
              type="text"
              @click="delGrade(scope.row.id,scope.$index)"
            >
              {{ $t('common.del') }}
            </el-button>
          </template>
        </el-table-column>>
      </el-table>
    </common-card>
    <div style="margin-bottom: 40px;display: flex; justify-content: flex-end;">
      <div style="position: fixed;bottom: 20px;z-index: 999">
        <el-button v-if="editMode" style="width: 200px;margin-right: 20px" type="primary" plain @click="cancel">{{ $t('scar.cancel') }}</el-button>
        <el-button v-if="editMode" v-has-permi="['sp:score-config:save']" style="width: 200px;margin-right: 20px" type="primary" @click="saveAll">{{ $t('scar.preservation') }}</el-button>
        <navigationButton v-if="!editMode" @goToPreviousStep="goToPreviousStep" @goToNextStep="goToNextStep" />
      </div>
    </div>
    <el-dialog
      v-if="paymentCycleVisible"
      :visible.sync="paymentCycleVisible"
      :title="$t('sp.paymentCycleScoreSetting')"
    >
      <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
        <el-input
          v-model="queryParams.code	"
          style="flex: 0 1 40%"
          :placeholder="$t('sp.pleaseEnterPaymentCycleCodeAndPaymentCycleName')"
          clearable
          @keyup.enter.native="handleSearch(true)"
        />
        <el-button
          type="primary"
          @click="handleSearch(true)"
        >{{ $t('common.search') }}</el-button>

      </div>
      <div>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-upload2"
          @click="handleImport"
        >  {{ $t('supplier.batchImport') }}</el-button>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-upload2"
          @click="download()"
        >  {{ $t('common.batchExport') }}</el-button>
      </div>
      <el-table :data="paymentCycleList" style="margin-top: 10px;margin-bottom: 30px">
        <el-table-column :label="$t('sp.paymentCycleCode')" prop="code" />
        <el-table-column :label="$t('sp.paymentCycleName')" prop="name">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SUPPLIER_PAYMENT_DAYS" :value="scope.row.code" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('sp.scorepercentageSystem')" prop="score">
          <template #default="scope">
            <vxe-input
              v-model.number="scope.row.score"
              type="number"
              min="0"
              max="100"
            />
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary" @click="savePaymentCycle">{{ $t('common.save') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        <div slot="tip" class="el-upload__tip text-center">
          <span>{{ $t('common.onlyXlsXlsxFormatFilesAreAllowedToBeImported') }}</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-has-permi="['order:header:query']"
          @click="upload.open = false"
        >{{ $t('common.cancel') }}</el-button>
        <el-button
          v-hasPermi="['order:good-receiving:import']"
          type="primary"
          plain
          @click="importTemplate"
        >  {{ $t('common.downloadTemplate') }}</el-button>
        <el-button
          v-has-permi="['order:header:query']"
          type="primary"
          @click="submitFileForm"
        >  {{ $t('common.confirm') }}</el-button>

      </div>
    </el-dialog>

  </div>
</template>

<script>
import configHead from '@/views/performance/components/configHead.vue'
import {
  deleteGradeConfig,
  getGradeConfig, getPaymentCycle, getPaymentCycleExcel,
  getPointsDeductedConfig,
  getScoreConfig, getVetoConfig, savePaymentCycle,
  saveScoreConfig
} from '@/api/performance'
import { getBaseHeader } from '@/utils/request'
import { getConfig } from '@/utils/config'
import { zeroIsTrue } from '@/utils/validate'
import { getDictDatas, getDictDatas2 } from '@/utils/dict'
import navigationButton from '@/views/performance/components/navigationButton.vue'
export default {
  name: 'Standard',
  components: {
    navigationButton,
    configHead
  },
  data() {
    return {
      upload: {
        open: false,
        title: '',
        isUploading: false,
        headers: getBaseHeader(),
        url: ''
      },

      editMode: false,
      topLevel: 5,
      queryParams: {
        baseConfigId: null,
        scoreStandardLevel: 5,
        code: ''
      },
      total: 0,
      list: [],
      pointsDeductedConfig: [],
      voteCreate: [],
      gradeList: [],
      paymentCycleVisible: false,
      paymentCycleList: []
    }
  },
  methods: {
    // 上一步
    goToPreviousStep() {
      this.$router.push('/system/sp_config/participation')
    },
    // 下一步
    goToNextStep() {
      this.$router.push('/system/sp_config/category')
    },
    getDictDatas,
    getDictDatas2,
    init(id, scoreStandardLevel) {
      if (scoreStandardLevel) {
        this.queryParams.scoreStandardLevel = scoreStandardLevel
      } else {
        this.queryParams.scoreStandardLevel = 5
      }
      this.getTable(id)
      this.getGrade()
      this.getPointsDeducted()
      this.getVeto()
    },
    getTable(id) {
      if (id) {
        this.queryParams.baseConfigId = id
      }
      this.loading = true
      getScoreConfig(this.queryParams).then(res => {
        this.loading = false
        res.data.forEach(a => {
          if (a.scoreConfigGroups.length) {
            this.queryParams.scoreStandardLevel = a.scoreConfigGroups.length
            a.scoreConfigGroups.forEach(score => {
              a[`opSymbol${score.level}`] = score.opSymbol
              a[`opValue${score.level}`] = score.opValue
              a[`score${score.level}`] = score.score
            })
          } else {
            for (const level in this.queryParams.scoreStandardLevel) {
              a[`opSymbol${level}`] = ''
              a[`opValue${level}`] = null
              a[`score${level}`] = null
            }
          }
        })
        this.list = res.data
      })
    },
    getGrade() {
      getGradeConfig(this.queryParams).then(res => {
        this.gradeList = res.data
      })
    },
    getPointsDeducted() {
      getPointsDeductedConfig(this.queryParams).then(res => {
        this.pointsDeductedConfig = res.data
      })
    },
    getVeto() {
      getVetoConfig(this.queryParams).then(res => {
        this.voteCreate = res.data
      })
    },
    headerStyle({ row, column, rowIndex, columnIndex }) {
      console.log(row)
      console.log(rowIndex)
      console.log(column)
      for (let i = 0; i < row.length; i++) {
        if (i > 3 && rowIndex === 1) {
          const con = (i - 3) % 3
          if (con === 1) {
            row[i].colSpan = 3
          } else if (con === 2) {
            row[i].colSpan = 0
          } else if (con === 0) {
            row[i].colSpan = 0
          }
        }
      }
      if (columnIndex > 3 && rowIndex === 1 &&
          ((columnIndex - 3) % 3 === 2 || (columnIndex - 3) % 3 === 0)
      ) {
        return 'display: none'
      }
    },
    numStyle({ columnIndex, rowIndex }) {
      if (rowIndex < 7 && columnIndex > 7 && (columnIndex - 7) % 3 === 0
      ) {
        return 'background:#a2ddf5;'
      }
    },
    scoreStandardType(val, k, v) {
      if (val) {
        k.scoreStandardType = v
      } else {
        k.scoreStandardType = null
      }
      this.initSpecialScoring(k.piId, k.name, v, val)
    },
    initSpecialScoring(id, name, value, check) {
      if (value === 'deduction_points') {
        if (!this.pointsDeductedConfig.find(a => a.piId === id) && check) {
          this.pointsDeductedConfig.push({
            name: name,
            baseConfigId: this.queryParams.baseConfigId,
            bottomScore: null,
            deductPoints: null,
            frequency: null,
            fullMarks: null,
            piId: id,
            piResult: null
          })
        } else {
          this.pointsDeductedConfig = this.pointsDeductedConfig.filter(a => a.piId !== id)
        }
        // this.voteCreate = this.voteCreate.filter(a => a.piId !== id)
      } else if (value === 'pi_value' && check) {
        this.pointsDeductedConfig = this.pointsDeductedConfig.filter(a => a.piId !== id)
      }
    },
    changeVeto(id, name, check) {
      if (!this.voteCreate.find(a => a.piId === id) && check) {
        this.voteCreate.push({
          name: name,
          baseConfigId: this.queryParams.baseConfigId,
          frequency: null,
          influenceSphere: null,
          piId: id
        })
      } else {
        this.voteCreate = this.voteCreate.filter(a => a.piId !== id)
      }
    },
    saveAll() {
      // 是否通过
      let pass = true
      const levelArray = []
      let lineNo = 0
      let hasExits = false
      // 绩效评分标准设定对象
      const scoreConfigRepVOs = this.list.map(a => {
        hasExits = false
        lineNo++
        const checkObj = {
          count: lineNo,
          set: new Set()
        }
        if (a.piCategory === 'quality' && a.unitType !== 'number' && !a.additionalRules) {
          pass = false
          checkObj.set.add(0)
          levelArray.push(checkObj)
          hasExits = true
        }
        const scoreConfigGroups = []
        if ((!a.scoreStandardType || a.scoreStandardType === 'nothing')) {
          let pre = 0
          for (let level = 1; level < this.queryParams.scoreStandardLevel + 1; level++) {
            if (level === 1 && (!a[`opSymbol${level}`] || !zeroIsTrue(a[`opValue${level}`]) || !zeroIsTrue(a[`score${level}`]))) {
              pass = false
              checkObj.set.add(level)
              pre = 0
              break
            }
            if (!a[`opSymbol${level}`] && !zeroIsTrue(a[`opValue${level}`]) && !zeroIsTrue(a[`score${level}`])) {
              // 检查是否是连续的元素
              if (level - pre !== 1) {
                pass = false
                pre = 0
                break
              }
              pre = level
            } else if (!a[`opSymbol${level}`] || !zeroIsTrue(a[`opValue${level}`]) || !zeroIsTrue(a[`score${level}`])) {
              pass = false
              checkObj.set.add(level)
              pre = 0
              break
            }
            pre = level

            scoreConfigGroups.push({
              level,
              opSymbol: a[`opSymbol${level}`] ?? '',
              opValue: a[`opValue${level}`] ?? null,
              score: a[`score${level}`] ?? null
            })
          }
          if (!hasExits && checkObj.set.size > 0) {
            levelArray.push(checkObj)
          }
        }

        return {
          piConfigId: a.piConfigId,
          baseConfigId: a.baseConfigId,
          piId: a.piId,
          veto: a.veto,
          scoreStandardType: a.scoreStandardType || 'nothing', // 什么都不填也要传，默认为评分区间
          additionalRules: a.additionalRules,
          scoreConfigGroups
        }
      })

      if (!pass) {
        console.log(levelArray)
        const result = levelArray.map(j => {
          return `${this.$t('sp.section')} ${j.count} ${this.$t('sp.line')}: ${[...j.set].map(val => val === 0 ? this.$t('sp.pleaseFillInTheSupplementaryRules') : this.$t('sp.pleaseFillInRuleWhichCannotBeInterrupted')).join(',')}`
        }).join(';')

        this.$message.error(this.$t('tender.scoringCriteria') + result)
        return
      }

      if (new Set(this.gradeList.map(j => j.gradeName)).size < this.gradeList.length) {
        this.$message.error(this.$t('sp.discoveringTheSamePerformanceLevelSavingFailed'))
        return
      }

      saveScoreConfig({
        baseConfigId: this.queryParams.baseConfigId,
        scoreStandardLevel: this.queryParams.scoreStandardLevel,
        gradeConfigCreateReqVOs: this.gradeList,
        scoreConfigRepVOs,
        pointsDeductedConfigCreateReqVOs: this.pointsDeductedConfig,
        voteCreateReqVOs: this.voteCreate
      }).then(res => {
        this.init(this.queryParams.baseConfigId, this.queryParams.scoreStandardLevel)
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.cancel()
      })
    },
    cancel() {
      this.editMode = !this.editMode
      this.init(this.queryParams.baseConfigId, this.queryParams.scoreStandardLevel)
    },
    headerStyleGrade({ row, column, rowIndex, columnIndex }) {
      row[1].colSpan = 2
      row[2].colSpan = 0
      if (columnIndex === 2) {
        return 'display: none'
      }
    },
    newGrade() {
      if (this.gradeList.length >= this.getDictDatas(this.DICT_TYPE.SP_GRADE).length) {
        this.$message.error(this.$t('sp.theNumberOfPerformanceLevelsHasExceeded'))
        return
      }
      this.gradeList.push({
        baseConfigId: this.queryParams.baseConfigId,
        color: '',
        gradeDescribe: '',
        gradeName: '',
        opSymbol: '',
        opValue: null
      })
    },

    delGrade(id, index) {
      if (id) {
        deleteGradeConfig({
          id
        }).then(res => {
          this.gradeList.splice(index, 1)
        })
      } else {
        this.gradeList.splice(index, 1)
      }
    },
    showPaymentCycle() {
      this.paymentCycleVisible = true
      this.getPaymentCycleList()
    },
    handleSearch(bool1) {
      this.getPaymentCycleList()
    },
    handleImport(str1) {
      this.upload.title = this.$t('sp.paymentCycleImport')
      this.upload.open = true
      this.upload.url = getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + `/admin-api/sp/payment-cycle-config/import?baseConfigId=${this.queryParams.baseConfigId}`
    },
    download() {
      getPaymentCycleExcel(this.queryParams).then(response => {
        this.$download.excel(response, this.$t('sp.exportPaymentCycleXlsx'))
      })
    },
    getPaymentCycleList() {
      getPaymentCycle(this.queryParams).then(res => {
        this.paymentCycleList = res.data
        this.paymentCycleList.forEach(j => {
          if (!j.baseConfigId) {
            j.baseConfigId = this.queryParams.baseConfigId
          }
        })
      })
    },
    savePaymentCycle() {
      savePaymentCycle(this.paymentCycleList).then(res => {
        this.$message.success(this.$t('common.savedSuccessfully'))
      })
    },
    handleFileUploadProgress() {
      this.upload.isUploading = true
    },
    handleFileSuccess(response) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      const data = response.data
      let text = ''
      if (data.createDetail) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createDetail.length
      }
      if (data.failureDetail) {
        text += '<br />' + this.$t('common.numberOfCreationFailures') + '：' + Object.keys(data.failureDetail).length
      }
      if (data.filePath) {
        text += '<br />' + this.$t('common.numberOfValidationFailures')
        text += '<br /><a class="el-button--text" target="_blank" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }

      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getPaymentCycleList()
    },
    submitFileForm() {
      this.$refs.upload.submit()
    },
    importTemplate() {
      getPaymentCycleExcel({ baseConfigId: this.queryParams.baseConfigId }).then(response => {
        this.$download.excel(response, this.$t('sp.paymentCycleImportTemplateXlsx'))
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.standard{
  padding: 15px 20px;
}
.card{
  margin: 15px 0;
  color: #4996b8;
  font-size: 16px;
  font-weight: 700;
}
.scoreNum{
  width: 60px;
  margin: 0 4px;
}

.numberClass{
  width: 80px;
}
</style>
