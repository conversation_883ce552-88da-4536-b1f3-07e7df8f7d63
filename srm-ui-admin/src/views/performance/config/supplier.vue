<template>
  <div class="supplier">
    <configHead
      ref="configHeadComponent"
      :edit-mode.sync="editMode"
      @getTable="getTable"
    />
    <common-card
      :title="$t('sp.participatingSuppliersAndTheirWeights')"
    >
      <div style="display: flex;justify-content: center">
        <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="130px" size="small">
          <el-form-item :label="$t('material.category')" prop="code">
            <cascading-category
              :max-level="queryParams.categoryLevel"
              :original-value.sync="queryParams.categoryIds"
            />
          </el-form-item>
          <el-form-item :label="$t('sl.categoryPositioning')" prop="name">
            <el-select
              v-model="queryParams.categoryPosition"
              :placeholder="$t('common.pleaseSelect')"
              clearable
              multiple
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('sp.participateIn')">
            <el-select v-model="queryParams.involved" :placeholder="$t('common.pleaseSelect')" clearable>
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SP_INVOLVED)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <div>
            <el-form-item :label="$t('supplier.supplierName')">
              <el-input v-model="queryParams.supplierName" style="width: 194px" />
            </el-form-item>
            <el-form-item>
              <!--              <el-select v-model="queryParams.level" :placeholder="$t('common.pleaseSelect')" clearable>-->
              <!--                <el-option-->
              <!--                  v-for="dict in getDictDatas(DICT_TYPE.SP_INVOLVED)"-->
              <!--                  :key="dict.value"-->
              <!--                  :label="dict.label"-->
              <!--                  :value="dict.value"-->
              <!--                />-->
              <!--              </el-select>-->
            </el-form-item>
            <el-form-item label=" ">
              <el-button icon="el-icon-search" type="primary" @click="queryParams.pageNo = 1;getTable();">{{ $t('common.search') }}</el-button>
              <el-button icon="el-icon-refresh" @click="reset()">{{ $t('common.reset') }}</el-button>
            </el-form-item>
          </div>

        </el-form>

      </div>
      <div style="display: flex;justify-content: space-between;margin-bottom: 15px">
        <div>
          <el-button
            v-if="editMode"
            icon="el-icon-upload2"
            size="mini"
            type="primary"
            @click="handleImport"
          > {{ $t('supplier.batchImport') }}
          </el-button>
          <el-button
            icon="el-icon-upload2"
            size="mini"
            type="primary"
            @click="download()"
          > {{ $t('common.batchExport') }}
          </el-button>
        </div>
        <div>
          <el-button v-if="editMode" type="primary" @click="participateEdit(true)">{{ $t('sp.participateIn') }}</el-button>
          <el-button v-if="editMode" type="primary" @click="participateEdit(false)">{{ $t('sp.notParticipating') }}</el-button>
        </div>
      </div>
      <el-table ref="list" v-loading="loading" :data="list">
        <el-table-column
          v-if="editMode"
          type="selection"
          width="30"
        />
        <el-table-column :label="$t('supplier.supplierCode')" prop="supplierCode" />
        <el-table-column :label="$t('supplier.supplierName')" prop="supplierName" />
        <el-table-column :label="$t('sp.participateIn')" prop="involved">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SP_INVOLVED" :value="scope.row.involved" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('material.category')">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="scope.row.categoryId" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('sl.categoryPositioning')" prop="">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY" :value="scope.row.categoryPosition" />
          </template>
        </el-table-column>

      </el-table>
      <pagination
        v-show="total > 0"
        :limit.sync="queryParams.pageSize"
        :page.sync="queryParams.pageNo"
        :total="total"
        @pagination="getTable()"
      />
    </common-card>
    <div style="margin-bottom: 40px;display: flex; justify-content: flex-end;">
      <div style="position: fixed;bottom: 20px;z-index: 999">
        <!--      XUERES-1501  绩效评估供应商，编辑中按钮名字容易误解-->
        <el-button v-if="editMode" style="width: 200px;margin-right: 20px" type="primary" plain @click="cancel">{{ $t('sp.closeEditing') }}
        </el-button>
        <navigationButton v-if="!editMode" @goToPreviousStep="goToPreviousStep" @goToNextStep="goToNextStep" />
      </div>
    </div>
    <el-dialog :title="upload.title" :visible.sync="upload.open" append-to-body width="400px">
      <el-upload
        ref="upload"
        :action="upload.url"
        :auto-upload="false"
        :disabled="upload.isUploading"
        :headers="upload.headers"
        :limit="1"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :before-upload="preUploadCheck"
        accept=".xlsx, .xls"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        <div slot="tip" class="el-upload__tip text-center">
          <span>{{ $t('common.onlyXlsXlsxFormatFilesAreAllowedToBeImported') }}</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-has-permi="['order:header:query']"
          @click="upload.open = false"
        >{{ $t('common.cancel') }}
        </el-button>
        <el-button
          v-has-permi="['order:header:query']"
          type="primary"
          @click="submitFileForm"
        > {{ $t('common.confirm') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import configHead from '@/views/performance/components/configHead.vue'
import { getBaseHeader } from '@/utils/request'
import { getConfig } from '@/utils/config'
import {
  enabledSupplierConfig,
  getSupplierConfig,
  getSupplierConfigExcel,
  getSupplierConfigExcelTemplate,
  getCountOfCompanyYear
} from '@/api/performance'
import NavigationButton from '@/views/performance/components/navigationButton.vue'

export default {
  name: 'Supplier',
  components: {
    NavigationButton,
    configHead
  },
  data() {
    return {
      upload: {
        open: false,
        title: '',
        isUploading: false,
        headers: getBaseHeader(),
        url: 'supplier-config/import'
      },

      editMode: false,
      queryParams: {
        baseConfigId: 0,
        categoryIds: [],
        categoryPosition: [],
        involved: '',
        pageSize: 20,
        pageNo: 1,
        supplierName: '',
        level: '',
        categoryLevel: undefined
      },
      list: [],
      total: 0,
      loading: false

    }
  },
  methods: {
    // 上一步
    goToPreviousStep() {
      this.$router.push('/system/sp_config/category')
    },
    // 下一步
    goToNextStep() {
      this.$router.push('/system/sp_config/auto')
    },
    init() {
    },
    reset() {
      this.queryParams = {
        ...this.queryParams,
        categoryIds: [],
        categoryPosition: [],
        involved: '',
        pageSize: 20,
        pageNo: 1,
        supplierName: ''
      }
      this.getTable()
    },

    getTable(id, categoryLevel) {
      if (id) {
        this.queryParams.baseConfigId = id
      }
      if (categoryLevel) {
        this.queryParams.categoryLevel = categoryLevel
      }
      this.loading = true
      getSupplierConfig(this.queryParams).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    download(str1) {
      getSupplierConfigExcel(this.queryParams).then(response => {
        this.$download.excel(response, this.$t('sp.suppliersParticipatingInPerformanceXlsx'))
      })
    },
    handleFileUploadProgress() {
      this.upload.isUploading = true
    },
    handleFileSuccess(response) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      const data = response.data
      let text = ''
      if (data.createDetail) {
        text += this.$t('common.successfullyCreatedQuantity') + '：' + data.createDetail.length
      }
      if (data.failureDetail) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + Object.keys(data.failureDetail).length
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" target="_blank" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }

      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.getTable()
    },
    submitFileForm() {
      this.$refs.upload.submit()
    },
    importTemplate() {
      getSupplierConfigExcelTemplate().then(response => {
        this.$download.excel(response, this.$t('sp.supplierImportTemplateForPerformanceParticipationXlsx'))
      })
    },
    participateEdit(involved) {
      enabledSupplierConfig({
        dataList: this.$refs.list.selection,
        involved,
        baseConfigId: this.queryParams.baseConfigId
      }).then(res => {
        this.getTable()
        this.$message.success(this.$t('common.savedSuccessfully'))
        this.updatePiCount()
      })
    },
    updatePiCount() {
      setTimeout(() => {
        this.$refs.configHeadComponent.initHead(null)
      }, 1000) // 设置延迟时间，这里是1秒
    },
    handleImport(str1) {
      this.upload.title = this.$t('sp.supplierImportsInvolved')
      this.upload.open = true
      this.upload.url = getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + `/admin-api/sp/supplier-config/import?baseConfigId=${this.queryParams.baseConfigId}`
    },
    cancel() {
      this.editMode = !this.editMode
      this.getTable(this.queryParams.baseConfigId, this.queryParams.categoryLevel)
    },
    // 如果用户停留在页面时间长过token的过期时间，用户再次点击上传，当上传的时候，调用接口，会导致token过期报错，此方法就是用户预先请求，预先请求可以刷新token
    async preUploadCheck() {
      await getCountOfCompanyYear()
      this.upload.headers = getBaseHeader()
    }
  }
}
</script>

<style lang="scss" scoped>
.supplier {
  padding: 15px 20px;
}
.fixedBottom{
  z-index: 99;
  text-align: right;
  position: fixed;
  width: calc(100% - 231px);
  bottom: 20px
}
</style>
