<template>
  <div class="category">
    <configHead
      ref="configHeadComponent"
      :edit-mode.sync="editMode"
      @getTable="init"
    />

    <common-card
      :title="$t('sp.categoryAndWeightOfParticipation')"
    >
      <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="98px" size="small">
        <el-form-item :label="$t('material.category')" prop="code">
          <cascading-category
            :max-level="queryParams.categoryLevel"
            :original-value.sync="queryParams.categoryIds"
          />
        </el-form-item>
        <el-form-item :label="$t('sl.categoryPositioning')" prop="name">
          <el-select v-model="queryParams.categoryPosition" :placeholder="$t('supplier.pleaseSelectStatus')" clearable>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('sp.participateIn')">
          <el-select v-model="queryParams.involved" :placeholder="$t('common.pleaseSelect')" clearable>
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.SP_INVOLVED)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            v-hasPermi="['sp:category-config:query']"
            icon="el-icon-search"
            type="primary"
            @click="init(queryParams.baseConfigId,queryParams.categoryLevel)"
          >{{ $t('common.search') }}
          </el-button>
          <el-button icon="el-icon-refresh" @click="reset()">{{ $t('common.reset') }}</el-button>
        </el-form-item>
      </el-form>
      <div style="display: flex;justify-content: space-between;margin-bottom: 15px">
        <div>
          <el-button
            v-if="editMode"
            v-hasPermi="['sp:category-config:import']"
            icon="el-icon-upload2"
            size="mini"
            type="primary"
            @click="handleImport"
          > {{ $t('supplier.batchImport') }}
          </el-button>
          <el-button
            v-hasPermi="['sp:category-config:query']"
            icon="el-icon-upload2"
            size="mini"
            type="primary"
            @click="download()"
          > {{ $t('common.batchExport') }}
          </el-button>
        </div>
        <div>
          <el-button v-if="editMode" type="primary" @click="setParticipate('1')">{{ $t('sp.participateIn') }}</el-button>
          <el-button v-if="editMode" type="primary" @click="setParticipate('0')">{{ $t('sp.notParticipating') }}</el-button>
          {{ $t('sp.performanceCalculationBasedOn') }}

          <show-or-edit
            :disabled="!editMode"
            :dict="DICT_TYPE.SP_CATEGORY_LEVEL"
            :value="queryParams.categoryLevel"
          >
            <el-select
              v-model="queryParams.categoryLevel"
              :placeholder="$t('common.pleaseSelect')"
              style="width: 180px"
              @change="(val)=>init(queryParams.baseConfigId,val)"
            >
              <el-option
                v-for="dict in getDictDatas(DICT_TYPE.SP_CATEGORY_LEVEL)"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
              />
            </el-select>
          </show-or-edit>
          {{ $t('sp.summarizeAndCalculateCategories') }}

        </div>
      </div>
      <!--      XUERES-1500 绩效指标评估品类中，列表列宽无法调整-->
      <el-table ref="list" v-loading="loading" border :data="list">
        <el-table-column
          v-if="editMode"
          type="selection"
          width="30"
        />
        <el-table-column :label="$t('auth.categoryCode')" prop="categoryCode" />
        <el-table-column :label="$t('material.category')">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="scope.row.categoryId" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('sp.participateIn')" prop="involved">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SP_INVOLVED" :value="scope.row.involved" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('sl.categoryPositioning')" prop="">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.AUTH_SUPPLIER_SET_UP_STANDARD_CATEGORY" :value="scope.row.categoryPosition" />
          </template>
        </el-table-column>
        <el-table-column v-for="item in piList" :key="item.piId" width="110px" align="right">
          <template #header>
            <el-button
              style="text-decoration: underline;padding: 0;width: 85px;word-break: break-all;white-space: normal"
              type="text"
              @click="showBulkSetUp(item.piId)"
            >
              {{
                item.name
              }} / {{ $t('tender.weight') }}
            </el-button>
          </template>
          <template #default="scope">
            <vxe-input
              v-model.number="scope.row[`pi_${item.piId}`]"
              :disabled="!editMode"
              type="integer"
              style="width: 85px"
            />
          </template>
        </el-table-column>

      </el-table>
    </common-card>
    <el-dialog
      v-if="bulkSetupVisible"
      :visible.sync="bulkSetupVisible"
      :title="$t('sp.batchSettingIndicatorWeights')"
    >
      <div>
        {{ bulkName }}
        <vxe-input
          v-model.number="bulkNum"
          style="margin: 0 10px"
          type="integer"
        />
        %
      </div>
      <div slot="footer">
        <el-button @click="bulkSetupVisible=false">{{ $t('common.cancel') }}</el-button>
        <el-button
          v-if="editMode"
          type="primary"
          @click="setBulk"
        >
          {{ $t('common.save') }}
        </el-button>
      </div>
    </el-dialog>
    <div style="margin-bottom: 40px;display: flex; justify-content: flex-end;">
      <div style="position: fixed;bottom: 20px;z-index: 999">
        <el-button
          v-if="editMode"
          style="width: 200px;margin-right: 20px"
          type="primary"
          plain
          @click="cancel"
        >
          {{ $t('sp.closeEditing') }}
        </el-button>
        <el-button
          v-if="editMode"
          v-has-Permi="['sp:category-config:save']"
          style="width: 200px;margin-right: 20px"
          type="primary"
          @click="saveCategoryConfig"
        >
          {{ $t('scar.preservation') }}
        </el-button>
        <navigationButton v-if="!editMode" @goToPreviousStep="goToPreviousStep" @goToNextStep="goToNextStep" />
      </div>
    </div>
    <el-dialog :title="upload.title" :visible.sync="upload.open" append-to-body width="400px">
      <el-upload
        ref="upload"
        :action="upload.url"
        :auto-upload="false"
        :disabled="upload.isUploading"
        :headers="upload.headers"
        :limit="1"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        accept=".xlsx, .xls"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        <div slot="tip" class="el-upload__tip text-center">
          <span>{{ $t('common.onlyXlsXlsxFormatFilesAreAllowedToBeImported') }}</span>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="upload.open = false"
        >{{ $t('common.cancel') }}
        </el-button>
        <el-button
          type="primary"
          @click="submitFileForm"
        > {{ $t('common.confirm') }}
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import configHead from '@/views/performance/components/configHead.vue'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { exportCategoryConfig, getCategoryConfig, getPiConfigList, saveCategoryConfig } from '@/api/performance'
import { getBaseHeader } from '@/utils/request'
import { getConfig } from '@/utils/config'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import NavigationButton from '@/views/performance/components/navigationButton.vue'

export default {
  name: 'Category',
  components: {
    NavigationButton,
    configHead,
    ShowOrEdit
  },
  data() {
    return {
      upload: {
        open: false,
        title: '',
        isUploading: false,
        headers: getBaseHeader(),
        url: ''
      },

      editMode: false,
      queryParams: {
        baseConfigId: 0,
        categoryIds: [],
        categoryLevel: 0,
        categoryPosition: '',
        involved: ''
      },
      list: [],
      loading: false,
      piList: [],
      bulkSetupVisible: false,
      bulkId: '',
      bulkName: '',
      bulkNum: null
    }
  },
  mounted() {
    // this.getPiList()
  },
  methods: {
    // 上一步
    goToPreviousStep() {
      this.$router.push('/system/sp_config/standard')
    },
    // 下一步
    goToNextStep() {
      this.$router.push('/system/sp_config/supplier')
    },

    reset() {
      this.queryParams = {
        ...this.queryParams,
        categoryIds: [],
        categoryPosition: '',
        involved: ''
      }
      this.init(this.queryParams.baseConfigId, this.queryParams.categoryLevel)
    },
    async init(id, categoryLevel) {
      if (id) {
        this.queryParams.baseConfigId = id
      }
      if (categoryLevel) {
        this.queryParams.categoryLevel = categoryLevel
      } else {
        this.queryParams.categoryLevel = Number(getDictDatas(DICT_TYPE.SP_CATEGORY_LEVEL).at(-1).value)
      }
      this.loading = true
      this.getPiList().then(res => {
        getCategoryConfig(this.queryParams).then(res => {
          this.loading = false
          this.list = res.data
        })
      })
    },
    async getPiList() {
      const res = await getPiConfigList(this.queryParams)
      this.piList = res.data
    },
    resetQuery() {
      this.queryParams.involved = ''
      this.categoryPosition.involved = ''
      this.init(this.queryParams.baseConfigId, this.categoryPosition.categoryLevel)
    },
    handleImport() {
      this.upload.title = this.$t('sp.categoryAndWeightImport')
      this.upload.open = true
      this.upload.url = getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + `/admin-api/sp/category-config/import?baseConfigId=${this.queryParams.baseConfigId}
      &categoryLevel=${this.queryParams.categoryLevel}
      `
    },
    download() {
      exportCategoryConfig(this.queryParams).then(response => {
        this.$download.excel(response, this.$t('sp.exportingParticipatingCategoriesAndWeightsXlsx'))
      })
    },
    showBulkSetUp(id) {
      this.bulkId = id
      this.bulkName = this.piList.find(a => a.piId === id)?.name
      this.bulkSetupVisible = this.editMode
    },
    setBulk() {
      this.list.forEach(item => {
        item[`pi_${this.bulkId}`] = this.bulkNum
      })
      this.bulkSetupVisible = false
      this.bulkNum = null
    },
    saveCategoryConfig() {
      saveCategoryConfig({
        ...this.queryParams,
        categoryConfigData: this.list
      }).then(res => {
        if (res.data) {
          this.$message.success(this.$t('common.savedSuccessfully'))
          setTimeout(() => {
            this.$refs.configHeadComponent.initHead(null)
          }, 1000) // 设置延迟时间，这里是1秒
          this.cancel()
        }
      })
    },
    cancel() {
      this.editMode = !this.editMode
      this.init(this.queryParams.baseConfigId, this.queryParams.categoryLevel)
    },
    setParticipate(val) {
      this.$refs.list.selection.forEach(item => {
        item.involved = val
      })
    },
    handleFileUploadProgress() {
      this.upload.isUploading = true
    },
    handleFileSuccess(response) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      const data = response.data
      let text = ''
      if (data.createCategoryConfig) {
        text += this.$t('sp.numberOfSuccessfulUpdates') + data.createCategoryConfig.length
      }
      if (data.failureCategoryConfig) {
        text += '<br />' + this.$t('common.numberOfValidationFailures') + '：' + Object.keys(data.failureCategoryConfig).length
      }
      if (data.filePath) {
        text += '<br /><a class="el-button--text" target="_blank" href="' + data.filePath + '" style="text-decoration: underline">' + this.$t('common.verificationFailedDataDownload') + '</a>'
      }

      this.$alert(text, this.$t('supplier.importResults'), { dangerouslyUseHTMLString: true })
      this.init(this.queryParams.baseConfigId, this.queryParams.categoryLevel)
    },
    submitFileForm() {
      this.$refs.upload.submit()
    }
  }
}
</script>

<style lang="scss" scoped>
.category {
  padding: 15px 20px;

}

.vxe-input--inner {
  color: #444444 !important;
}
</style>
