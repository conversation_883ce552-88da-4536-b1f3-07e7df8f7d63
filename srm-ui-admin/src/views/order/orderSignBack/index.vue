<template>
  <div class="app-container">
    <div style="display: flex;justify-content: center;align-items: center;margin-bottom: 25px;">
      <el-input v-model="queryParams.search	" :placeholder="$t('order.orderNoMaterialCodeSupplierName')" clearable style="flex: 0 1 40%" @keyup.enter.native="handleSearch(true)" />
      <el-button
        v-has-permi="['order:confirm-sign:query']"
        plain
        type="primary"
        @click="handleSearch(true)"
      >{{ $t('common.search') }}</el-button>
      <el-button
        v-has-permi="['order:confirm-sign:query']"
        plain
        style="margin-left: 0"
        @click="handleClick()"
      >{{ $t('common.reset') }}</el-button>
      <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
        <el-button
          v-has-permi="['order:confirm-sign:query']"
          type="text"
          @click="init()"
        >
          {{ $t('common.advancedSearch') }}
        </el-button>

        <i
          :style="showSearch? '':{transform: 'rotate(180deg)'}"
          class="el-icon-arrow-up"
          style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;
          font-weight: 300;"
        />
      </div>

    </div>
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="189px" size="small">

      <el-form-item :label="$t('order.orderNumber')" class="searchItem" prop="orderNumber">
        <el-input v-model="queryParams.orderNo" :placeholder="$t('common.pleaseEnter')" class="searchValue" clearable />
      </el-form-item>
      <el-form-item :label="$t('common.buyer')" class="searchItem" prop="sourcingIds">
        <el-select v-model="queryParams.sourcingIds" class="searchValue" clearable filterable multiple>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_USERS, 0)"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('supplier.supplier')" class="searchItem" prop="supplier">
        <el-input v-model="queryParams.supplier" :placeholder="$t('common.pleaseEnter')" class="searchValue" clearable />
      </el-form-item>
      <el-form-item :label="$t('order.company')" class="searchItem" prop="companyIds">
        <el-select v-model="queryParams.companyIds" class="searchValue" clearable multiple>
          <el-option v-for="item in getDictDatas(DICT_TYPE.COMMON_COMPANY, 0)" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('material.materialCode')" class="searchItem" prop="materialCode">
        <el-input v-model="queryParams.materialCode" :placeholder="$t('common.pleaseEnter')" class="searchValue" />
      </el-form-item>
      <el-form-item :label="$t('material.manufacturer')" class="searchItem" prop="materialMfg">
        <el-input v-model="queryParams.materialMfg" :placeholder="$t('common.pleaseEnter')" class="searchValue" />
      </el-form-item>

      <el-form-item :label="$t('order.orderStatus')" class="searchItem" prop="orderStatus">
        <el-select v-model="queryParams.orderStatus" class="searchValue" clearable multiple>
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('material.materialDescription')" class="searchItem" prop="materialDesc">
        <el-input v-model="queryParams.materialDesc" :placeholder="$t('common.pleaseEnter')" class="searchValue" />
      </el-form-item>
      <el-form-item :label="$t('material.manufacturersPartNumber')" class="searchItem" prop="materialMpn">
        <el-input v-model="queryParams.materialMpn" :placeholder="$t('common.pleaseEnter')" class="searchValue" />
      </el-form-item>
      <el-form-item :label="$t('order.orderSigningStatus')" class="searchItem" prop="orderStatus">
        <el-select v-model="queryParams.orderSignStatus" class="searchValue" clearable multiple>
          <el-option v-for="item in getDictDatas(DICT_TYPE.ORDER_SIGN_STATUS)" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('order.timeType')" class="searchItem" prop="dateType">
        <el-select v-model="queryParams.dateType" class="searchValue" clearable>
          <el-option v-for="item in dateTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item class="searchItem" label=" ">
        <el-date-picker
          v-model="queryParams.time"
          class="searchValue"
          :end-placeholder="$t('common.endDate')"
          :range-separator="$t('order.to')"
          :start-placeholder="$t('common.startDate')"
          type="daterange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <!--      <div style="text-align: center">-->
      <!--        <el-button-->
      <!--          v-has-permi="['order:confirm-sign:query']"-->
      <!--          icon="el-icon-search"-->
      <!--          size="mini"-->
      <!--          type="primary"-->
      <!--          plain-->
      <!--          @click="init()"-->
      <!--        >{{-->
      <!--          $t('common.search')-->
      <!--        }}-->
      <!--        </el-button>-->
      <!--        <el-button-->
      <!--          v-has-permi="['order:confirm-sign:query']"-->
      <!--          icon="el-icon-refresh"-->
      <!--          size="mini"-->
      <!--          @click="handleClick"-->
      <!--        >{{ $t('common.reset') }}</el-button>-->
      <!--      </div>-->
    </el-form>
    <vxe-grid
      ref="orderSignBack"
      :data="list"
      :loading="loading"
      v-bind="girdOption"
      @sort-change="sortMethod"
    >
      <template #backSignFile="{row}">
        <el-button type="text" @click="showBackSignFile(row)">{{ $t('order.signBackFile') }}</el-button>
      </template>
      <template #orderNo="{row}">
        <copy-button type="text" @click="$router.push(`/order/orderDetail/${row.id}?id=${row.orderNo}`)">
          {{ row.orderNo }}
        </copy-button>
      </template>
      <template #factoryId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_FACTORY" :value="row.factoryId" />
      </template>
      <template #currency="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.currency" />
      </template>
      <template #sourcingId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcingId" />
      </template>
      <template #buyerCompanyId="{row}">
        <dict-tag :type="DICT_TYPE.COMMON_COMPANY" :value="row.buyerCompanyId" />
      </template>
      <template #orderType="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_TYPE" :value="row.orderType" />
      </template>
      <template #status="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_STATUS" :value="row.status" />
      </template>
      <template #signStatus="{row}">
        <dict-tag :type="DICT_TYPE.ORDER_SIGN_STATUS" :value="row.signStatus" />
      </template>
      <template #version="{row}">
        <el-button
          v-has-permi="['order:confirm-sign:query']"
          type="text"
          @click="showHis(row.id)"
        >
          {{ row.version }}
        </el-button>
      </template>
      <!--      此处红色为真实数值为红色-->
      <template #totalAmountAfterTax="{row}">
        <number-format v-if="lessThanZero(row.totalAmountAfterTax)" :font-color="'red'" :value="row.totalAmountAfterTax" />
        <number-format v-else :value="row.totalAmountAfterTax" />
      </template>

      <template #toolbar_buttons>
        <el-row :gutter="10" class="mb8" style="width: 100%">
          <el-col :span="10" style="display: flex">
            <el-button
              v-has-permi="['order:confirm-sign:orderSignFor']"
              icon="el-icon-check"
              size="mini"
              type="primary"
              @click="acceptLine"
            >
              {{ $t('order.orderReceipt') }}
            </el-button>
            <el-button
              v-has-permi="['order:confirm-sign:export-pdf']"
              icon="el-icon-printer"
              plain
              size="mini"
              type="primary"
              @click="downloadPdf"
            >
              {{ $t('order.downloadStampedPdf') }}
            </el-button>
            <el-button
              v-has-permi="['order:confirm-sign:export-confirm']"
              icon="el-icon-download"
              plain
              size="mini"
              type="primary"
              @click="downLoadExcel"
            >
              {{ $t('order.downloadList') }}
            </el-button>
            <el-button
              v-has-permi="['order:confirm-sign:orderTern']"
              icon="el-icon-close"
              plain
              size="mini"
              type="danger"
              @click="rejectLine"
            >
              {{ $t('order.rejectOrder') }}
            </el-button>

          </el-col>
          <el-col :span="14">
            <right-toolbar
              :key="girdOption.id"
              :custom-columns.sync="girdOption.columns"
              :list-id="girdOption.id"
              :show-search.sync="showSearch"
              @queryTable="init"
            />
          </el-col>
        </el-row>
      </template>
    </vxe-grid>

    <!--查看订单的历史版本组件-->
    <historyversion ref="historyversion" />

    <pagination
      v-show="total > 0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.pageNo"
      :total="total"
      @pagination="handleSearch(false)"
    />
    <el-dialog
      v-if="backSignFileShow"
      :visible.sync="backSignFileShow"
      :title="$t('order.signBackFile')"
      width="1000px"
    >
      <el-table :data="backSignFileList">
        <el-table-column :label="$t('order.uploadDate')" prop="createTime">
          <template #default="scope">
            {{ parseTime(scope.row.createTime) }}

          </template>
        </el-table-column>
        <el-table-column :label="$t('order.orderVersion')" prop="orderVersion" />
        <el-table-column :label="$t('order.uploadUsers')" prop="creator" />
        <el-table-column :label="$t('avpl.source')" prop="source">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.ORDER_CONFIRM_FILE_SOURCE" :value="scope.row.source" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('auth.documentName')" prop="name" />
        <el-table-column :label="$t('common.operate')">
          <template #default="scope">
            <el-button type="text" @click="download(scope.row)">{{ $t('order.download') }}</el-button>
            <el-button type="text" @click="delFile(scope.row)">{{ $t('common.del') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button @click="backSignFileShow=false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="showUpload">{{ $t('order.uploadSignatureFile') }}</el-button>
        <el-button type="primary" @click="backSignFileShow=false">{{ $t('order.determine') }}</el-button>
      </div>

    </el-dialog>
    <el-dialog
      v-if="uploadVisible"
      :visible.sync="uploadVisible"
      :title="$t('common.uploadFile')"
      width="400px"
    >
      <el-upload
        ref="upload"
        :action="url"
        :disabled="disabled"
        :drag="!disabled"
        :file-list="fileList"
        :headers="headers"
        :limit="1"
        :on-remove="handleRemove"
        :on-success="handleFileSuccess"
        class="upload-container"
        multiple
      >
        <i v-if="!disabled" class="el-icon-upload" />
        <div v-if="!disabled" class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
      </el-upload>

      <el-form style="margin-top: 15px">
        <el-form-item :label="$t('order.orderVersion')" required>
          <el-select v-model="orderVersion">
            <el-option
              v-for="item in versionList"
              :label="item.version"
              :value="item.version"
            />

          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="!disabled"
          v-has-permi="['order:confirm-sign:file-bind']"
          type="primary"
          @click="submitFileForm"
        > {{ $t('common.confirm') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { defineComponent } from 'vue'
import {
  exportConfirm,
  fileBind,
  getOrderConfirmFile,
  getOrderVersionList,
  orderSignFor,
  orderTern,
  pageConfirmSign
} from '@/api/orderSign'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { delDeliveryFile } from '@/api/order/deliveryNote'
import { getAccessToken } from '@/utils/auth'
import { getLatestPdfWithOrderId } from '@/api/order/orderCoordination'
import historyversion from '@/views/order/orderCoordination/historyversion'
import {getConfig} from "@/utils/config";

export default defineComponent({
  name: 'Ordersignback',
  components: {
    historyversion
  },
  data() {
    return {
      isUploading: false,
      url: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      data: {},
      disabled: false,
      fileList: [],
      showSearch: false,
      dateTypeList: getDictDatas(DICT_TYPE.ORDER_DATE_TYPE_IN_ORDER),
      queryParams: {
        search: '',
        materialCode: '',
        beginOrderDate: undefined,
        endOrderDate: undefined,
        dateType: '',
        deliveryStatus: '',
        factoryIds: [],
        companyIds: [],
        materialDesc: '',
        materialMfg: '',
        materialMpn: '',
        orderLineStatus: '',
        orderNo: '',
        orderStatus: [],
        receiptStatus: '',
        orderSignStatus: [],
        sortBy: '',
        sortField: '',
        sourcingIds: [],
        supplier: '',
        overdue: [],
        time: '',
        receiptVoucherNum: '',
        pageNo: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      loading: false,
      girdOption: {
        align: 'left',
        border: true,
        keepSource: false,
        id: 'orderSignBack',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isHover: true
        },
        columns: [
          { type: 'checkbox', width: 30, visible: true, fixed: 'left' },
          { title: this.$t('order.orderNumber'), slots: { default: 'orderNo' }, field: 'orderNo', visible: true, width: 100, fixed: 'left' },
          { title: this.$t('order.orderDate'), sortable: true, field: 'orderDate', visible: true, width: 100 },
          { title: this.$t('order.buyerCompany'), slots: { default: 'buyerCompanyId' }, field: 'buyerCompanyId', visible: true, width: 100 },
          { title: this.$t('common.buyer'), slots: { default: 'sourcingId' }, field: 'sourcingId', visible: true, width: 100 },
          { title: this.$t('order.orderStatus'), slots: { default: 'status' }, field: 'status', visible: true, width: 100 },
          { title: this.$t('order.orderType'), slots: { default: 'orderType' }, field: 'orderType', visible: true, width: 100 },
          { title: this.$t('order.amountIncludingTax'), slots: { default: 'totalAmountAfterTax' }, field: 'totalAmountAfterTax', visible: true, width: 100, align: 'right' },
          { title: this.$t('system.currency'), slots: { default: 'currency' }, field: 'currency', visible: true, width: 100 },
          { title: this.$t('order.requiredArrivalDate'), sortable: true, field: 'requestDeliveryDate', visible: true, width: 100 },
          { title: this.$t('order.latestRequiredArrivalDate'), sortable: true, field: 'latestRequestDeliveryDate', visible: true, width: 100 },
          { title: this.$t('supplier.shortNameOfSupplier'), field: 'supplierNameShort', visible: true, width: 100 },
          { title: this.$t('order.edition'), slots: { default: 'version' }, field: 'version', visible: true, width: 100 },
          { title: this.$t('order.numberOfOrderLines'), field: 'orderDetailNum', visible: true, width: 100 },
          { title: this.$t('order.supplier'), field: 'supplyingParty', visible: true, width: 100 },
          { title: this.$t('supplier.supplierName'), field: 'supplierName', visible: true, width: 100 },
          { title: this.$t('order.orderSigningStatus'), slots: { default: 'signStatus' },
            field: 'signStatus', visible: true, width: 100 },
          { title: this.$t('order.signBackFile'), field: 'backSignFile', slots: { default: 'backSignFile' }, visible: true, width: 100 }
        ],
        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            // 自定义工具栏模板
            buttons: 'toolbar_buttons'
          }
        }
      },
      backSignFileShow: false,
      backSignFileList: [],
      uploadVisible: false,
      versionList: [],
      orderVersion: '',
      orderId: ''
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.dateTypeList = this.dateTypeList.filter(i => i.value !== 'receiptDate')
      this.loading = true
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.queryParams.time, 'orderDate')
      pageConfirmSign(params).then(res => {
        this.loading = false
        this.list = res.data.list
        this.total = res.data.total
      })
    },
    downLoadExcel() {
      const params = { ...this.queryParams }
      this.addBeginAndEndTime(params, this.queryParams.time, 'orderDate')
      exportConfirm(params).then(res => {
        this.$download.excel(res, this.$t('order.orderCountersignatureXlsx'))
      })
    },
    handleClick() {
      this.queryParams = {
        search: '',
        materialCode: '',
        beginOrderDate: undefined,
        endOrderDate: undefined,
        dateType: '',
        deliveryStatus: '',
        orderSignStatus: [],
        factoryIds: [],
        companyIds: [],
        materialDesc: '',
        materialMfg: '',
        materialMpn: '',
        orderLineStatus: '',
        orderNo: '',
        orderStatus: [],
        receiptStatus: '',
        sortBy: '',
        sortField: '',
        sourcingIds: [],
        supplier: '',
        time: '',
        pageNo: 1,
        pageSize: 10
      }
      this.init()
    },
    sortMethod({ order, property }) {
      this.queryParams.sortBy = order
      this.queryParams.sortField = property
      this.init()
    },
    acceptLine() {
      const data = this.$refs.orderSignBack.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      orderSignFor(data.map(a => a.id)).then(res => {
        this.init()
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    },
    rejectLine() {
      const data = this.$refs.orderSignBack.getCheckboxRecords()
      if (data.length === 0) {
        this.$message.warning(this.$t('supplier.pleaseSelectData'))
        return
      }
      orderTern(data.map(a => a.id)).then(res => {
        this.init()
        this.$message.success(this.$t('order.operationSucceeded'))
      })
    },
    downloadPdf(row, rowIndex) {
      const data = this.$refs.orderSignBack.getCheckboxRecords()
      if (data.length === 0 || data.length > 1) {
        this.$message.warning(this.$t('order.pleaseSelectPieceOfData'))
        return
      }
      this.$modal.confirm(this.$t('order.areYouSureToDownloadTheOrderPdf')).then(() => {
        getLatestPdfWithOrderId(data[0].id).then(res => {
          if (res.data) {
            window.open(res.data)
          } else {
            this.$message.warning(this.$t('order.theOrderWasNotSuccessfully'))
          }
        })
      })
    },
    handleSearch(flag) {
      // 搜索按钮和回车默认回到第一页，翻页不需要
      if (flag) {
        this.queryParams.pageNo = 1
      }
      this.init()
    },
    lessThanZero(num) {
      if (num) {
        return num < 0
      }
      return false
    },
    showBackSignFile(row) {
      this.backSignFileShow = true
      if (row?.id) {
        this.orderId = row?.id
      }
      getOrderConfirmFile({
        businessId: this.orderId,
        businessType: 'order_confirm'
      }).then(res => {
        this.backSignFileList = res.data
      })
    },
    getVersion() {
      getOrderVersionList({
        id: this.orderId
      }).then(res => {
        this.versionList = res.data
        // 需要默认选择最高的订单版本
        if (this.versionList) {
          this.orderVersion = Math.max(...this.versionList.map(item => item.version))
        }
      })
    },
    showUpload() {
      this.getVersion()
      this.uploadVisible = true
      this.fileList = []
      this.orderVersion = ''
    },
    submitFileForm() {
      if (!this.orderVersion) {
        this.$message.error(this.$t('order.pleaseSelectVersion'))
        return
      }
      fileBind({
        businessId: this.orderId,
        businessType: 'order_confirm',
        fileId: this.fileList.at(0)?.response.data.id,
        orderVersion: this.orderVersion,
        source: 'user_upload'
      }).then(res => {
        this.$message.success(this.$t('common.uploadSucceeded'))
        this.uploadVisible = false
        this.showBackSignFile()
        this.init()
      })
    },
    handleRemove(file, fileList) {
      this.fileList = []
    },
    handleFileSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
        this.$refs.upload.clearFiles()
        return
      }
      this.fileList = fileList
    },
    delFile(row) {
      delDeliveryFile(row.id).then(res => {
        this.$message.success(this.$t('common.delSuccess'))
        this.showBackSignFile()
      })
    },
    download(row) {
      window.open(row.path)
    },
    // 订单历史版本
    showHis(orderId) {
      this.$refs.historyversion.historyVisible = true
      this.$refs.historyversion.hisParams.orderId = orderId
      this.$refs.historyversion.getHisList()
    }
  }
})
</script>
<style lang="scss" scoped>
.searchItem {
  width: 33.3%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 189px);
  }
}

.searchTimeItem {
  margin-right: 0;

  ::v-deep .el-form-item__content {
    width: calc(100% - 82px);
  }
}

.searchValue {
  width: 95%;
}

.red {
  color: red;
}
</style>

