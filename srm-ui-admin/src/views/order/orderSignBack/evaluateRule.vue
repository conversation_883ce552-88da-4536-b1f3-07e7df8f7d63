
<template>
  <div class="app-container">
    <common-card
      v-for="(item,index) in lateRule"
      :title="`${$t('order.overdueCalculationRules')}${index+1}`"
    >
      <div style="margin-bottom: 15px">
        <el-button v-hasPermi="['order:good-receiving-config:create']" type="primary" @click="saveConfig(item)">{{ $t('order.updateConfiguration') }}</el-button>
        <el-button v-hasPermi="['order:good-receiving-config:create']" type="primary" @click="newRule(lateRule,item.configType)">{{ $t('order.addRules') }}</el-button>
        <el-button v-if="lateRule.length>1" v-hasPermi="['order:good-receiving-config:delete']" type="danger" @click="delRule(lateRule,index,item.id)">{{ $t('order.deleteRule') }}</el-button>
      </div>
      <el-form label-width="300px">
        <el-form-item
          :label="$t('order.timeFrameForOntime')"
        >
          {{ $t('order.advance') }}<el-input-number v-model="item.aheadDay" :min="0" :max="999" class="configInput" />{{ $t('order.day') }}
          {{ $t('order.delay') }}<el-input-number v-model="item.delayDay" :min="0" :max="999" class="configInput" />{{ $t('order.day') }}
        </el-form-item>
        <el-form-item :label="$t('order.deliveryQuantityToleranceSetting')">
          {{ $t('order.insufficientDeliveryLimit') }}<el-input-number v-model="item.payLimit" :min="0" :max="100" class="configInput" />%
        </el-form-item>
        <el-form-item :label="$t('order.suppliersApplyingThisRule')">
          <el-radio-group v-model="item.otherAll" @input="(val)=>validateOther(lateRule,item,val)">
            <el-radio :label="true">{{ $t('order.allOthers') }}</el-radio>
            <el-radio :label="false">{{ $t('order.designatedSupplier') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div v-if="!item.otherAll">
        <el-button v-hasPermi="['order:good-receiving-config:import']" type="primary" @click="handleImport(item.configType,index)">{{ $t('supplier.batchImport') }}</el-button>
        <el-table :data="item.tableData">
          <el-table-column :label="$t('supplier.supplierCode')" prop="supplierCode" />
          <el-table-column :label="$t('supplier.supplierName')" prop="supplierName" />
          <el-table-column :label="$t('common.operate')">
            <template #default="scope">
              <el-button type="text" @click="delSupplier(item,scope)">{{ $t('common.del') }}</el-button>
            </template>
          </el-table-column>

        </el-table>
        <pagination
          v-show="item.supplierRelList.length > 0"
          :total="item.supplierRelList.length"
          :page.sync="item.pageNo"
          :limit.sync="item.pageSize"
          @pagination="getTable(item)"
        />
      </div>

    </common-card>
    <common-card
      v-for="(item,index) in onTimeRule"
      :title="`${$t('order.deliveryOntimeCalculationRules')}${index+1}`"
    >
      <div style="margin-bottom: 15px">
        <el-button v-hasPermi="['order:good-receiving-config:create']" type="primary" @click="saveConfig(item)">{{ $t('order.updateConfiguration') }}</el-button>
        <el-button v-hasPermi="['order:good-receiving-config:create']" type="primary" @click="newRule(onTimeRule,item.configType)">{{ $t('order.addRules') }}</el-button>
        <el-button v-if="onTimeRule.length>1" v-hasPermi="['order:good-receiving-config:delete']" type="danger" @click="delRule(onTimeRule,index,item.id)">{{ $t('order.deleteRule') }}</el-button>

      </div>
      <el-form label-width="200px">
        <el-form-item
          :label="$t('order.timeFrameForOntimeDelivery')"
        >
          <span>
            {{ $t('order.advance') }}<el-input-number v-model="item.aheadDay" :min="0" :max="999" class="configInput" />{{ $t('order.day') }}

          </span>
          <span>
            {{ $t('order.delay') }}<el-input-number v-model="item.delayDay" :min="0" :max="999" class="configInput" />{{ $t('order.day') }}

          </span>

        </el-form-item>
        <el-form-item :label="$t('order.suppliersApplyingThisRule')">
          <el-radio-group v-model="item.otherAll" @input="(val)=>validateOther(onTimeRule,item,val)">
            <el-radio :label="true">{{ $t('order.allOthers') }}</el-radio>
            <el-radio :label="false">{{ $t('order.designatedSupplier') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div v-if="!item.otherAll">
        <el-button v-hasPermi="['order:good-receiving-config:import']" type="primary" @click="handleImport(item.configType,index)">{{ $t('supplier.batchImport') }}</el-button>
        <el-table :data="item.tableData">
          <el-table-column :label="$t('supplier.supplierCode')" prop="supplierCode" />
          <el-table-column :label="$t('supplier.supplierName')" prop="supplierName" />
          <el-table-column :label="$t('common.operate')">
            <template #default="scope">
              <el-button type="text" @click="delSupplier(item,scope)">{{ $t('common.del') }}</el-button>
            </template>
          </el-table-column>

        </el-table>
        <pagination
          v-show="item.supplierRelList.length > 0"
          :total="item.supplierRelList.length"
          :page.sync="item.pageNo"
          :limit.sync="item.pageSize"
          @pagination="getTable(item)"
        />
      </div>

    </common-card>
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="600px" append-to-body>
      <div class="upload">
        <p>
          {{ $t('common.uploadDescription') }}：
        </p>
        <p>
          {{ $t('common.1uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.2uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.3uploadDescriptionTitle') }}
        </p>
        <p>
          {{ $t('common.4uploadDescriptionTitle') }}
        </p>
      </div>
      <div class="text-center">
        <el-upload
          ref="upload"
          class="small-padding"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
        </el-upload>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" plain @click="importTemplate"> {{ $t('common.downloadTemplate') }}</el-button>
        <el-button type="primary" @click="submitFileForm"> {{ $t('common.confirm') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { defineComponent } from 'vue'
import { getBaseHeader } from '@/utils/request'
import { getConfig } from '@/utils/config'
import {
  deleteGoodReceivingConfig,
  getGoodReceivingConfigList,
  getImportTemplateConfig,
  saveGoodReceivingConfig
} from '@/api/orderSign'

export default defineComponent({
  name: 'Evaluaterule',
  data() {
    return {
      upload: {
        configType: '',
        uploadIndex: '',
        open: false,
        title: '',
        isUploading: false,
        headers: getBaseHeader(),
        url: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/order/good-receiving-config/import'
      },

      lateRule: [
        {
          supplierRelList: [
          ],
          tableData: [],
          aheadDay: 1,
          delayDay: 1,
          payLimit: 1,
          configType: 'overdue', // overdue
          otherAll: false,
          pageNo: 1,
          pageSize: 10
        }
      ],
      onTimeRule: [
        {
          supplierRelList: [

          ],
          tableData: [],
          aheadDay: 1,
          delayDay: 1,
          payLimit: 1,
          configType: 'good_receiving_overdue', // overdue good_receiving_overdue
          otherAll: false,
          pageNo: 1,
          pageSize: 10
        }
      ]
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    getTable(item, init) {
      if (init) {
        item.pageSize = 10
        item.pageNo = 1
      }
      item.tableData = item.supplierRelList.filter(
        (a, index) => index >= (item.pageNo - 1) * item.pageSize &&
        index < item.pageNo * item.pageSize
      )
    },
    init() {
      getGoodReceivingConfigList({
        configType: 'overdue'
      }).then(res => {
        if (res.data?.length) {
          res.data.forEach(a => {
            a.pageNo = 1
            a.pageSize = 10
            if (a.supplierRelList === null) {
              a.supplierRelList = []
            }
            a.tableData = a.supplierRelList.filter((a, index) => index < 10)
          })
          this.lateRule = res.data
        }
      })
      getGoodReceivingConfigList({
        configType: 'good_receiving_overdue'
      }).then(res => {
        if (res.data?.length) {
          res.data.forEach(a => {
            a.pageNo = 1
            a.pageSize = 10
            if (a.supplierRelList === null) {
              a.supplierRelList = []
            }
            a.tableData = a.supplierRelList.filter((a, index) => index < 10)
          })
          this.onTimeRule = res.data
        }
      })
    },
    handleImport(tpye, index) {
      this.upload.title = this.$t('supplier.batchImport')
      this.upload.configType = tpye
      this.upload.uploadIndex = index
      this.upload.open = true
    },
    importTemplate() {
      getImportTemplateConfig().then(response => {
        this.$download.excel(response, '交期配置供应商模板.xlsx')
      })
    },
    submitFileForm() {
      this.$refs.upload.submit()
    },
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      if (response.code !== 0) {
        this.$modal.msgError(response.msg)
        return
      }
      switch (this.upload.configType) {
        case 'overdue':
          this.lateRule.at(this.upload.uploadIndex)?.supplierRelList.unshift(...response.data)
          this.getTable(this.lateRule.at(this.upload.uploadIndex), true)

          break
        case 'good_receiving_overdue':
          this.onTimeRule.at(this.upload.uploadIndex)?.supplierRelList.unshift(...response.data)
          this.getTable(this.onTimeRule.at(this.upload.uploadIndex), true)
          break
      }
    },
    newRule(rule, configType) {
      rule.push({
        supplierRelList: [

        ],
        tableData: [],
        aheadDay: 1,
        delayDay: 1,
        payLimit: 1,
        configType,
        otherAll: false,
        pageNo: 1,
        pageSize: 10

      })
    },
    async delRule(rule, index, id) {
      if (id) {
        await deleteGoodReceivingConfig({ id })
      }
      rule.splice(index, 1)
      this.$message.success(this.$t('common.delSuccess'))
    },
    validateOther(rule, item) {
      if (rule.filter(a => a.otherAll).length > 1) {
        this.$message.error(this.$t('order.allOtherRuleOptionsCanOnlyHaveOneOption'))
        item.otherAll = false
      }
    },
    saveConfig(item) {
      saveGoodReceivingConfig(item).then(res => {
        this.$message.success(this.$t('common.savedSuccessfully'))
        item.id ??= res.data
      })
    },
    delSupplier(item, scope) {
      item.tableData.splice(scope.$index, 1)
      for (let i = item.supplierRelList.length - 1; i >= 0; i--) {
        if (item.supplierRelList[i].supplierId === scope.row.supplierId) {
          item.supplierRelList.splice(i, 1)
        }
      }
      this.getTable(item)
    }
  }
})
</script>
<style scoped lang="scss">
.configInput{
  width: 130px;
  margin: 0 3px;
}
</style>
