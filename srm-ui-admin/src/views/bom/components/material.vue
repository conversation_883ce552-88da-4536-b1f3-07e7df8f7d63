<template>
  <el-dialog
    v-if="materialVisible"
    :before-close="closeMaterial"
    :title="$t('BOM物料')"
    :visible.sync="showDialog"
    class="rfqMaterial"
    width="1000px"
  >
    <!--基本信息-->
    <el-card class="mainTab">
      <div slot="header">
        {{ $t('基本信息    ') }}
        <dict-tag :type="DICT_TYPE.BOM_TEMPORARY_MATERIAL" :value="materialInfo.temporaryMaterial===false ? materialInfo.temporaryMaterial:materialInfo.id==null ? null:materialInfo.temporaryMaterial" />
      </div>
      <el-form ref="materialInfo" :model="materialInfo" :rules="rules"  inline label-width="120px">
        <!--物料编码-->
        <el-form-item
          :label="$t('物料编码')"
          class="commonMaterialItem"
        >
          <div v-if="materialInfo.id!=null">{{ materialInfo.materialCode }}</div>
          <el-autocomplete
            v-else
            v-model="materialInfo.materialCode"
            :fetch-suggestions="querySearchAsync"
            :placeholder="$t('rfq.pleaseEnterTheContent')"
            class="overList"
            @change="changeMaterialCode"
            @select="handleMaterialCodeSelect"
          />
        </el-form-item>
        <!--物料编码对照-->
        <el-form-item
          :label="$t('物料编码对照')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.materialCodeControl"
            :disabled="materialCodeControlEdit"
            @blur="saveMaterialCodeControl()"
          />
        </el-form-item>
        <!--自制或采购-->
        <el-form-item
          :label="$t('自制或采购')"
          class="commonMaterialItem"
          prop="homemadeSourcing"
        >
          <el-select
            v-model="materialInfo.homemadeSourcing"
            :disabled="materialEdit ||bringInfoEdit('homemadeSourcing') || !bomProjectEdit"
            class="materialItem"
            filterable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_MAKE_OR_BUY)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!--母件物料编码-->
        <el-form-item
          :label="$t('母件物料编码')"
          class="commonMaterialItem"
          prop="parentMaterialCode"
        >
          <el-select
            v-model="materialInfo.parentMaterialCode"
            :disabled="materialEdit ||bringInfoEdit('parentMaterialCode') || !bomProjectEdit || (materialInfo.level==='LV0' || materialInfo.level===null || materialInfo.level==='')"
            class="materialItem"
            filterable
            clearable
            @change="handleParentMaterialChange"
          >
            <el-option
              v-for="dict in parentMaterialList"
              :key="dict.id"
              :label="dict.materialCode"
              :value="dict.id"
            />
          </el-select></el-form-item>
        <!--价格类别-->
        <el-form-item
          :label="$t('价格类别')"
          class="commonMaterialItem"
          prop="priceCategory"
        >
          <el-select
            v-model="materialInfo.priceCategory"
            :disabled="materialEdit ||bringInfoEdit('priceCategory') || !bomProjectEdit"
            class="materialItem"
            clearable
            filterable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.RFQ_PRICE_CATEGORY)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!--预计年用量-->
        <el-form-item
          :label="$t('预计年用量')"
          class="commonMaterialItem"
        >
          <vxe-input
            v-model.number="materialInfo.estimatedAnnualUsage"
            :digits="2"
            :disabled="materialEdit || !bomProjectEdit"
            :max="999999999.99"
            :min="0.01"
            type="float"
            @blur="fixZero"
          />
        </el-form-item>
        <!--品类-->
        <el-form-item
          :label="$t('material.category')"
          class="commonMaterialItem"
          prop="category"
        >
          <el-cascader
            v-model="materialInfo.category"
            :disabled="materialEdit || bringInfoEdit('category') || !bomProjectEdit"
            :options="categoryList"
            :placeholder="$t('material.pleaseSelectCategory')"
            :props="{ value: 'id',label:'name'}"
            class="materialItem"
            clearable
            filterable
          />
        </el-form-item>
        <!--版本-->
        <el-form-item
          :label="$t('版本')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.version	"
            :disabled="materialEdit|| bringInfoEdit('version') || !bomProjectEdit"
          />
        </el-form-item>
        <!--层级-->
        <el-form-item
          :label="$t('层级')"
          class="commonMaterialItem"
          prop="level"
        >
          <el-select
            @change="handleLevelChange"
            v-model="materialInfo.level"
            :disabled="materialEdit ||bringInfoEdit('priceCategory') || !bomProjectEdit ||disableLevel"
            class="materialItem"
            clearable
            filterable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.BOM_MATERIAL_LEVEL)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!--规格型号-->
        <el-form-item
          :label="$t('规格型号')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.specifications"
            :disabled="materialEdit || bringInfoEdit('specifications')|| !bomProjectEdit"
          />
        </el-form-item>
        <!--用量-->
        <el-form-item
          :label="$t('用量')"
          class="commonMaterialItem"
          prop="dosage"
        >
          <vxe-input
            v-model="materialInfo.dosage"
            :disabled="materialEdit || !bomProjectEdit"
            :min="1"
            type="number"
          />
        </el-form-item>
        <!--采购类型-->
        <el-form-item
          :label="$t('采购类型')"
          class="commonMaterialItem"
        >
          <el-select
            v-model="materialInfo.purchaseType"
            :disabled="materialEdit|| bringInfoEdit('purchaseType') || !bomProjectEdit"
            class="materialItem"
            clearable
            filterable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_PURCHASE_TYPE,0)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <div style="display: flex;align-items: center;justify-content: center">
          <el-checkbox-group
            v-if="!quantityEdit"
            v-model="quantity"
            :disabled="materialEdit || !bomProjectEdit"
            size="mini"
            style="display: inline-block"
          >
            <el-checkbox-button v-for="item in quantityList" :key="item" :label="item">{{ item }}</el-checkbox-button>
          </el-checkbox-group>
          <div
            v-else
          >
            <vxe-input
              v-for="item in quantityEditList"
              v-model.number="item.val"
              size="mini"
              style="width: 75px"
              type="integer"
            />
          </div>
          <el-button
            v-if="!quantityEdit"
            style="margin-left:10px;display: inline-block;text-decoration: underline"
            type="text"
            @click="doQuantity"
          >
            {{ $t('rfq.customSettings') }}
          </el-button>
          <el-button
            v-else
            style="margin-left:10px;display: inline-block;text-decoration: underline"
            type="text"
            @click="saveQuantity"
          >
            {{ $t('common.saveSettings') }}
          </el-button>

        </div>
        <div style="display: flex;justify-content:center;margin: 15px 0">
          <div style="margin-top: 10px;margin-right: 5px"><span
            style="color: red"
          >*</span>{{ $t('rfq.requestedOrderQuantityFrom') }}
          </div>
          <div>
            <div
              v-for="(item,index) in materialInfo.moqList"
              style="margin:5px 0"
            >
              <el-form-item
                :prop="`moqList[${index}].quantityFrom`"
                :rules="{required: true, message: $t('common.pleaseEnter'), trigger: 'blur'}"
                style="margin-bottom: 5px;margin-top: 0px"
              >
                <vxe-input
                  v-model="item.quantityFrom"
                  :disabled="materialEdit || !bomProjectEdit"
                  size="mini"
                  :min="1"
                  style="width: 80%"
                  type="number"
                />
              </el-form-item>
            </div>
          </div>
          <div style="margin-top: 10px;margin-right: 5px">{{ $t('rfq.theOrderQuantityIsRequiredToReach') }}</div>
          <div>
            <div
              v-for="(item,index) in materialInfo.moqList"
              :disabled="materialEdit || !bomProjectEdit"
              style="display: flex;margin: 5px 0"
            >
              <el-form-item style="margin-bottom: 5px;margin-top: 0px">
                <vxe-input
                  v-model="item.quantityTo"
                  :disabled="materialEdit || !bomProjectEdit"
                  size="mini"
                  :min="1"
                  style="width: 80%"
                  type="number"
                />
              </el-form-item>
              <div style="display: flex;align-items: center;">
                <i
                  class="el-icon-circle-plus-outline"
                  style="font-size: 18px;margin: 0 5px;cursor:pointer;"
                  @click="addLadder"
                />
                <i
                  v-if="materialInfo.moqList.length > 1"
                  class="el-icon-remove-outline"
                  style="font-size: 18px;cursor:pointer;"
                  @click="delLadder(index)"
                />
              </div>
            </div>

          </div>

        </div>
        <!--物料描述-->
        <el-form-item
          :label="$t('material.materialDescription')"
          class="oneLineMaterialItem"
          prop="materialDescription"
        >
          <el-input
            v-model="materialInfo.materialDescription"
            :disabled="materialEdit || bringInfoEdit('materialDescription')|| !bomProjectEdit"
          />
        </el-form-item>
        <!--物料备注-->
        <el-form-item
          :label="$t('material.materialRemarks')"
          class="oneLineMaterialItem"
        >
          <el-input
            v-model="materialInfo.remarks	"
            :disabled="materialEdit|| bringInfoEdit('remarks')|| !bomProjectEdit"
          />
        </el-form-item>
      </el-form>
    </el-card>
    <!--制造商/制造商料号-->
    <el-card class="mainTab">
      <div slot="header"> {{ $t('制造商/制造商料号') }}</div>
      <el-form
        v-if="materialInfo.id!=null&&materialInfo.materialId!=null"
        inline
        label-width="120px"
        style="margin-top: 10px"
      >
        <el-form-item :label="$t('material.manufacturer')" class="commonMaterialItem">
          {{ materialInfo.mfg }}
        </el-form-item>
        <el-form-item :label="$t('material.manufacturersPartNumber')" class="commonMaterialItem">
          {{ materialInfo.mpn }}
        </el-form-item>
      </el-form>
      <div v-else-if="materialInfo.materialId!=null" style="margin-left: 120px">
        <el-checkbox-group v-model="materialInfo.mfgMpnList">
          <el-checkbox v-for="item in mfgMpnList" :label="item">
            {{ item.mfg }} {{ item.mpn }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <el-form
        v-else-if="materialInfo.id!=null && materialInfo.materialId==null"
        inline
        label-width="120px"
        style="margin-top: 10px"
      >
        <el-form-item :label="$t('material.manufacturer')" class="commonMaterialItem">
          {{ materialInfo.mfg }}
        </el-form-item>
        <el-form-item :label="$t('material.manufacturersPartNumber')" class="commonMaterialItem">
          {{ materialInfo.mpn }}
        </el-form-item>
      </el-form>
      <el-form v-else inline label-width="120px" style="margin-top: 10px">
        <el-form-item :label="$t('material.manufacturer')" class="commonMaterialItem">
          <el-input
            v-model="materialInfo.mfg"
            :disabled="materialEdit || !bomProjectEdit"
          />
        </el-form-item>
        <el-form-item :label="$t('material.manufacturersPartNumber')" class="commonMaterialItem">
          <el-input
            v-model="materialInfo.mpn"
            :disabled="materialEdit || !bomProjectEdit"
          />
        </el-form-item>
      </el-form>
    </el-card>
    <!--图纸-->
    <el-card class="mainTab">
      <div slot="header">{{ $t('图纸') }}</div>
      <el-upload
        :action="uploadUrl"
        :disabled="materialEdit || !bomProjectEdit"
        :headers="getBaseHeader()"
        :on-preview="onPreview"
        :on-success="(response, file, fileList)=>onDrawingSuccess(response, file,)"
        :show-file-list="false"
        class="rfq-create-upload"
        multiple
      >
        <el-button
          v-if="bomProjectEdit"
          :disabled="materialEdit || !bomProjectEdit "
          plain
          size="small"
          type="primary"
        >{{ $t('rfq.uploadDrawings') }}
        </el-button>
      </el-upload>

      <el-table :data="materialInfo.fileInfos">
        <el-table-column :label="$t('rfq.fileName')" prop="fileName">
          <template slot-scope="scope">
            <el-button
              style="text-decoration: underline"
              type="text"
              @click="download(scope.row.url)"
            >
              {{ scope.row.fileName }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="$t('rfq.drawingRemarks')" prop="remarks">
          <template #default="scope">
            <el-input v-model="scope.row.remarks" :disabled="materialEdit || !bomProjectEdit" />
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.operate')">
          <template #default="scope">
            <!--              <el-button type="text">{{ $t('rfq.addTo') }}</el-button>-->
            <el-button
              :disabled="materialEdit || !bomProjectEdit"
              type="text"
              @click="materialInfo.fileInfos.splice(scope.$index,1)"
            >{{ $t('common.del') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!--详细信息-->
    <el-card
      class="mainTab"
      style="margin-bottom: 80px"
    >
      <div slot="header">{{ $t('rfq.detailedInformation') }}</div>
      <el-form inline label-width="120px">
        <!--基本单位-->
        <el-form-item
          :label="$t('基本单位')"
          class="commonMaterialItem"
        >
          <el-select
            v-model="materialInfo.basicUnit"
            :disabled="materialEdit || bringInfoEdit('basicUnit')|| !bomProjectEdit"
            class="materialItem"
            filterable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_UOM)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!--物料类型-->
        <el-form-item
          :label="$t('物料类型')"
          class="commonMaterialItem"
        >
          <el-select
            v-model="materialInfo.materialType"
            :disabled="materialEdit || bringInfoEdit('materialType')|| !bomProjectEdit"
            class="materialItem"
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!--采购单位-->
        <el-form-item
          :label="$t('采购单位')"
          class="commonMaterialItem"
        >
          <el-select
            v-model="materialInfo.purchasingUnit"
            :disabled="materialEdit|| bringInfoEdit('purchasingUnit') || !bomProjectEdit"
            class="materialItem"
            filterable
          >
            <el-option
              v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_UOM)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!--特殊采购类型-->
        <el-form-item
          :label="$t('特殊采购类型')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.specialPurchaseType"
            :disabled="materialEdit || bringInfoEdit('specialPurchaseType') || !bomProjectEdit"
          />
        </el-form-item>
        <!--表面处理-->
        <el-form-item
          :label="$t('rfq.surfaceTreatment')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.surfaceTreatment"
            :disabled="materialEdit || !bomProjectEdit"
          />
        </el-form-item>
        <!--计划交货周期-->
        <el-form-item
          :label="$t('计划交货周期')"
          class="commonMaterialItem"
        >
          <el-input
            v-model.number="materialInfo.deliveryTime"
            :disabled="materialEdit || bringInfoEdit('deliveryTime')|| !bomProjectEdit"
            type="number"
          />
        </el-form-item>
        <!--工艺要求-->
        <el-form-item
          :label="$t('rfq.technologicalRequirements')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.technologicalRequirements"
            :disabled="materialEdit || !bomProjectEdit"
          />
        </el-form-item>
        <!--计划最小包装量-->
        <el-form-item
          :label="$t('rfq.plannedMinimumPackagingQuantity')"
          class="commonMaterialItem"
        >
          <el-input
            v-model.number="materialInfo.mpq"
            :disabled="materialEdit || bringInfoEdit('mpq')|| !bomProjectEdit"
            type="number"
          />
        </el-form-item>
        <!--材质-->
        <el-form-item
          :label="$t('材质')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.texture"
            :disabled="materialEdit|| bringInfoEdit('brand') || !bomProjectEdit"
          />
        </el-form-item>
        <!--计划最小订货量-->
        <el-form-item
          :label="$t('rfq.plannedMinimumOrderQuantity')"
          class="commonMaterialItem"
        >
          <el-input
            v-model.number="materialInfo.moq"
            :disabled="materialEdit || bringInfoEdit('moq')|| !bomProjectEdit"
            type="number"
          />
        </el-form-item>
        <!--图纸版本-->
        <el-form-item
          :label="$t('图纸版本')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.drawingVersion"
            :disabled="materialEdit|| bringInfoEdit('brand') || !bomProjectEdit"
          />
        </el-form-item>
        <!--机型-->
        <el-form-item
          :label="$t('机型')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.model"
            :disabled="materialEdit||bringInfoEdit('model') || !bomProjectEdit"
          />
        </el-form-item>
        <!--品牌-->
        <el-form-item
          :label="$t('material.brand')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.brand"
            :disabled="materialEdit|| bringInfoEdit('brand') || !bomProjectEdit"
          />
        </el-form-item>
        <!--颜色-->
        <el-form-item
          :label="$t('material.colour')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.color"
            :disabled="materialEdit || bringInfoEdit('color')|| !bomProjectEdit"
          />
        </el-form-item>
        <!--终端客户料号-->
        <el-form-item
          :label="$t('终端客户料号')"
          class="commonMaterialItem"
        >
          <el-input
            v-model="materialInfo.endCustomerPn"
            :disabled="materialEdit || bringInfoEdit('endCustomerPn')|| !bomProjectEdit"
          />
        </el-form-item>
      </el-form>

    </el-card>
    <el-card
      style="max-width: calc(100%);
       width: 943px;
    position:fixed;bottom: 20px"
    >
      <div style="text-align: right">
        <el-button @click="closeDialog">{{ $t('common.cancel') }}</el-button>
        <el-button
          v-if="bomProjectEdit&&!materialEdit"
          v-has-permi="['rfq:projects:save']"
          type="primary"
          @click="submitMaterial"
        >{{ $t('order.determine') }}
        </el-button>

      </div>
    </el-card>

  </el-dialog>
</template>

<script>
import { getMasterMaterialByMaterialId, saveMaterialCodeControl } from '@/api/rfq/home'
import { createMaterial, getParentProjectMaterialListByProjectId, getMaterialDetail } from '@/api/bom/home'
import { getTreeMap } from '@/utils'
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { getBaseHeader } from '@/utils/request'
import localforage from 'localforage'
import { getMaterialInfo } from '@/api/material/main'
import {getConfig} from "@/utils/config";

export default {
  props: ['materialVisible', 'bomProjectStatus', 'bomProjectId'],
  data() {
    return {
      disabledItem: [],
      materialInfo: {
        id: null,
        basicUnit: '',
        brand: '',
        categoryId: 0,
        category: [],
        color: '',
        currentPurchasePrice: 0,
        deliveryTime: 0,
        dosage: 1,
        endCustomerPartNumber: '',
        estimatedAnnualUsage: 1,
        homemadeSourcing: '',
        level: null,
        materialCode: '',
        materialCodeControl: '',
        materialDescription: '',
        materialHierarchy: 0,
        materialId: null,
        materialType: '',
        mfg: '',
        fileInfos: [],
        mfgMpnList: [
        ],
        model: '',
        moq: 0,
        moqList: [
          {
            quantityFrom: 1,
            quantityTo: null
          }
        ],
        mpn: '',
        mpq: 0,
        priceCategory: '',
        projectId: 0,
        purchaseType: '',
        purchasingUnit: '',
        remarks: '',
        sourcing: 0,
        specialPurchaseType: '',
        specifications: '',
        surfaceTreatment: '',
        targetPrice: 0,
        status: '',
        technologicalRequirements: '',
        temporaryMaterial: true,
        transactor: 0,
        version: '',
        parentId: '',
        parentMaterialCode: null,
        endCustomerPn: ''
      },
      categoryList: [],
      quantity: [],
      quantityList: [1, 10, 50, 100, 500, 1000, 2000, 3000, 5000, 10000],
      quantityEdit: false,
      mfgMpnList: [],
      getBaseHeader,
      uploadUrl: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload',
      quantityEditList: [],
      parentMaterialList: [],
      disableLevel: false
    }
  },
  computed: {
    DICT_TYPE() {
      return DICT_TYPE
    },
    bomProjectEdit() {
      return ['new', 'processing'].includes(this.bomProjectStatus) || !this.bomProjectId
    },
    // 当是正式物料或者物料状态不在以下状态中，物料编码对照不可编辑
    materialCodeControlEdit() {
      return (this.materialInfo.id && !['unpublished', 'published', 'to_quote', 'quoted', 'returned', 'terminated'].includes(this.materialInfo.status))
    },
    materialEdit() {
      return this.materialInfo.id && !['unpublished'].includes(this.materialInfo.status)
    },
    // 物料代入数据不为空则禁用
    bringInfoEdit() {
      return (item) => {
        return this.materialInfo.materialId && this.disabledItem.includes(item)
      }
    },
    showDialog: {
      get() {
        return this.materialVisible
      },
      set(val) {
        this.$emit('update:materialVisible', false)
      }
    },
    rules(){
      if(this.materialInfo.level!=="LV0"){
        return{
          priceCategory: [
            { required: true, message: this.$t('common.pleaseEnter'), trigger: 'change' }
          ],
          category: [
            { required: true, message: this.$t('common.pleaseEnter'), trigger: 'change' }
          ],
          materialDescription: [
            { required: true, message: this.$t('common.pleaseEnter'), trigger: 'change' }
          ],
          homemadeSourcing: [
            { required: true, message: this.$t('common.pleaseEnter'), trigger: 'change' }
          ],
          level: [
            {
              required: true, message: this.$t('common.pleaseEnter'), trigger: 'change'
            }
          ],
          dosage: [
            { required: true, message: this.$t('common.pleaseEnter'), trigger: 'blur' }
          ]
        }
      }else{
          return {
            materialDescription: [
              { required: true, message: this.$t('common.pleaseEnter'), trigger: 'change' }
            ],
            level: [
              {
                required: true, message: this.$t('common.pleaseEnter'), trigger: 'change'
              }
            ]
          }
      }
    }
  },
  watch: {
    quantity(newValue, oldValue) {
      let xor = []
      if (newValue.length > oldValue.length) {
        xor = newValue.filter(item => !oldValue.includes(item))
        this.materialInfo.moqList.push({
          quantityFrom: xor[0],
          quantityTo: null
        })
        this.materialInfo.moqList.sort((a, b) => a.quantityFrom - b.quantityFrom)
      } else {
        xor = oldValue.filter(item => !newValue.includes(item))
        this.materialInfo.moqList = this.materialInfo.moqList.filter(item => item.quantityFrom !== xor[0])
        this.materialInfo.moqList.sort((a, b) => a.quantityFrom - b.quantityFrom)
      }
      if (this.materialInfo.moqList.length === 0) {
        this.materialInfo.moqList.push({
          quantityFrom: null,
          quantityTo: null
        })
      }
    },
    materialVisible(newValue) {
      if (newValue) {
        if (this.bomProjectId) {
          getParentProjectMaterialListByProjectId({ projectId: this.bomProjectId }).then(res => {
            this.parentMaterialList = res.data
          })
        } else {
          this.bomProjectId = true
        }
      }
    }
  },
  mounted() {
    this.getCategories()
    this.initQuantity()
  },
  methods: {
    handleLevelChange(){
      this.$refs.materialInfo.clearValidate();
    },
    buttonStyle(isTemporary) {
      return {
        color: isTemporary ? 'orange' : '#9ACD32',
        borderColor: isTemporary ? 'orange' : '#9ACD32',
        backgroundColor: 'white',
        fontWeight: 'bold'
      };
    },
    getDictDatas,
    initQuantity() {
      localforage.getItem('rfqQuantity', (err, val) => {
        if (val) {
          this.quantityList = val.map(item => item.val)
        }
      })
    },
    closeDialog() {
      this.$emit('update:materialVisible', false)
    },
    closeMaterial(close) {
      this.$emit('getTable')
      this.$emit('update:materialVisible', false)
      close()
    },
    clearMaterial() {
      this.quantity.length = 0
      this.quantity = [1]
      this.mfgMpnList = []
      this.disableLevel = false
      this.materialInfo = {
        basicUnit: '',
        brand: '',
        categoryId: 0,
        category: [],
        color: '',
        currentPurchasePrice: 0,
        deliveryTime: 0,
        dosage: 1,
        endCustomerPartNumber: '',
        estimatedAnnualUsage: 1,
        homemadeSourcing: '',
        level: '',
        materialCode: '',
        materialCodeControl: '',
        materialDescription: '',
        materialHierarchy: 0,
        materialId: null,
        materialType: '',
        mfg: '',
        fileInfos: [],
        mfgMpnList: [
        ],
        model: '',
        moq: 0,
        moqList: [
        ],
        mpn: '',
        mpq: 0,
        priceCategory: 'standard_price',
        projectId: this.bomProjectId,
        purchaseType: '',
        purchasingUnit: '',
        remarks: '',
        sourcing: 0,
        specialPurchaseType: '',
        specifications: '',
        surfaceTreatment: '',
        targetPrice: 0,
        technologicalRequirements: '',
        temporaryMaterial: true,
        transactor: 0,
        version: ''
      }
    },
    setParentMaterial(item) {
      this.disableLevel = true
      this.materialInfo = {
        ...this.materialInfo,
        level: 'LV1',
        parentId: item.id,
        parentMaterialCode: item.materialCode
      }
    },
    querySearchAsync(queryString, cb) {
      if (queryString.length < 2) {
        return
      }
      getMaterialInfo({
        materialCode: queryString
      }).then(res => {
        cb(res.data.map(item => {
          return {
            value: item.materialCode + '-' + item.materialDescription,
            ...item
          }
        }))
      })
    },
    changeMaterialCode() {
      this.materialInfo.materialId = null
      this.materialInfo.temporaryMaterial=true
    },
    handleMaterialCodeSelect(item) {
      this.materialInfo.materialCode = item.materialCode
      this.materialInfo.materialDescription = item.materialDescription
      // 根据id取物料主数据的完整信息
      getMasterMaterialByMaterialId({
        materialCodeId: item.id
      }).then(res => {
        this.disabledItem.length = 0
        const { id, ...cloneItem } = res.data.material
        const { id: noGeneralMaterialId, ...clonenoGeneralMaterial } = res.data.noGeneralMaterial
        Object.assign(this.materialInfo, cloneItem, {
          category: getTreeMap(res.data.material.categoryId, this.categoryList)
        }, clonenoGeneralMaterial)
        for (const clonenoGeneralMaterialKey in clonenoGeneralMaterial) {
          if (clonenoGeneralMaterial[clonenoGeneralMaterialKey]) {
            this.disabledItem.push(clonenoGeneralMaterialKey)
          }
        }
        for (const cloneItemKey in cloneItem) {
          if (cloneItemKey === 'materialType') {
            continue
          }
          if (cloneItem[cloneItemKey]) {
            this.disabledItem.push(cloneItemKey)
          }
        }
        if (cloneItem.materialType) {
          this.disabledItem.push('homemadeSourcing')
        }
        this.disabledItem.push('materialCodeControl')
        if (cloneItem.categoryId) {
          this.disabledItem.push('category')
        }
        if(res.data.mfgRel.length>0)
        {
          this.mfgMpnList = res.data.mfgRel
          this.materialInfo.mfgMpnList = [this.mfgMpnList[0]];
        }

        // 物料主数据的id
        this.materialInfo.materialId = res.data.material.id
        // 当是物料主数据的时候，清空物料编码对照
        this.materialInfo.materialCodeControl = ''
        // 采购自制字段因为物料主数据有2个同名属性，所以需要手动指定
        this.materialInfo.homemadeSourcing = res.data.material.materialType
        this.materialInfo.temporaryMaterial=false
      })
    },
    addLadder() {
      this.materialInfo.moqList.push({
        quantityFrom: null,
        quantityTo: null
      })
    },
    delLadder(index) {
      this.materialInfo.moqList.splice(index, 1)
    },
    onPreview(file) {
      if (file?.response?.data?.url) {
        window.open(file.response.data.url)
      }
      if (file?.url) {
        window.open(file.url)
      }
    },
    fixZero() {
      if (this.materialInfo.estimatedAnnualUsage === 0) {
        this.materialInfo.estimatedAnnualUsage = 0.01
      }
    },
    download(url) {
      window.open(url)
    },
    onDrawingSuccess(response) {
      const {
        id: fileId,
        name: fileName,
        ...file
      } = response.data
      this.materialInfo.fileInfos.push({ fileId, fileName, ...file, remarks: '' })
    },
    // save the material
    submitMaterial() {
      if (!this.materialInfo.moqList.every((element, index, arr) => {
        if (index === 0) {
          return true
        }
        return element.quantityFrom >= arr[index - 1].quantityFrom
      })) {
        this.$message.error(this.$t('rfq.orderQuantityMustBeInPositiveOrder'))
        return
      }

      if (!this.materialInfo.temporaryMaterial && this.mfgMpnList.length>0 && this.materialInfo.mfgMpnList.length===0 ) {
        this.$message.error(this.$t('正式物料的MFG/MPN时必须至少选择一条'))
        return
      }
      if (this.materialInfo.level !== 'LV0' && (this.materialInfo.parentMaterialCode == null || this.materialInfo.parentMaterialCode.length <= 0)) {
        this.$message.error(this.$t('当层级不为LV0，请填写母件物料编码'))
        return
      }
      if (this.materialInfo.level === 'LV0' && (this.materialInfo.parentMaterialCode != null)) {
        this.$message.error(this.$t('当层级为LV0，请勿填写母件物料编码'))
        return
      }
      if (this.materialInfo.materialCode ===this.materialInfo.parentMaterialCode){
        this.$message.error(this.$t('母件物料编码不能与物料编码一致'))
        return
      }
      this.$refs.materialInfo.validate((valid) => {
        if (valid) {
          this.materialInfo.categoryId = this.materialInfo.category.at(-1)
          createMaterial(this.materialInfo).then(res => {
            this.$message({
              message: this.$t('common.createdSuccessfully'),
              type: 'success'
            })
            this.$emit('getTable')
            this.clearMaterial()
            this.$emit('update:materialVisible', false)
            this.$emit("expandAll")
            // this.emitter.emit('freshStep', this.bomProjectId)
          })
        }
      })
    },
    // search all parent material under the project
    searchParentMaterial() {
      getParentProjectMaterialListByProjectId({ projectId: this.bomProjectId }).then(res => {
        this.parentMaterialList = res.data.map(item => {
          return {
            parentId: item.id,
            parentMaterialCode: item.materialCode
          }
        })
      })
    },
    // process the parent material code be selected event
    handleParentMaterialChange(selectedParentId) {
      const selectedParentMaterial = this.parentMaterialList.find(dict => dict.id === selectedParentId)
      if (selectedParentMaterial) {
        // 存在匹配项则更新 parentId 和 parentMaterialCode
        this.materialInfo.parentId = selectedParentMaterial.id
        this.materialInfo.parentMaterialCode = selectedParentMaterial.materialCode
      } else {
        // 如无匹配项可清空或进行其他操作
        this.materialInfo.parentId = null
        this.materialInfo.parentMaterialCode = null
      }
    },
    saveMaterialCodeControl() {
      if (this.materialInfo.materialCodeControl && this.materialInfo.id) {
        saveMaterialCodeControl({
          id: this.materialInfo.id,
          materialCodeControl: this.materialInfo.materialCodeControl,
          projectId: this.materialInfo.projectId
        }).then(res => {
          if (res.data) {
            this.$message.success(this.$t('common.updateSuccessful'))
          }
        })
      }
    },
    showMaterialDetail(projectMaterialId) {
      getMaterialDetail({
        id: projectMaterialId
      }).then(res => {
        res.data.category = getTreeMap(res.data.categoryId, this.categoryList)
        this.mfgMpnList = res.data.mfgMpnList
        this.materialInfo = res.data
        if (!this.materialInfo.temporaryMaterial) {
          this.onloadDisabled({ ...this.materialInfo, id: this.materialInfo.materialId })
        }
      })
    },
    onloadDisabled(item) {
      this.materialInfo.materialCode = item.materialCode
      this.materialInfo.materialDescription = item.materialDescription
      // 根据id去物料主数据的完整信息
      getMasterMaterialByMaterialId({
        materialCodeId: item.id
      }).then(res => {
        this.disabledItem.length = 0
        const { id, ...cloneItem } = res.data.material
        const { id: noGeneralMaterialId, ...clonenoGeneralMaterial } = res.data.noGeneralMaterial
        for (const clonenoGeneralMaterialKey in clonenoGeneralMaterial) {
          if (clonenoGeneralMaterial[clonenoGeneralMaterialKey]) {
            this.disabledItem.push(clonenoGeneralMaterialKey)
          }
        }
        for (const cloneItemKey in cloneItem) {
          if (cloneItemKey === 'materialType') {
            continue
          }
          if (cloneItem[cloneItemKey]) {
            this.disabledItem.push(cloneItemKey)
          }
        }
        if (cloneItem.materialType) {
          this.disabledItem.push('homemadeSourcing')
        }
        this.disabledItem.push('materialCodeControl')
        if (cloneItem.categoryId) {
          this.disabledItem.push('category')
        }
      })
    },
    getCategories() {
      this.categoryList.push(...this.handleTree(getDictDatas(DICT_TYPE.COMMON_CATEGORY, 0), 'id'))
    },
    doQuantity() {
      this.quantityEdit = true
      this.quantityEditList = this.quantityList.map(val => {
        return {
          val
        }
      })
    },
    saveQuantity() {
      localforage.setItem('rfqQuantity', this.quantityEditList)
      this.quantityList = this.quantityEditList.map(item => item.val)
      this.quantityEdit = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/card.scss";

.oneLineMaterialItem {
  width: 100%;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.commonFormItem {
  width: 33.3%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

  ::v-deep .el-form-item__content {
    width: calc(100% - 170px);
  }
}

.commonFormItemLeft {
  width: 33.3%;
  padding-bottom: 5px;
  margin: 5px 0 !important;

  .content {
    width: 100%;
  }

  ::v-deep .el-form-item__content {
    width: calc(100% - 115px);
  }
}

.commonMaterialItem {
  width: 49%;
  padding-bottom: 5px;
  margin: 5px 0 !important;
}

::v-deep .el-form--label-top .el-form-item__label {
  padding-bottom: 0;
}

.rfq-create-upload {
  margin: 10px 0
}

.rfq-create {
  margin-top: 15px;
}

.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 120px);
  }
}

.materialItem {
  width: 178px;
}

.searchValue {
  width: 100%;
}

.rfqMaterial {
  .mainTab {
    margin-bottom: 15px;
  }
}

.configItem {
  margin: 5px 0;
  width: 50%;

}
.configValue{
  position: relative;
  top: 4px;
  margin-top: 10px;
  width: 375px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.overList{
  ::v-deep .el-autocomplete-suggestion{
    width: 320px !important;
  }
}
</style>
