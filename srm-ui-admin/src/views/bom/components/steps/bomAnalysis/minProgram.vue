
<template>
  <div>
    <div>
      <el-row type="flex" justify="space-between">
        <el-col :span="10" style="color: #4d93b9">
          <div style="font-size: 28px;">
            {{ schemaAndCost.scheme.totalPrice + schemaAndCost.costs.reduce((sum, item) => sum + Number(item.amount), 0) }}
            <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="schemaAndCost.scheme.completeness" />
          </div>
          <div style="margin: 10px 0">
            BOM总成本
            <el-tooltip class="item" effect="dark" content="Top Right 提示文字" placement="top-end">
              <i
                class="el-icon-question"
                style="cursor: pointer;margin-left: 10px;font-size: 14px;"
              />
            </el-tooltip>
          </div>
          <el-row>
            <el-col :span="12">
              <div style="margin: 5px 0;font-weight: bold">{{ schemaAndCost.scheme.totalPrice || '-' }}</div>
              <div>单套BOM成本</div>
            </el-col>
            <el-col :span="12">
              <div style="margin: 5px 0;font-weight: bold"> {{ schemaAndCost.costs.reduce((sum, item) => sum + Number(item.amount), 0) }}</div>
              <div>其他费用成本</div>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="4" style="text-align: center">
          <el-progress
            type="circle"
            :percentage="schemaAndCost.scheme.completeness"
            text-color="#d43030"
            color="#d43030"
          />
          <div style="text-align: center">
            推荐完成度
          </div>

        </el-col>
        <el-col :span="10">
          <delivery-chart
            v-if="schemaAndCost.scheme.deliveryPeriodInterval"
            :chart-data="schemaAndCost.scheme.deliveryPeriodInterval"
          />
          <div style="text-align: center">
            交期统计
          </div>
        </el-col>
      </el-row>
    </div>
    <div style="margin-top: 20px">
      <el-descriptions
        :column="3"
        border
        :label-style="{
          width: '120px',
          background:'#e9edf0'
        }"
      >
        <el-descriptions-item label="方案名称">{{ schemaAndCost.scheme.name }}</el-descriptions-item>
        <el-descriptions-item label="方案描述">{{ schemaAndCost.scheme.description }}</el-descriptions-item>
        <el-descriptions-item label="汇总成本">{{ schemaAndCost.costs.reduce((sum, item) => sum + Number(item.amount), 0) }}</el-descriptions-item>
        <el-descriptions-item v-for="item in getDictDatas(DICT_TYPE.BOM_SCHEME_COST)" :label="item.label">{{ schemaAndCost.costs.find(a=>a.name === item.value)?.amount }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div style="text-align: center;margin-top: 15px">
      <el-button type="primary" plain @click="showEdit">编辑</el-button>
    </div>
    <el-dialog
      :visible.sync="editSchemaVisible"
      title="编辑方案描述"
    >

      <el-form :model="editProgram" inline label-width="135px">
        <el-row>
          <el-col :span="12">
            <el-form-item class="searchItem" label="方案名称" prop="schemeName">
              <el-input v-model="editProgram.schemeName" class="searchValue" />
            </el-form-item>
            <el-form-item class="searchItem" label="方案描述" prop="schemeDescription">
              <el-input v-model="editProgram.schemeDescription" class="searchValue" autosize type="textarea" />

            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-for="(item,index) in editProgram.costs" class="searchItem">
              <template #label>
                <el-select v-model="item.name">
                  <el-option
                    v-for="item in getDictDatas(DICT_TYPE.BOM_SCHEME_COST)"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="editProgram.costs.map(a=>a.name).includes(item.label)"
                  />
                </el-select>
              </template>
              <template>
                <vxe-input min="0" v-model.number="item.cost" type="float" :digits="3" class="searchValue" />
                <i
                  v-if="editProgram.costs.length -1 === index"
                  class="el-icon-circle-plus-outline"
                  style="font-size: 18px;margin: 0 5px;cursor:pointer;"
                  @click="addCost(editProgram)"
                />
                <i
                  class="el-icon-remove-outline"
                  style="font-size: 18px;margin: 0 5px;cursor:pointer;"
                  @click="delCost(index)"
                />
              </template>
            </el-form-item>
          </el-col>
        </el-row>
        <div style="text-align: center;color: #4d93b9">
          汇总成本： {{ totalCost }}
        </div>

      </el-form>
      <div slot="footer">
        <el-button @click="editSchemaVisible = false"> 取消</el-button>
        <el-button type="primary" @click="saveCost"> 保存</el-button>

      </div>
    </el-dialog>

  </div>
</template>
<script>
import { DICT_TYPE, getDictDatas } from '@/utils/dict'
import { saveSchemeAndCost } from '@/api/bom/analysis'
import DeliveryChart from '@/views/bom/components/steps/bomAnalysis/deliveryChart.vue'

export default {
  name: 'MinProgram',
  components: { DeliveryChart },
  props: ['schemaAndCost'],
  data() {
    return {
      minProgram: {
        scheme: {
          projectId: 0,
          name: '',
          totalPrice: null,
          currency: null,
          completeness: null,
          description: null,
          status: null,
          priorHisPricePrinciple: '',
          longestDeliveryPeriod: null,
          maximumOrderQuantity: null,
          deliveryPeriodInterval: null,
          id: 0
        },
        costs: []
      },
      editProgram: {
        schemeName: '新建方案',
        schemeDescription: null,
        costs: [{
          name: null,
          cost: null
        }]
      },
      editSchemaVisible: false
    }
  },
  computed: {
    totalCost() {
      return this.editProgram.costs.reduce((sum, item) => sum + Number(item.cost), 0)
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.editProgram = {
        schemeName: this.schemaAndCost.scheme.name,
        schemeDescription: this.schemaAndCost.scheme.description,
        costs: this.schemaAndCost.costs.map(a => {
          return {
            name: a.name,
            cost: a.amount
          }
        })
      }
      if (this.editProgram.costs.length === 0) {
        this.editProgram.costs.push(
          {
            name: 'material_cost',
            cost: null
          },
          {
            name: 'management_cost',
            cost: null
          },
          {
            name: 'test_cost',
            cost: null
          },
          {
            name: 'manufacturing_cost',
            cost: null
          }
        )
      }
    },
    name() {

    },
    addCost(item) {
      if (item.costs.length < getDictDatas(DICT_TYPE.BOM_SCHEME_COST).length) {
        item.costs.push({
          name: null,
          cost: null
        })
      } else {
        this.$message.error('最多添加' + getDictDatas(DICT_TYPE.BOM_SCHEME_COST).length + '个')
      }
    },
    delCost(index) {
      if (this.editProgram.costs.length > 1) {
        this.editProgram.costs.splice(index, 1)
      } else {
        this.$message.error('至少保留一个')
      }
    },
    saveCost() {
      if (this.editProgram.costs.every(a => a.cost > 0)) {
        saveSchemeAndCost({
          schemeId: this.schemaAndCost.scheme.id,
          ...this.editProgram }).then(res => {
          this.$message.success('保存成功')
          this.editSchemaVisible = false
          this.$emit('freshCost', this.schemaAndCost.scheme.id)
        })
      }else {
        this.$message.error('请填写完整成本')
      }

    },
    showEdit() {
      this.editSchemaVisible = true
      // this.editProgram.
    }
  }
}
</script>

<style scoped lang="scss">
.searchItem {
  width: 100%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 135px);
  }
}
.searchValue {
  width: 80%;
}

::v-deep .el-progress__text{
  font-size: 18px!important;
  font-weight: bold;
}

</style>
