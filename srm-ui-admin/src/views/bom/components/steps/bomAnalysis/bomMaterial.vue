
<template>
  <div>
    <div style="display: flex;justify-content: space-around;align-items: center;margin-bottom: 25px;">
      <div style="display: flex">
        <el-input
          v-model="queryParams.search"
          :placeholder="$t('请输入物料编码，物料描述，规格型号，MFG，MPN')"
          clearable
          style="width: 500px"
          @keyup.enter.native="getMaterialDetail();"
        />
        <el-button plain type="primary" @click="getMaterialDetail();">{{ $t('common.search') }}</el-button>
        <el-button style="margin-left: 0" @click="resetQuery">{{ $t('common.reset') }}</el-button>
        <div style="margin-left: 30px;display: flex;align-items: center" @click="showSearch= !showSearch">
          <el-button type="text">
            {{ $t('common.advancedSearch') }}
          </el-button>
          <i
            :style="showSearch? '':{transform: 'rotate(180deg)'}"
            class="el-icon-arrow-up"
            style="margin-left:10px;font-size: 18px;cursor:pointer;float: right;transition: transform .3s;  font-weight: 300;"
          />
        </div>
      </div>
    </div>
    <!--      advanced search-->
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="189px" size="small">
      <!--物料状态-->
      <el-form-item
        :label="$t('物料状态')"
        class="searchItem"
      >
        <el-select v-model="queryParams.materialStatus" class="searchValue" clearable filterable multiple>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.BOM_PROJECT_MATERIAL_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!--采购类型-->
      <el-form-item
        :label="$t('采购类型')"
        class="searchItem"
      >
        <el-select v-model="queryParams.purchaseCategory" class="searchValue" clearable filterable multiple>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_PURCHASE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!--询价项目-->
      <el-form-item
        :label="$t('询价项目')"
        class="searchItem"
      >
        <el-input
          v-model="queryParams.rfqProjectNo"
          :placeholder="$t('common.pleaseEnter')"
          class="searchValue"
          clearable
        />
      </el-form-item>
      <!--正式物料-->
      <el-form-item
        :label="$t('正式物料')"
        class="searchItem"
        prop="factoryIds"
      >
        <el-select v-model="queryParams.officialMaterial" class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.COMMON_Y_N)"
            :key="dict.value"
            :label="dict.label"
            :value="Number(dict.value)"
          />
        </el-select>
      </el-form-item>
      <!--价格类别-->
      <el-form-item
        :label="$t('价格类别')"
        class="searchItem"
        prop="factoryIds"
      >
        <el-select v-model="queryParams.priceCategory" class="searchValue" clearable filterable multiple>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.RFQ_PRICE_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!--创建人-->
      <el-form-item
        :label="$t('创建人')"
        class="searchItem"
        prop="pgIds"
      >
        <el-select v-model="queryParams.creatorIds" class="searchValue" clearable filterable multiple>
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <!--品类-->
      <el-form-item
        :label="$t('品类')"
        class="searchItem"
        prop="factoryIds"
      >
        <cascading-category
          class="searchValue"
          :original-value.sync="queryParams.categories"
        />

      </el-form-item>
      <!--自制或采购-->
      <el-form-item
        :label="$t('自制或采购')"
        class="searchItem"
        prop="pgIds"
      >
        <el-select v-model="queryParams.purchaseOrSelfMade" class="searchValue" clearable filterable>
          <el-option
            v-for="dict in getDictDatas(DICT_TYPE.MATERIAL_MAKE_OR_BUY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!--寻源采购-->
      <el-form-item
        :label="$t('寻源采购')"
        class="searchItem"
        prop="pgIds"
      >
        <el-select
          v-model="queryParams.sourcing"
          class="searchValue"
          clearable
          multiple
        >
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.COMMON_USERS,0)"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="$t('无价格物料')"
        class="searchItem"
      >
        <el-checkbox v-model="queryParams.noPriceMaterial" :true-label="1" :false-label="0" />

      </el-form-item>
      <el-form-item
        :label="$t('价格来源')"
        class="searchItem"
      >
        <el-select
          v-model="queryParams.priceSource"
          class="searchValue"
          clearable
          multiple
        >
          <el-option
            v-for="item in getDictDatas(DICT_TYPE.BOM_PRICE_SOURCE,0)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="$t('成本占比')"
        class="searchItem"
        prop="pgIds"
      >
        <el-slider v-model="queryParams.proportionCost" />

      </el-form-item>
    </el-form>

    <vxe-grid
      ref="bomMaterialTable"
      resizable
      row-id="id"
      :data="list"
      :loading="loading"
      :tree-config="{ transform: true, reserve: true,rowField:'materialId',expandAll:true }"
      :checkbox-config="{ labelField: 'name' }"
      v-bind="gridOption"
    >
      <template
        #dict="{row,column}"
      >
        <dict-tag
          :type="column.params.dictType"
          :value="row[column.field]"
        />
      </template>
      <template #unitPriceIncludesTax="{ row }">
        <show-or-edit
          :value="row.unitPriceIncludesTax"
          :disabled="!minEditable"
        >
          <vxe-input
            v-model="row.unitPriceIncludesTax"
            type="float"
            @blur="savePrice(row)"
          />

        </show-or-edit>
      </template>
      <template #schemeEdit="{ row,column }">
        <show-or-edit
          :value="row[column.field]"
          :disabled="!minEditable"
        >
          <vxe-input
            v-model="row[column.field]"
            type="float"
            :digits="2"
            @blur="saveSchemePrice(row,column)"
          />

        </show-or-edit>
      </template>
      <template #status="{ row }">
        <dict-tag :type="DICT_TYPE.BOM_PROJECT_MATERIAL_STATUS" :value="row.status" />
      </template>
      <template #materialCode="{ row }">
        <copy-button
          type="text"
          @click="showMaterialDetail(row)"
        >
          {{ row.materialCode }}
        </copy-button>
      </template>
      <template #homemadeSourcing="{ row }">
        <dict-tag :type="DICT_TYPE.MATERIAL_MAKE_OR_BUY" :value="row.homemadeSourcing" />
      </template>
      <template #priceCategory="{ row }">
        <dict-tag :type="DICT_TYPE.RFQ_PRICE_CATEGORY" :value="row.priceCategory" />
      </template>
      <template #temporaryMaterial="{ row }">
        <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="row.temporaryMaterial" />
      </template>
      <template #creator="{ row }">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.creator" />
      </template>
      <template #sourcingPurchase="{ row }">
        <dict-tag :type="DICT_TYPE.COMMON_USERS" :value="row.sourcingPurchase" />
      </template>
      <template #historyCurrency="{ row }">
        <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.historyCurrency" />
      </template>
      <template #rfqProjectNo="{ row }">
        <copy-button
          type="text"
          @click="$router.push(`/rfq/processHome/${row.rfqProjectNo}?id=${row.rfqProjectId}&code=bom_enquiry&projectNo=${row.rfqProjectNo}`)"
        >
          {{ row.rfqProjectNo }}
        </copy-button>
      </template>
      <template #categoryId="{ row }">
        <dict-tag :type="DICT_TYPE.COMMON_CATEGORY" :value="row.categoryId" />
      </template>
      <template #purchaseType="{ row }">
        <dict-tag :type="DICT_TYPE.MATERIAL_PURCHASE_TYPE" :value="row.purchaseType" />
      </template>
      <template #basicUnit="{ row }">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.basicUnit" />
      </template>
      <template #purchasingUnit="{ row }">
        <dict-tag :type="DICT_TYPE.MATERIAL_UOM" :value="row.purchasingUnit" />
      </template>
      <template #materialType="{ row }">
        <dict-tag :type="DICT_TYPE.MATERIAL_TYPE" :value="row.materialType" />
      </template>
      <template #operate="{ row }">
        <OperateDropDown
          v-if="row.level === 'LV1'&&minEditable"
          :menu-item="[
            {
              name: $t('使用历史价'),
              show: true,
              action: () => usePrice(row, 'history_price'),
              para: row
            },
            {
              name: $t('使用询报价'),
              show: true,
              action: () => usePrice(row, 'rfq_price'),
              para: row
            }
          ]"
        />
      </template>
      <template
        #supplierName="{row}"
      >
        <el-button
          v-if="!row.supplierName && row.originalPriceSource==='rfq_price'"
          style="text-decoration: underline"
          type="text"
          @click="showSupplier(row)"
        >{{ $t('请选择') }}</el-button>
        <el-button
          v-else
          style="text-decoration: underline"
          type="text"
          @click="showSupplier(row)"
        >{{ row.supplierName }}</el-button>
      </template>
      <template #toolbar_buttons>
        <el-row style="width: 100%">
          <el-col :span="23">
            <div style="display:flex;align-items: center">
              <material-config
                v-if="selectedSchema.id&&minEditable"
                :selected-schema="selectedSchema"
                @getMaterialDetail="getMaterialDetail"
              />
              <el-button
                v-if="selectedSchema.approvalStatus==='new'&&!minEditable"
                size="mini"
                type="primary"
                @click="$emit('toEdit', selectedSchema.id)"
              > {{ $t('编辑方案') }}
              </el-button>
              <!--展开所有-->
              <el-button
                plain
                size="mini"
                type="primary"
                @click="$refs.bomMaterialTable.setAllTreeExpand(true)"
              > {{ $t('展开所有') }}
              </el-button>
              <!--关闭所有-->
              <el-button
                v-has-permi="['rfq:project-materials:tempoToFormal']"
                plain
                size="mini"
                type="primary"
                @click="$refs.bomMaterialTable.clearTreeExpand()"
              > {{ $t('关闭所有') }}
              </el-button>
              <!--查看报告-->
              <div
                v-if="selectedSchema.approvalStatus!=='new'"
                style="margin-left: 15px;"
              >
                <el-button
                  plain
                  size="mini"
                  type="primary"
                  @click="showViewReport()"
                > {{ $t('查看报告') }}
                </el-button>
              </div>
            </div>
          </el-col>
          <el-col :span="1">
            <right-toolbar
              :custom-columns.sync="gridOption.columns"
              :list-id="gridOption.id"
              :only-custom="false"
              :show-search.sync="showSearch"
              style="float: right"
              @queryTable="getMaterialDetail"
            />
          </el-col>
        </el-row>
      </template>
    </vxe-grid>

    <el-dialog
      v-if="showSupplierDialog"
      title="选择供应商"
      width="1000px"
      :visible.sync="showSupplierDialog"
    >
      <el-row style="color: #4d93b9;font-size: 14px;margin: 10px 0;width: 1000px">
        <el-col :span="6"> 物料编码 {{ selectedMaterial.materialCode }}</el-col>
        <el-col :span="4"> 单套总用量 {{ selectedMaterial.singleTotalUsage }}</el-col>
        <el-col :span="8"> 物料描述 {{ selectedMaterial.materialDescription }}</el-col>
      </el-row>
      <vxe-table ref="supplierList" :data="supplierList"
         :checkbox-config="{
          highlight:true
        }"
        :row-config="{keyField: 'id'}"
      >
        <vxe-column title="供应商" field="supplierName" />
        <vxe-column title="报价的制造商" field="quotedMfg" />
        <vxe-column title="报价的制造商料号" field="quotedMpn" />
        <vxe-column title="订货量从" field="quotationQuantityFrom" />
        <vxe-column title="订货量到" field="quotationQuantityTo" />
        <vxe-column title="报价单价含税" field="priceTax" />
        <vxe-column title="报价原币种" field="currency">
          <template #default="{row}">
            <dict-tag :type="DICT_TYPE.COMMON_CURRENCY" :value="row.currency" />
          </template>
        </vxe-column>
        <vxe-column title="配额(%)" field="quota">
          <template #default="{row}">
            <vxe-input
              v-model="row.quota"
              type="float"
              style="width: 80px"
            />
          </template>
        </vxe-column>
      </vxe-table>
      <div slot="footer">
        <el-button @click="showSupplierDialog = false">取消</el-button>
        <el-button type="primary" @click="saveSupplier">确定</el-button>
      </div>
    </el-dialog>
    <!--查看报告-->
    <el-dialog :title="$t('查看报告')" :visible.sync="viewReport.showReport" width="600px">
      <!--报告选项-->
      <el-descriptions>
        <el-descriptions-item>
          <el-checkbox-group v-model="viewReport.selectReport" style="display: block;">
            <el-checkbox
              v-for="(item, index) in viewReport.fileList"
              :key="index"
              :label="item"
              style="display: block;"
            >
              {{ item.fileName }}
            </el-checkbox>
          </el-checkbox-group>
        </el-descriptions-item>
      </el-descriptions>
      <!--按钮-->
      <div style="display: flex;justify-content: space-around;margin-top: 20px;padding: 0 64px;">
        <el-button style="width: 20%" @click="viewReport.showReport = false">{{ $t('common.cancel') }}</el-button>
        <el-button style="width: 20%" type="primary" plain @click="downloadReport">{{ $t('下载报告') }}</el-button>
        <el-button
          style="width: 20%"
          type="primary"
          @click="previewReport"
        >
          {{ $t('预览报告') }}
        </el-button>
      </div>
    </el-dialog>
    <!--预览报告-->
    <el-dialog :title="$t('预览报告')" :visible.sync="viewReport.previewReport" width="1000px">
      <div style="min-height: 700px">
        <el-tabs v-model="viewReport.activeTab" type="border-card">
          <el-tab-pane
            v-for="(file, index) in viewReport.selectReport"
            :key="index"
            :label="file.fileName"
            :name="file.fileName"
          >
            <vue-office-excel
              :src="file.filePath"
              style="height: 700px;"
              @rendered="onRendered"
              @error="onError"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
    <el-dialog
      v-if="showHistoryPriceDialog"
      title="历史价格"
      width="1000px"
      :visible.sync="showHistoryPriceDialog"
    >
      <el-row style="color: #4d93b9;font-size: 14px;margin: 10px 0;width: 1000px">
        <el-col :span="6"> 物料编码 {{ selectedMaterial.materialCode }}</el-col>
        <el-col :span="4"> 单套总用量 {{ selectedMaterial.singleTotalUsage }}</el-col>
        <el-col :span="8"> 物料描述 {{ selectedMaterial.materialDescription }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-form  ref="queryHistoryForm" :inline="true" :model="supplierQuery" label-width="189px" size="small">
            <!--物料编码-->
            <el-form-item
              :label="$t('物料编码')"
              class="searchItem"
            >
              <el-input
                v-model="supplierQuery.materialCode"
                :placeholder="$t('请输入物料编码')"
                clearable
                style="width: 150px"
              />
            </el-form-item>
            <!--制造商-->
            <el-form-item
              :label="$t('制造商')"
              class="searchItem"
            >
              <el-input
                v-model="supplierQuery.mfg"
                :placeholder="$t('common.pleaseEnter')"
                clearable
                class="searchValue"
                style="width: 150px"
              />
            </el-form-item>
            <!--制造商料号-->
            <el-form-item
              :label="$t('制造商料号')"
              class="searchItem"
            >
              <el-input
                v-model="supplierQuery.mpn"
                :placeholder="$t('common.pleaseEnter')"
                class="searchValue"
                clearable
                style="width: 150px"
              />
            </el-form-item>
            <!--价格类别-->
            <el-form-item
              :label="$t('价格类别')"
              class="searchItem"
            >
              <el-select v-model="supplierQuery.priceCategory" class="searchValue" clearable filterable
                         style="width: 150px" >
                <el-option
                  v-for="dict in getDictDatas(DICT_TYPE.RFQ_PRICE_CATEGORY)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              :label="$t(' ')"
              class="searchItem"
            >
              <div style="width: 150px">
              <el-button plain type="primary" @click="getHistoryList(false);">{{ $t('common.search') }}</el-button>
              <el-button style="margin-left: 0" @click="resetHistory">{{ $t('common.reset') }}</el-button>
              </div>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>


      <vxe-table
        ref="historyList"
        row-id="id"
        :radio-config="{
          highlight:true
        }"
        :data="historyList"
      >
        <vxe-column type="radio" width="50" />
        <vxe-column title="询价单号" field="rfqNo" />
        <vxe-column title="物料编码" field="materialCode" />
        <vxe-column title="物料描述" field="materialDescription" />
        <vxe-column title="制造商" field="mfg" />
        <vxe-column title="制造商料号" field="mpn" />
        <vxe-column title="历史供应商" field="supplierName" />
        <vxe-column title="历史价格" field="price" />
        <vxe-column title="历史币种" field="currency">
          <template #default="{row}">
            <dict-tag :value="row.currency" :type="DICT_TYPE.COMMON_CURRENCY" />
          </template>
        </vxe-column>
        <vxe-column title="基本单位" field="basicUnit">
          <template #default="{row}">
            <dict-tag :value="row.basicUnit" :type="DICT_TYPE.MATERIAL_UOM" />
          </template>
        </vxe-column>
        <vxe-column title="价格单位" field="unit" />
      </vxe-table>
      <pagination
        v-show="historyListTotal > 0"
        :total="historyListTotal"
        :page.sync="supplierQuery.pageNo"
        :limit.sync="supplierQuery.pageSize"
        @pagination="getHistoryList(false)"
      />
      <div slot="footer">
        <el-button @click="showHistoryPriceDialog = false">取消</el-button>
        <el-button type="primary" @click="saveHistory">确定</el-button>
      </div>
    </el-dialog>
    <!--物料查看-->
    <materialInfo
      ref="materialInfo"
      :material-visible.sync="materialVisible"
      :bom-project-id="null"
      :bom-project-status="null"
    />

  </div>

</template>
<script>
import OperateDropDown from '@/components/OperateDropDown/index.vue'
import {
  downloadReport,
  getBillMaterialSchemeMaterialRelList,
  getMaterialHistoryPriceList,
  getProjectMaterialPriceSourceList,
  getSupplierAvplHistoryPriceList,
  saveProjectMaterialPriceSourceList, saveSupplierAvplHistoryPrice, updateSchemeMaterialManualPrice,
  updateSchemeMaterialPrice
} from '@/api/bom/analysis'
import { DICT_TYPE, getDictDataLabel } from '@/utils/dict'
import MaterialConfig from '@/views/bom/components/steps/bomAnalysis/materialConfig.vue'
import ShowOrEdit from '@/components/ShowOrEdit/index.vue'
import { getBomFile } from '@/api/bom/home'
import localforage from 'localforage'
import VueOfficeExcel from '@vue-office/excel'
// 引入相关样式
import '@vue-office/excel/lib/index.css'
import { deepClone } from '@/utils'

export default {
  name: 'BomMaterial',
  components: { ShowOrEdit, MaterialConfig, OperateDropDown, VueOfficeExcel,
    materialInfo: () => import('@/views/bom/components/material')
  },
  props: ['selectedSchema', 'minEditable'],
  data() {
    return {
      noSelectedColumn: [
        { type: 'checkbox', width: 80, fixed: 'left', treeNode: true, visible: true },

        {
          title: this.$t('层级'),
          field: 'level',
          fixed: 'left',
          width: 80,
          visible: true
        },

        {
          title: this.$t('物料编码'),
          field: 'materialCode',
          fixed: 'left',
          width: 80,

          visible: true
        },
        {
          title: this.$t('物料描述'),
          field: 'materialDescription',
          fixed: 'left',
          width: 180,
          visible: true
        },
        {
          title: this.$t('单套总用量'),
          field: 'singleTotalUsage',
          width: 80,
          visible: true
        },
        {
          title: this.$t('自制或者采购'),
          field: 'homemadeSourcing',
          width: 80,
          slots: { default: 'homemadeSourcing' },
          visible: true
        },

        {
          title: this.$t('单价含税'),
          field: 'unitPriceIncludesTax',
          width: 80,
          visible: true
        },
        {
          title: this.$t('供应商'),
          field: 'supplierName',
          width: 80,
          visible: true
        },
        {
          title: this.$t('价格来源'),
          field: 'sourceOfPrice',
          params: { dictType: DICT_TYPE.BOM_PRICE_SOURCE },
          width: 80,
          slots: { default: 'dict' },
          visible: true
        },
        {
          title: this.$t('询价项目'),
          field: 'rfqProjectNo',
          width: 150,
          visible: true
        },
        {
          title: this.$t('报价状态'),
          field: 'status',
          width: 80,
          formatter: ({ cellValue }) => cellValue ? getDictDataLabel(DICT_TYPE.RFQ_QUOTATION_STATUS, cellValue) : '',
          visible: true
        }

      ],
      selectedColumn: [
        { type: 'checkbox', width: 80, fixed: 'left', treeNode: true, visible: true },
        {
          title: this.$t('层级'),
          field: 'level',
          fixed: 'left',
          width: 80,
          visible: true
        },

        {
          title: this.$t('物料编码'),
          field: 'materialCode',
          slots: { default: 'materialCode' },
          fixed: 'left',
          width: 80,
          visible: true
        },
        {
          title: this.$t('物料描述'),
          field: 'materialDescription',
          fixed: 'left',
          width: 180,
          visible: true
        },
        {
          title: this.$t('单套总用量'),
          field: 'singleTotalUsage',
          width: 80,
          visible: true
        },
        {
          title: this.$t('自制或者采购'),
          field: 'homemadeSourcing',
          width: 80,
          slots: { default: 'homemadeSourcing' },
          visible: true
        },

        {
          title: this.$t('单价含税'),
          field: 'unitPriceIncludesTax',
          slots: { default: 'unitPriceIncludesTax' },
          width: 80,
          visible: true
        },
        {

          title: this.$t('目标币种'),
          field: 'targetCurrency',
          params: { dictType: DICT_TYPE.COMMON_CURRENCY },
          slots: { default: 'dict' },
          width: 80,
          visible: true
        },
        {
          title: this.$t('供应商'),
          field: 'supplierName',
          slots: { default: 'supplierName' },
          width: 80,
          visible: true
        },
        {
          title: this.$t('价格来源'),
          field: 'sourceOfPrice',
          params: { dictType: DICT_TYPE.BOM_PRICE_SOURCE },
          width: 80,
          slots: { default: 'dict' },
          visible: true
        },
        {
          title: this.$t('单套BOM成本'),
          field: 'singleBomCost',
          width: 80,
          visible: true
        },
        {
          title: this.$t('成本占比'),
          formatter: ({ cellValue }) => cellValue ? `${cellValue}%` : '',

          field: 'costRatio',
          width: 80,
          visible: true
        },
        {
          title: this.$t('询价项目'),
          field: 'rfqProjectNo',
          slots: { default: 'rfqProjectNo' },
          width: 150,
          visible: true
        },
        {
          title: this.$t('报价单价含税'),
          field: 'quotedPriceIncludesTax',
          width: 80,
          visible: true
        },
        {
          title: this.$t('报价原币种'),
          field: 'originalCurrency',
          params: { dictType: DICT_TYPE.COMMON_CURRENCY },
          slots: { default: 'dict' },
          width: 80,
          visible: true
        },
        {
          title: this.$t('PPV%'),
          field: 'ppv',
          slots: { default: 'schemeEdit' },
          width: 80,
          visible: true
        },
        {
          title: this.$t('PPV总值'),
          slots: { default: 'schemeEdit' },

          field: 'ppvTotal',
          width: 80,
          visible: true
        },
        {
          title: this.$t('物料附加%'),
          slots: { default: 'schemeEdit' },
          field: 'materialAddition',
          width: 80,
          visible: true
        },
        {
          title: this.$t('物料附加成本'),
          slots: { default: 'schemeEdit' },
          field: 'materialAdditionCost',
          width: 80,
          visible: true
        },
        {
          title: this.$t('耗损%'),
          field: 'consume',
          slots: { default: 'schemeEdit' },
          width: 80,
          visible: true
        },
        {
          title: this.$t('损耗金额'),
          field: 'consumeAmount',
          slots: { default: 'schemeEdit' },
          width: 80,
          visible: true
        },
        {
          title: this.$t('NRE%'),
          slots: { default: 'schemeEdit' },

          field: 'nre',
          width: 80,
          visible: true
        },
        {
          title: this.$t('NRE金额'),
          slots: { default: 'schemeEdit' },
          field: 'nreAmount',
          width: 80,
          visible: true
        },
        {
          title: this.$t('BOM总成本'),
          field: 'bomTotalCost',
          width: 80,
          visible: true
        },
        {
          title: this.$t('交货条件'),
          field: 'deliveryCondition',
          width: 80,
          visible: true
        },
        {
          title: this.$t('出厂地'),
          field: 'factoryPlace',
          width: 80,
          visible: true
        }
      ],
      queryParams: {
        projectId: this.$route.query.id,
        schemeId: '',
        materialStatus: [
        ],
        purchaseCategory: [
        ],
        rfqProjectNo: '',
        officialMaterial: null,
        priceCategory: [
        ],
        creatorIds: [
        ],
        categories: [
        ],
        purchaseOrSelfMade: '',
        sourcing: [
        ],
        noPriceMaterial: 0,
        priceSource: [
        ],
        proportionCost: 100
      },
      showSearch: false,
      list: [],
      gridOption: {
        align: 'left',
        border: true,
        keepSource: true,
        id: 'bomMaterialGrid',
        maxHeight: 700,
        columnConfig: {
          resizable: true
        },
        showOverflow: true,
        customConfig: {
          storage: {
            resizable: true
          }
        },
        rowConfig: {
          isCurrent: true,
          isHover: true
        },
        columns: [],

        sortConfig: {
          remote: true
        },
        toolbarConfig: {
          slots: {
            buttons: 'toolbar_buttons'
          }
        }
      },

      loading: false,
      showSupplierDialog: false,
      storeList: [],
      storePriceMap: {},
      showHistoryPriceDialog: false,
      historyList: [],
      historyListTotal: 0,
      supplierList: [],
      supplierQuery: {
        search: '',
        materialCode: '',
        mfg: '',
        mpn: '',
        priceCategory: '',
        materialId: 1,
        pageNo: 1,
        pageSize: 10
      },
      selectedMaterial: {},
      viewReport: {
        activeTab: '',
        previewReport: false,
        showReport: false,
        fileList: [],
        selectReport: []
      },
      materialVisible: false
    }
  },
  watch: {
    minEditable: {
      immediate: true,
      handler(val) {
        const operator = {
          title: this.$t('操作'),
          fixed: 'left',
          slots: { default: 'operate' },
          width: 30,
          visible: true
        }
        const index = this.selectedColumn.findIndex(col => col.title === this.$t('操作'))
        if (val) {
        // 如果操作列不存在，则添加
          if (index === -1) {
            this.selectedColumn.splice(1, 0, operator)
          }
        } else {
        // 如果操作列存在，则移除
          if (index !== -1) {
            this.selectedColumn.splice(index, 1)
          }
        }
      }
    },
    'selectedSchema.id': {
      immediate: true,
      handler(val) {
        if (!val) {
          this.gridOption.columns = this.noSelectedColumn
        } else {
          this.gridOption.columns = this.selectedColumn
        }
        this.queryParams.schemeId = val
        this.getMaterialDetail()
      }
    }
  },
  methods: {
    getMaterialDetail() {
      this.loading = true
      getBillMaterialSchemeMaterialRelList(this.queryParams).then(res => {
        this.list = res.data
        this.list.forEach(a => {
          this.storePriceMap[a.id] = a.unitPriceIncludesTax
        })
        this.storeList = deepClone(res.data)
        this.loading = false,
        this.$nextTick(() => {
          if (this.$refs.bomMaterialTable) {
            // 先清空展开状态
            this.$refs.bomMaterialTable.clearTreeExpand()
            // 然后重新展开
            this.$refs.bomMaterialTable.setAllTreeExpand(true)
          }
        })
      })
    },
    resetQuery() {
      this.queryParams = {
        projectId: this.$route.query.id,
        schemeId: this.selectedSchema.id,
        materialStatus: [
        ],
        purchaseCategory: [
        ],
        rfqProjectNo: '',
        officialMaterial: null,
        priceCategory: [
        ],
        creatorIds: [
        ],
        categories: [
        ],
        purchaseOrSelfMade: '',
        sourcing: [
        ],
        noPriceMaterial: 0,
        priceSource: [
        ],
        proportionCost: 100
      }
      this.getMaterialDetail()
    },
    expandAll() {
      if (this.$refs.bomMaterialTable) {
        this.$refs.bomMaterialTable.setAllTreeExpand(true)
      }
    },
    showMaterialDetail(row) {
      this.materialVisible = true
      this.$refs.materialInfo.showMaterialDetail(row.materialId)
    },
    getBomTableSelected() {
      return this.$refs.bomMaterialTable.getCheckboxRecords()
    },
    usePrice(row, price) {
      getMaterialHistoryPriceList(this.queryParams.schemeId, row.materialId, price).then(res => {
        this.$message.success('操作成功')
        this.getMaterialDetail()
      })
    },
    showSupplier(row) {
      this.selectedMaterial = row
      // 选择供应商
      if (row.sourceOfPrice === 'rfq_price' || row.originalPriceSource === 'rfq_price') {
        this.showSupplierDialog = true
        this.getPriceList(row)
      } else if (row.sourceOfPrice === 'history_price') {

        this.showHistoryPriceDialog = true
        this.supplierQuery.materialCode = row.materialCode
        this.supplierQuery.mfg = row.quotedMfg
        this.supplierQuery.mpn = row.quotedMpn
        this.supplierQuery.priceCategory=row.priceCategory
        this.getHistoryList(true)
      }
    },
    getHistoryList(initialization) {
      console.log(this.supplierQuery.priceCategory)
      if (initialization) {
        this.supplierQuery.materialId = this.selectedMaterial.materialId
      } else {
        this.supplierQuery.materialId = null
      }
      // 处理查询参数
      getSupplierAvplHistoryPriceList(this.supplierQuery).then(res => {
        this.historyList = res.data.list
        this.historyListTotal = res.data.total

        const row = this.historyList.find(a=>a.materialSupplierLadderId===this.selectedMaterial.materialSupplierLadderId)
        if (row){
          this.$refs.historyList.setRadioRow(row)
        }

      })
    },
    getPriceList(row) {
      getProjectMaterialPriceSourceList(row.materialId, 'rfq_price').then(res => {
        this.supplierList = res.data
        const findRows = this.supplierList.find(a=>a.id===row.historyPriceId)
        if (findRows){
          this.$refs.supplierList.setCheckboxRow(findRows, true)
        }

      })
    },
    savePrice(row) {
      if(row.unitPriceIncludesTax<0)
      {
        this.$message.error('不能位负数!')
        return
      }
      if (String(row.unitPriceIncludesTax) !== String(this.storePriceMap[row.id]?.toFixed(2))) {
        updateSchemeMaterialManualPrice({
          ...row,
          schemeId: this.queryParams.schemeId
        }).then(res => {
          this.$message.success('操作成功')
          this.getMaterialDetail()
        })
      }
    },
    saveSupplier() {
      var data=this.supplierList;
      if (data.reduce(
        (acc, cur) => acc + (cur.quota ? Number(cur.quota) : 0), 0
      ) === 100) {
        saveProjectMaterialPriceSourceList({
          schemeId: this.queryParams.schemeId,
          materialId: this.selectedMaterial.materialId,
          priceList: data
        }).then(
          res => {
            this.showSupplierDialog = false
            this.getMaterialDetail()
            this.$message.success('操作成功')
          }
        )
      } else {
        this.$message.error('配额必须等于100%')
      }
    },
    saveHistory() {
      const data = this.$refs.historyList.getRadioRecord()
      if (data) {
        saveSupplierAvplHistoryPrice({
          ...data,
          id: this.selectedMaterial.id
        }).then(res => {
          this.showHistoryPriceDialog = false
          this.$message.success('操作成功')
          this.getMaterialDetail()
        })
      } else {
        this.$message.error('请选择供应商历史价')
      }
    },
    resetHistory() {
      this.supplierQuery = {
        search: '',
        materialCode: '',
        mfg: '',
        mpn: '',
        priceCategory: '',
        materialId:1,
        pageNo: 1,
        pageSize: 10
      }
      this.getHistoryList()
    },
    showViewReport() {
      getBomFile({ businessId: this.selectedSchema.approvalId, businessType: 'bom_report' }).then(res => {
        this.viewReport.fileList = res.data
        this.viewReport.selectReport = []
        this.viewReport.showReport = true
      }).catch(() => {

      })
    },
    onRendered() {
      console.log('Excel file rendered successfully.')
    },
    onError() {
      console.log('Failed to render Excel file.')
    },
    previewReport() {
      window.open('/previewReport')
      localforage.setItem('viewReport', this.viewReport)
    },
    downloadReport() {
      downloadReport({ fileIds: this.viewReport.selectReport.map(j => j.fileId) }).then(res => {
        this.$download.zip(res, this.selectedSchema.name + '.zip')
      })
    },
    saveSchemePrice(row, column) {
      if (row[column.field] && String(row[column.field]) !== String(this.storeList.find(a => a.id === row.id)[column.field]?.toFixed(2))) {
        updateSchemeMaterialPrice(
          {
            ...row,
            schemeId: this.queryParams.schemeId
          }
        ).then(res => {
          this.getMaterialDetail()
          this.$message.success('操作成功')
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  ::v-deep .el-form-item__content {
    width: calc(100% - 189px);
  }
}

.searchValue {
  width: 100%;
}
</style>
