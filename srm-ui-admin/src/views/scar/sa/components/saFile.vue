<template>
  <div>
    <el-dialog
      v-if="visible"
      :title="seeSaFile? $t('scar.seeFile'):$t('common.uploadFile')"
      :visible.sync="visible"
      append-to-body
      width="400px"
    >
      <el-upload
        ref="upload"
        :action="url"
        :disabled="seeSaFile"
        :drag="!seeSaFile"
        :file-list="fileList"
        :headers="headers"
        :on-preview="onPreview"
        :on-remove="handleRemove"
        :on-success="handleFileSuccess"
        class="upload-container"
        multiple
      >
        <i v-if="!seeSaFile" class="el-icon-upload" />
        <div v-if="!seeSaFile" class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="!seeSaFile"
          type="primary"
          @click="submitFileForm"
        > {{ $t('common.confirm') }}
        </el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import { getAccessToken } from '@/utils/auth'
import { getConfig } from '@/utils/config'

export default {
  name: 'Safile',
  inject: ['app'],
  props: {
    seeSaFile: {
      type: Boolean,
      default: false
    },
    businessValue: {
      type: String
    }
  },

  data() {
    return {
      // 此业务id非真实的附件关联的业务id，存在当前行未保存时上传附件，需要按行维度来维护数据
      rowIndex: '',
      visible: false,
      isUploading: false,
      url: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      data: {},
      fileList: [],
      tempList: [],
      delIds: []
    }
  },
  computed: {
    scarInfo() {
      return this.app.scarInfo
    }
    // fileList() {
    //   const a = this.app.scarInfo.saQuestionDescRelList[this.rowIndex].fileRelList?.filter(a => a.businessValue === this.businessValue)
    //   a?.forEach(b => {
    //     b.name = b?.fileName
    //   })
    //   return a
    // }
  },
  methods: {
    handleFileSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
        this.$refs.upload.clearFiles()
        return
      }
      if (!this.scarInfo.saQuestionDescRelList[this.rowIndex].fileRelList) {
        this.scarInfo.saQuestionDescRelList[this.rowIndex].fileRelList = []
      }
      this.tempList.push({
        name: file.name,
        fileName: file.name,
        fileId: response.data.id,
        scarId: this.scarInfo.id,
        businessValue: this.businessValue,
        tmpBusinessId: this.rowIndex,
        filePath: response.data.url
      })
    },
    submitFileForm() {
      this.fileList = this.fileList.filter(a => !this.delIds.includes(a.fileId))
      this.scarInfo.saQuestionDescRelList[this.rowIndex].fileRelList =
        [...this.scarInfo.saQuestionDescRelList[this.rowIndex].fileRelList.filter(a => a.businessValue !== this.businessValue),
          ...this.fileList, ...this.tempList]
      this.tempList = []
      this.visible = false
    },
    handleRemove(file, fileList) {
      if (file.fileId) {
        this.delIds.push(file.fileId)
      } else {
        const index = this.fileList.findIndex(a => a.fileId === file.response.data.id)
        this.tempList.splice(index, 1)
      }
      this.scarInfo.saQuestionDescRelList[this.rowIndex].fileRelList = this.scarInfo.saQuestionDescRelList[this.rowIndex].fileRelList.filter(a => a.fileId !== file.fileId)
    },
    onPreview(file) {
      window.open(file.filePath)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding-top: 15px;
}
</style>
