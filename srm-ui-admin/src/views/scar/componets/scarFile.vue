<template>
  <div style="display: flex">
    <el-upload
      v-if="!disabled"
      ref="upload"
      style="width: 36px"
      :action="url"
      :disabled="disabled"
      :file-list="fileList"
      :headers="headers"
      :before-upload="beforeUpload"
      :on-preview="onPreview"
      :on-remove="handleRemove"
      :on-success="handleFileSuccess"
      class="upload-container"
      :show-file-list="false"
      multiple
    >
      <el-button
        class="uploadBtn"
        size="small"
        style="padding: 5px 9px"
        :disabled="fileLength===0&&disabled"
        plain
        :icon="!disabled?'el-icon-plus':''"
        :type="fileLength===0&&disabled?'':'primary'"
      >
        <span v-if="disabled">{{ fileLength }}</span>
      </el-button>

    </el-upload>
    <div>
      <span v-if="!disabled" style="margin:0 10px 0 20px">{{ $t('scar.seeFile') }}</span>
      <el-button
        class="uploadBtn"
        size="small"
        style="padding: 5px 9px"
        :disabled="fileLength===0"
        plain
        :type="fileLength===0?'':'primary'"
        @click="showFile"
      >
        <span>{{ fileLength }}</span>
      </el-button>
      <!--    <el-button v-if="!disabled" type="primary" @click="showUpload">{{ $t('common.uploadFile') }}-->
      <!--      <span v-show="fileLength">({{ fileLength }})</span>-->
      <!--    </el-button>-->
      <!--    <el-button v-else type="primary" @click="showFile">{{ $t('scar.viewAttachments') }}-->
      <!--      <span>({{ fileLength }})</span>-->
      <!--    </el-button>-->
      <el-dialog
        v-if="visible"
        :title="disabled? $t('scar.seeFile'): $t('common.uploadFile')"
        :visible.sync="visible"
        append-to-body
        width="500px"
        @close="submitFileForm"
      >
        <el-upload
          ref="upload"
          :action="url"
          :disabled="disabled"
          :file-list="fileList"
          :headers="headers"

          :on-preview="onPreview"
          :on-remove="handleRemove"
          :on-success="handleFileSuccess"
          class="upload-container"
          multiple
        >
          <!--        <i v-if="!disabled" class="el-icon-upload" />-->
          <!--        <div v-if="!disabled" class="el-upload__text">{{ $t('common.clickOrDragTheFileHereToUpload') }}</div>-->
        </el-upload>
        <div slot="footer" class="dialog-footer">
          <!--                  <el-button-->
          <!--                    v-if="!disabled"-->
          <!--                    type="primary"-->
          <!--                    @click="submitFileForm"-->
          <!--                  > {{ $t('common.confirm') }}-->
          <!--                  </el-button>-->
        </div>
      </el-dialog>
    </div>

  </div>
</template>

<script>
import { getAccessToken } from '@/utils/auth'
import { getConfig } from '@/utils/config'

export default {
  name: 'Scarfile',
  inject: ['app'],
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    businessValue: {
      type: String

    }
  },

  data() {
    return {
      visible: null,
      isUploading: false,
      url: getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API) + '/admin-api/infra/file/upload',
      headers: { Authorization: 'Bearer ' + getAccessToken() },
      data: {},
      fileList: [],
      // 一次批量上传的临时文件集合
      tempList: [],
      delIds: []
    }
  },
  computed: {
    scarInfo() {
      return this.app.scarInfo
    },
    fileLength() {
      return this.app.scarInfo.fileRelList.filter(a => a.businessValue === this.businessValue).length
    }
  },
  methods: {
    handleFileSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
        this.$refs.upload.clearFiles()
        return
      }
      this.tempList.push({
        name: file.name,
        fileName: file.name,
        fileId: response.data.id,
        scarId: this.scarInfo.id,
        businessValue: this.businessValue,
        filePath: response.data.url
      })
      if (fileList.some(a => a.status === 'ready' || a.status === 'uploading')) {
        return true
      }
      // 全部上传完成后，提交表单
      this.submitFileForm()
    },
    submitFileForm() {
      const temp = this.app.scarInfo.fileRelList.filter(a => a.businessValue === this.businessValue && !this.delIds.includes(a.fileId))
      temp.forEach(b => {
        b.name = b?.fileName
      })
      this.fileList = [...this.tempList, ...temp]
      this.scarInfo.fileRelList = [...this.scarInfo.fileRelList.filter(a => a.businessValue !== this.businessValue), ...this.fileList]
      // 一次批量上传的临时文件集合需要在此处置空
      this.tempList = []
      this.visible = false
    },
    handleRemove(file, fileList) {
      if (file.fileId) {
        this.delIds.push(file.fileId)
      } else {
        const index = this.fileList.findIndex(a => a.fileId === file.response.data.id)
        this.tempList.splice(index, 1)
      }
    },
    showUpload() {
      if (!this.app.scarInfo.id && !this.disabled) {
        this.$message.error(this.$t('scar.pleaseSaveTheCurrentFillingContentFirst'))
      } else {
        this.showFile()
      }
    },
    onPreview(file) {
      window.open(file.filePath)
    },
    showFile() {
      this.visible = true
      const a = this.app.scarInfo.fileRelList.filter(a => a.businessValue === this.businessValue)
      a.forEach(b => {
        b.name = b?.fileName
      })
      this.fileList = a
    },
    beforeUpload() {
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding-top: 15px;
}
</style>
