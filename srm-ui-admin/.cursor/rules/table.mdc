---
description: 用于生成通用列表的规则
globs:
---
# 列表页面规范

## 1. 页面布局结构

### 1.1 基础布局
```vue
<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <!-- 数据列表 -->
    <!-- 分页组件 -->
    <!-- 弹窗组件 -->
  </div>
</template>
```

### 1.2 搜索区域
- 包含快速搜索栏和高级搜索两部分
- 快速搜索栏固定显示，包含主要搜索条件
- 高级搜索可折叠，包含更多筛选条件
- 搜索区域统一使用 `el-form` 组件
- 搜索表单项使用统一的 `searchItem` 和 `searchValue` 样式类

```vue
<!-- 快速搜索栏 -->
<div class="search-bar">
  <el-input
    v-model="queryParams.search"
    clearable
    style="flex: 0 1 40%"
    @keyup.enter.native="handleQuery"
  />
  <el-button type="primary" @click="handleQuery">搜索</el-button>
  <el-button @click="resetQuery">重置</el-button>
  <div class="advanced-search-toggle" @click="showSearch = !showSearch">
    高级搜索 <i class="el-icon-arrow-up" />
  </div>
</div>

<!-- 高级搜索表单 -->
<el-form
  v-show="showSearch"
  ref="queryForm"
  :model="queryParams"
  :inline="true"
  label-width="135px">
  <el-form-item label="示例字段" class="searchItem" prop="field">
    <el-input
      v-model="queryParams.field"
      placeholder="请输入"
      class="searchValue"
      clearable
    />
  </el-form-item>
  <el-form-item label="示例选择" class="searchItem" prop="select">
    <el-select
      v-model="queryParams.select"
      placeholder="请选择"
      class="searchValue"
      clearable>
      <el-option label="选项1" value="1" />
    </el-select>
  </el-form-item>
</el-form>
```

### 1.3 工具栏
- 位于列表上方，包含功能按钮
- 常见按钮：新建、导入、导出、批量操作等
- 使用权限控制按钮显示

```vue
<el-row :gutter="10" class="mb8">
  <el-col :span="1.5">
    <el-button
      v-hasPermi="['xxx:create']"
      type="primary"
      icon="el-icon-plus"
      @click="handleAdd">
      新建
    </el-button>
  </el-col>
  <!-- 其他功能按钮 -->
</el-row>
```

### 1.4 数据列表
- 使用 vxe-grid 或 el-table 组件
- 支持排序、筛选、自定义列等功能
- 操作列固定在左侧

```vue
<vxe-grid
  :data="list"
  :loading="loading"
  v-bind="gridOption">
  <!-- 自定义列模板 -->
  <template #operate="{row}">
    <OperateDropDown :menu-item="getOperateMenus(row)" />
  </template>
</vxe-grid>
```

## 2. 数据管理

### 2.1 基础数据结构
```js
data() {
  return {
    // 加载状态
    loading: false,
    // 列表数据
    list: [],
    // 总条数
    total: 0,
    // 查询参数
    queryParams: {
      pageNo: 1,
      pageSize: 10,
      search: '',
      // 其他查询条件
    },
    // 是否显示高级搜索
    showSearch: false,
  }
}
```

### 2.2 表格配置
```js
gridOption: {
  align: 'left',
  border: true,
  columnConfig: {
    resizable: true
  },
  showOverflow: true,
  rowConfig: {
    isHover: true
  },
  filterConfig: {
    remote: true
  },
  columns: [
    // 操作列（固定在左侧）
    {
      field: 'operate',
      title: this.$t('common.operate'),
      fixed: 'left',
      width: 120,
      slots: { default: 'operate' }
    },

    // 1. 普通字段示例
    {
      field: 'id',
      title: this.$t('common.id'),
      visible: true,
      width: 100
    },
    {
      field: 'name',
      title: this.$t('common.name'),
      visible: true,
      width: 150,
      sortable: true // 支持排序
    },
    {
      field: 'createTime',
      title: this.$t('common.createTime'),
      visible: true,
      width: 180,
      sortable: true,
      formatter: ({ cellValue }) => parseTime(cellValue) // 时间格式化
    },

    // 2. 字典字段示例
    {
      field: 'status',
      title: this.$t('common.status'),
      visible: true,
      width: 100,
      params: { dictType: DICT_TYPE.COMMON_STATUS }, // 字典类型
      slots: { default: 'dict' } // 使用字典插槽
    }
  ]
}
```

### 2.3 字段定义规范

#### 2.3.1 普通字段
```js
{
  field: 'fieldName',           // 字段名称
  title: this.$t('fieldName'), // 标题
  visible: true,                // 是否默认显示
  width: 100,                   // 列宽度
  sortable: true,               // 是否支持排序（可选）
  fixed: 'left'|'right',        // 是否固定列（可选）
  formatter: ({ cellValue }) => {}, // 格式化函数（可选）
  showOverflow: true,           // 内容超出是否显示tooltip（可选）
  align: 'left'|'center'|'right' // 对齐方式（可选）
}
```

#### 2.3.2 字典字段
```js
{
  field: 'dictField',           // 字段名称
  title: this.$t('dict.field'), // 国际化标题
  visible: true,                // 是否默认显示
  width: 100,                   // 列宽度
  params: {                     // 字典参数
    dictType: DICT_TYPE.XXX     // 字典类型
  },
  slots: {                      // 使用字典插槽
    default: 'dict'
  },
  filters: getDictDatas(DICT_TYPE.XXX), // 字典过滤项（可选）
  sortable: true,               // 是否支持排序（可选）
  fixed: 'left'|'right'         // 是否固定列（可选）
}
```

#### 2.3.3 特殊字段处理
1. 时间日期字段
```js
{
  field: 'datetime',
  title: this.$t('common.datetime'),
  visible: true,
  width: 180,
  sortable: true,
  formatter: ({ cellValue }) => parseTime(cellValue, '{y}-{m}-{d} {h}:{i}:{s}')
}
```

## 3. 方法规范

### 3.1 基础方法
```js
methods: {
  // 获取列表数据
  getList() {
    this.loading = true
    listAPI(this.queryParams).then(response => {
      this.list = response.data.list
      this.total = response.data.total
      this.loading = false
    })
  },

  // 搜索
  handleQuery() {
    this.queryParams.pageNo = 1
    this.getList()
  },

  // 重置
  resetQuery() {
    this.resetForm('queryForm')
    this.handleQuery()
  },

  // 排序
  sortMethod({ order, property }) {
    this.queryParams.sortBy = order
    this.queryParams.sortField = property
    this.getList()
  },

  // 过滤
  filterMethod({ column, values }) {
    this.queryParams[column.field] = values
    this.getList()
  }
}
```

### 3.2 操作方法
```js
methods: {
  // 新增
  handleAdd() {
    // 跳转到新增页面或打开新增弹窗
  },

  // 编辑
  handleUpdate(row) {
    // 跳转到编辑页面或打开编辑弹窗
  },

  // 删除
  handleDelete(row) {
    this.$modal.confirm('确认删除?').then(() => {
      return deleteAPI(row.id)
    }).then(() => {
      this.getList()
      this.$modal.msgSuccess('删除成功')
    })
  },

  // 导出
  handleExport() {
    this.exportLoading = true
    exportAPI(this.queryParams).then(response => {
      this.$download.excel(response, '导出数据.xlsx')
      this.exportLoading = false
    })
  }
}
```

## 4. 样式规范

### 4.1 基础样式
```scss

.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}

.advanced-search-toggle {
  margin-left: 30px;
  cursor: pointer;
  display: flex;
  align-items: center;

  i {
    margin-left: 10px;
    font-size: 18px;
    transition: transform 0.3s;
    font-weight: 300;
    &.is-active {
      transform: rotate(180deg);
    }
  }
}

// 搜索表单项样式
.searchItem {
  width: 33%;
  margin-right: 0;
  margin-bottom: 14px;

  :deep(.el-form-item__content) {
    width: calc(100% - 135px);
  }
}

.searchValue {
  width: 95%;
}
```

## 5. 最佳实践

### 5.1 权限控制
- 使用 v-hasPermi 指令控制按钮权限
- 操作按钮权限统一管理

### 5.2 国际化
- 所有文本使用 i18n
- 使用 $t() 函数获取翻译文本

### 5.3 组件复用
- 将通用组件抽离（如操作下拉菜单）
- 使用插槽实现自定义内容

### 5.4 性能优化
- 合理使用 v-show 和 v-if
- 大数据列表考虑虚拟滚动
- 避免不必要的数据请求

### 5.5 错误处理
- 统一的错误提示
- 加载状态管理
- 导出等耗时操作的loading控制
