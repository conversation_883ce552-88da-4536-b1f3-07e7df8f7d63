{"name": "srm-ui-admin", "version": "1.6.2-snapshot", "description": "SRM 2.0", "author": "芋道", "license": "MIT", "scripts": {"local": "vue-cli-service serve --mode local", "dev": "vue-cli-service serve --mode dev", "build:getein": "vue-cli-service build --mode getein", "build:hanza": "vue-cli-service build --mode hanza", "build:stage": "vue-cli-service build --mode stage", "build:prod": "vue-cli-service build --mode prod", "build:op": "vue-cli-service build --mode op", "build:dev": "vue-cli-service build --mode dev", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "clean": "rimraf node_modules"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://github.com/YunaiV/ruoyi-vue-pro"}, "dependencies": {"@babel/parser": "7.7.4", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^2.0.10", "@riophae/vue-treeselect": "0.4.0", "@superset-ui/embedded-sdk": "^0.1.0-alpha.9", "@vue-office/docx": "^1.6.2", "@vue-office/excel": "^1.7.11", "@vue-office/pdf": "^2.0.2", "@wocwin/t-ui": "^1.3.0", "axios": "0.24.0", "bpmn-js-token-simulation": "0.10.0", "clipboard": "2.0.8", "core-js": "^3.27.1", "dayjs": "^1.11.7", "echarts": "5.4.2", "element-ui": "^2.15.13", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "localforage": "^1.10.0", "min-dash": "3.5.2", "mitt": "^3.0.0", "node-polyfill-webpack-plugin": "^2.0.0", "nprogress": "0.2.0", "pinyin": "^3.0.0-alpha.4", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "^1.15.0", "throttle-debounce": "2.1.0", "v-viewer": "^1.7.4", "viewerjs": "^1.11.6", "vue": "2.7.14", "vue-beautiful-chat": "^2.5.0", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-demi": "^0.14.6", "vue-ellipsis-component": "^1.1.0", "vue-i18n": "8.27.1", "vue-i18n-composable": "^2.0.0", "vue-markdown": "^2.2.4", "vue-meta": "^2.4.0", "vue-router": "3.4.9", "vue-tour": "^2.0.0", "vue2-verify": "^1.1.5", "vuedraggable": "2.24.3", "vuex": "3.6.0", "vuex-persistedstate": "^4.1.0", "vxe-table": "3.7.10", "xe-utils": "^3.5.4", "xlsx-js-style": "^1.2.0", "xml-js": "1.6.11"}, "devDependencies": {"@babel/plugin-syntax-dynamic-import": "^7.8.3", "@vue/cli-plugin-babel": "5.0.8", "@vue/cli-plugin-eslint": "5.0.8", "@vue/cli-service": "5.0.8", "@vue/compiler-sfc": "^3.0.1", "@vue/eslint-config-prettier": "^5.0.0", "babel-eslint": "10.1.0", "bpmn-js": "8.9.0", "bpmn-js-properties-panel": "0.46.0", "caniuse-lite": "1.0.30001570", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-vue": "^9.1.1", "fs-extra": "^8.1.0", "html-webpack-plugin": "5.5.3", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.53.0", "sass-loader": "13.0.2", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "terser-webpack-plugin": "^4.2.3", "vue2-ace-editor": "^0.0.15", "webpack-bundle-analyzer": "^3.9.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}