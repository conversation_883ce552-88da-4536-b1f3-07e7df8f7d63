const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 查找所有剩余需要修改的文件
function findRemainingFiles() {
  try {
    const command = `Get-ChildItem -Path "src" -Recurse -Include "*.js","*.vue","*.ts" | Select-String -Pattern "process\\.env\\.VUE_APP_BASE_API(?!\\s*\\|\\|)" | Where-Object { $_.Line -notmatch "getConfig\\(" -and $_.Line -notmatch "^\\s*//" -and $_.Line -notmatch "^\\s*\\*" } | Select-Object -ExpandProperty Path | Get-Unique`;
    const result = execSync(command, { encoding: 'utf8', shell: 'powershell' });
    
    return result.trim().split('\n')
      .filter(line => line.trim())
      .map(line => line.trim().replace(/^.*srm-ui-admin[\\\/]/, '').replace(/\\/g, '/'));
  } catch (error) {
    console.error('查找文件失败:', error.message);
    return [];
  }
}

function fixFile(filePath) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`文件不存在: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(fullPath, 'utf8');
  let modified = false;

  // 替换直接使用 process.env.VUE_APP_BASE_API 的地方（排除注释行和已经使用getConfig的行）
  const lines = content.split('\n');
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 跳过注释行和已经使用getConfig的行
    if (line.trim().startsWith('//') || 
        line.trim().startsWith('*') || 
        line.includes('getConfig(') ||
        line.includes('// ')) {
      continue;
    }
    
    // 替换直接使用的 process.env.VUE_APP_BASE_API
    if (line.includes('process.env.VUE_APP_BASE_API')) {
      const newLine = line.replace(/process\.env\.VUE_APP_BASE_API/g, "getConfig('VUE_APP_BASE_API', process.env.VUE_APP_BASE_API)");
      if (newLine !== line) {
        lines[i] = newLine;
        modified = true;
        console.log(`  修改第${i+1}行: ${line.trim()}`);
      }
    }
  }
  
  if (modified) {
    content = lines.join('\n');
  }

  // 检查是否已经导入了 getConfig
  const hasGetConfigImport = /import.*getConfig.*from.*@\/utils\/config/.test(content);
  
  if (modified && !hasGetConfigImport) {
    // 查找合适的位置添加导入
    const scriptMatch = content.match(/<script[^>]*>/);
    if (scriptMatch) {
      const scriptIndex = scriptMatch.index + scriptMatch[0].length;
      const beforeScript = content.substring(0, scriptIndex);
      const afterScript = content.substring(scriptIndex);
      
      // 查找第一个 import 语句的位置
      const firstImportMatch = afterScript.match(/\nimport\s+/);
      if (firstImportMatch) {
        const importIndex = scriptIndex + firstImportMatch.index;
        const beforeImport = content.substring(0, importIndex);
        const afterImport = content.substring(importIndex);
        
        content = beforeImport + "\nimport { getConfig } from '@/utils/config'" + afterImport;
        console.log(`  添加导入语句`);
      } else {
        // 如果没有找到 import 语句，在 script 标签后添加
        content = beforeScript + "\nimport { getConfig } from '@/utils/config'\n" + afterScript;
        console.log(`  在script标签后添加导入语句`);
      }
    }
  }

  if (modified) {
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`✅ 已修改: ${filePath}`);
    return true;
  } else {
    console.log(`⏭️  无需修改: ${filePath}`);
    return false;
  }
}

console.log('🔍 查找剩余需要修改的文件...\n');

const remainingFiles = findRemainingFiles();
console.log(`找到 ${remainingFiles.length} 个需要修改的文件\n`);

if (remainingFiles.length === 0) {
  console.log('🎉 所有文件都已经修改完成！');
  process.exit(0);
}

let successCount = 0;
let totalCount = remainingFiles.length;

remainingFiles.forEach(filePath => {
  try {
    console.log(`\n📝 处理文件: ${filePath}`);
    if (fixFile(filePath)) {
      successCount++;
    }
  } catch (error) {
    console.error(`❌ 修改失败 ${filePath}:`, error.message);
  }
});

console.log(`\n🎯 批量修改完成！成功修改 ${successCount}/${totalCount} 个文件`);

// 再次检查是否还有剩余文件
const finalCheck = findRemainingFiles();
if (finalCheck.length === 0) {
  console.log('🎉 所有文件修改完成！项目中的 process.env.VUE_APP_BASE_API 都已经改为使用 getConfig 包装。');
} else {
  console.log(`⚠️  还有 ${finalCheck.length} 个文件需要手动检查。`);
}
